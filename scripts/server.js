import { select } from '@inquirer/prompts'
import * as fs from 'fs'
import { execSync } from 'child_process'
import { fileURLToPath } from 'url'
import * as path from 'path'
const __dirname = path.dirname(fileURLToPath(import.meta.url))
const answer = await select({
    message: '请选择需要启动的项目?',
    choices: [
        {
            name: '社会图书馆-FRID',
            value: 'shRFID',
        },
        {
            name: '社会图书馆-馆务系统',
            value: 'shLibrary',
        },
        {
            name: '图书馆馆务系统',
            value: 'yd-library',
        },
        {
            name: '一德权限系统',
            value: 'yd-permission',
        },
        {
            name: 'RFID图书管理系统',
            value: 'yd-rfid',
        },
        {
            name: '智慧宿舍系统',
            value: 'yd-dormitory',
        },
        {
            name: '安防系统',
            value: 'yd-security',
        },
        {
            name: '评价系统',
            value: 'yd-evaluation',
        },
        {
            name: '校易付系统',
            value: 'yd-xyf',
        },
        {
            name: '缴费系统',
            value: 'yd-paySystem',
        },
    ],
})

let itemCatalogue = answer

if (answer == 'shRFID') {
    itemCatalogue = 'yd-rfid'
}

if (answer == 'shLibrary') {
    itemCatalogue = 'yd-library'
}

const files = fs.readdirSync(path.resolve(__dirname, `../src/pages/${itemCatalogue}`))
const start = () => {
    const env = fs.readFileSync(path.resolve(__dirname, '../env/development'), 'utf8')
    fs.writeFileSync(path.resolve(__dirname, '../.env.development'), env.replace('{{}}', answer))
    execSync('npm run dev', { stdio: 'inherit' })
}
if (files.length > 0) {
    start()
} else {
    // git submodule add http://git.yyide.com/framework/yd-evaluation.git src/pages/yd-evaluation 安装子模块
    execSync('git submodule init', { stdio: 'inherit' })
    execSync(`git submodule update --remote src/pages/${itemCatalogue}`, {
        stdio: 'inherit',
    })
    start()
}
