import * as fs from 'fs'
import { execSync } from 'child_process'
import { fileURLToPath } from 'url'
import * as path from 'path'
const __dirname = path.dirname(fileURLToPath(import.meta.url))
let code = process.argv[2]
const env = process.argv[3]
const _env = fs.readFileSync(path.resolve(__dirname, `../env/${env}`), 'utf8')

if (code == 'shRFID') {
    code = 'yd-rfid'
}
if (code == 'shLibrary') {
    code = 'yd-library'
}

fs.writeFileSync(path.resolve(__dirname, `../.env.${env}`), _env.replace('{{}}', process.argv[2]))

try {
    const data = fs.readFileSync(path.resolve(__dirname, '../.gitmodules'), 'utf8')
    let str = data.toString()
    const isExist = /\/\/root/g
    if (!isExist.test(str)) {
        str = str.replace(/\/\//g, '//root:ScrtU8Q9kjMNyDrsj3hj@')
        fs.writeFileSync(path.resolve(__dirname, '../.gitmodules'), str)
        console.log('.gitmodules文件URL已替换: 开始同步到子项目')
        execSync(`git submodule sync -- src/pages/${code}`, { stdio: 'inherit' })
        execSync(`git submodule update --init --recursive -- src/pages/${code}`, {
            stdio: 'inherit',
        })
    } else {
        console.log('子项目URL已存在账号密码，不处理。')
    }
} catch (err) {
    console.error('替换子项目URL文件出错:', err)
}
execSync('git submodule init', { stdio: 'inherit' })
// execSync(`git submodule update --remote src/pages/${code}`, {
//     stdio: 'inherit',
// })
execSync(`git submodule foreach git checkout ${env}`, { stdio: 'inherit' })
execSync(`npm install --legacy-peer-deps`, { stdio: 'inherit' })
execSync(`npm run build:${env}`, { stdio: 'inherit' })
// node ./scripts/build yd-library uat uatrelease
