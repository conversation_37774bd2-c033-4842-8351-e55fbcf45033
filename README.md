# YD-Admin 微前端管理系统架构文档

## 项目概述

YD-Admin 是一个基于 Vue 3 + Vite 的微前端管理系统，采用 Git 子模块机制管理多个独立的业务子系统。该项目通过统一的主框架集成多个子项目，实现了模块化开发和部署。

## 技术栈

- **前端框架**: Vue 3.3.12 (Composition API)
- **构建工具**: Vite 4.4.5
- **UI 组件库**: Ant Design Vue 4.0.8
- **状态管理**: Pinia 2.1.6
- **路由管理**: Vue Router 4
- **CSS 框架**: UnoCSS 0.55.0
- **包管理器**: pnpm
- **代码规范**: Prettier + <PERSON><PERSON> + lint-staged

## 项目架构设计

### 1. 微前端架构

项目采用基于 Git 子模块的微前端架构，主要特点：

- **主应用框架**: 提供统一的登录、权限、布局、路由等基础功能
- **子模块系统**: 每个业务系统作为独立的 Git 子模块存在
- **动态加载**: 根据配置动态加载指定的子模块
- **统一构建**: 通过脚本统一管理子模块的拉取、构建和部署

### 2. 子模块列表

当前项目包含以下子模块：

| 子模块名称 | 仓库地址 | 业务描述 | 分支 |
|-----------|---------|---------|------|
| yd-rfid | http://git.yyide.com/framework/yd-rfid.git | RFID图书管理系统 | main |
| yd-library | http://git.yyide.com/framework/yd-library.git | 图书馆馆务系统 | main |
| yd-dormitory | http://git.yyide.com/framework/yd-dormitory.git | 智慧宿舍系统 | main |
| yd-evaluation | http://git.yyide.com/framework/yd-evaluation.git | 评价系统 | main |
| yd-permission | http://git.yyide.com/framework/yd-permission.git | 权限管理系统 | main |
| yd-xyf | http://git.yyide.com/framework/yd-xyf.git | 校易付系统 | main |
| yd-paySystem | http://git.yyide.com/framework/yd-paysystem.git | 缴费系统 | main |
| yd-security | http://git.yyide.com/framework/yd-security.git | 安防系统 | main |

### 3. 目录结构

```
yd-admin/
├── .gitmodules                 # Git子模块配置文件
├── package.json               # 项目依赖配置
├── vite.config.js            # Vite构建配置
├── unocss.config.js          # UnoCSS配置
├── env/                      # 环境配置文件夹
│   ├── dev                   # 开发环境配置
│   ├── uat                   # 测试环境配置
│   ├── main                  # 生产环境配置
│   └── ...
├── scripts/                  # 构建脚本
│   ├── server.js            # 开发服务器启动脚本
│   └── build.js             # 生产构建脚本
├── public/                   # 静态资源（按子模块分类）
│   ├── yd-library/
│   ├── yd-rfid/
│   └── ...
├── src/
│   ├── main.js              # 应用入口文件
│   ├── App.vue              # 根组件
│   ├── yConfig.js           # 子模块配置
│   ├── components/          # 公共组件
│   ├── router/              # 路由配置
│   ├── store/               # 状态管理
│   ├── utils/               # 工具函数
│   └── pages/               # 子模块页面目录
│       ├── yd-library/      # 图书馆系统子模块
│       ├── yd-rfid/         # RFID系统子模块
│       └── ...
└── ...
```

## 核心机制详解

### 1. Git 子模块管理机制

#### .gitmodules 配置
```ini
[submodule "src/pages/yd-library"]
    path = src/pages/yd-library
    url = http://git.yyide.com/framework/yd-library.git
    ignore = all
    branch = main
    active = false
    fetchRecurseSubmodules = no
```

**配置说明**:
- `path`: 子模块在主项目中的路径
- `url`: 子模块的 Git 仓库地址
- `ignore = all`: 忽略子模块的所有变更
- `branch = main`: 指定拉取的分支
- `active = false`: 默认不激活（按需激活）
- `fetchRecurseSubmodules = no`: 不递归拉取子模块

### 2. 动态环境配置

#### 环境变量模板机制
环境配置文件使用 `{{}}` 作为占位符，在构建时动态替换为具体的子模块代码：

```bash
# env/dev 文件内容
VITE_BASE_API = http://*************:9528
VITE_BASE_CODE = {{}}  # 动态替换为子模块代码
```

#### Vite 配置动态适配
```javascript
// vite.config.js 核心逻辑
export default ({ mode }) => {
    const env = loadEnv(mode, process.cwd())
    let dir = env.VITE_BASE_CODE
    
    // 特殊映射处理
    if (dir == 'shLibrary') dir = 'yd-library'
    if (dir == 'shRFID') dir = 'yd-rfid'
    
    return {
        base: './',
        publicDir: `./public/${dir}`,  // 动态指定静态资源目录
        // ... 其他配置
    }
}
```

### 3. 路由动态加载机制

#### 模块自动发现
```javascript
// src/router/getRoute.js
const modules = import.meta.glob('../pages/**/**/index.vue', { import: 'default' })
```

使用 Vite 的 `import.meta.glob` 自动发现所有子模块的入口组件。

#### 权限路由生成
系统通过后端接口 `/system/menu/getRouters` 获取用户权限路由，然后与本地模块进行匹配：

```javascript
const setRouter = arr => {
    let list = []
    arr?.forEach(i => {
        let item = {
            path: i.path,
            name: i.component,
            meta: { title: i.name, icon: i.icon },
            isVisible: i.isVisible,
        }
        if (i.filePath) {
            item.component = modules[i.filePath]  // 动态组件加载
        }
        // ...
    })
    return list
}
```

## 开发流程

### 1. 环境准备

```bash
# 克隆主项目
git clone http://git.yyide.com/framework/yd-admin.git
cd yd-admin

# 安装依赖
pnpm install
```

### 2. 开发模式启动

```bash
# 启动开发服务器（交互式选择子模块）
npm run server
```

**启动流程**:
1. 脚本会显示可选的子模块列表
2. 选择需要开发的子模块
3. 自动检查子模块是否存在
4. 如果不存在，自动执行子模块初始化和拉取
5. 生成对应的环境配置文件
6. 启动 Vite 开发服务器

#### server.js 核心逻辑
```javascript
// 检查子模块目录是否存在
const files = fs.readdirSync(path.resolve(__dirname, `../src/pages/${itemCatalogue}`))

if (files.length > 0) {
    start()  // 直接启动
} else {
    // 初始化并拉取子模块
    execSync('git submodule init', { stdio: 'inherit' })
    execSync(`git submodule update --remote src/pages/${itemCatalogue}`, {
        stdio: 'inherit',
    })
    start()
}
```

### 3. 生产构建

```bash
# 构建命令格式
node ./scripts/build [子模块代码] [环境] [可选参数]

# 示例
node ./scripts/build yd-library uat
node ./scripts/build yd-rfid main
```

**构建流程**:
1. 读取指定环境的配置文件
2. 替换环境变量中的子模块占位符
3. 处理 .gitmodules 文件中的认证信息
4. 同步并拉取指定子模块
5. 切换到指定环境分支
6. 安装依赖
7. 执行构建命令

#### build.js 核心逻辑
```javascript
// 动态替换环境配置
const _env = fs.readFileSync(path.resolve(__dirname, `../env/${env}`), 'utf8')
fs.writeFileSync(path.resolve(__dirname, `../.env.${env}`), _env.replace('{{}}', process.argv[2]))

// 处理Git认证
const data = fs.readFileSync(path.resolve(__dirname, '../.gitmodules'), 'utf8')
let str = data.toString()
const isExist = /\/\/root/g
if (!isExist.test(str)) {
    str = str.replace(/\/\//g, '//root:ScrtU8Q9kjMNyDrsj3hj@')
    fs.writeFileSync(path.resolve(__dirname, '../.gitmodules'), str)
}

// 子模块操作
execSync('git submodule init', { stdio: 'inherit' })
execSync(`git submodule foreach git checkout ${env}`, { stdio: 'inherit' })
execSync(`npm install --legacy-peer-deps`, { stdio: 'inherit' })
execSync(`npm run build:${env}`, { stdio: 'inherit' })
```

## 部署配置

### 1. 环境配置

项目支持多环境部署，每个环境都有独立的配置文件：

- `env/dev`: 开发环境
- `env/uat`: 测试环境  
- `env/uatrelease`: 预发布环境
- `env/main`: 生产环境

### 2. 构建脚本

package.json 中定义了不同环境的构建命令：

```json
{
  "scripts": {
    "server": "node ./scripts/server",
    "dev": "vite",
    "build:dev": "vite build --mode dev",
    "build:uat": "vite build --mode uat", 
    "build:uatrelease": "vite build --mode uatrelease",
    "build:main": "vite build --mode main"
  }
}
```

## 设计思路总结

### 1. 架构优势

**模块化开发**:
- 每个业务系统独立开发、测试、部署
- 团队可以并行开发不同的子系统
- 降低系统复杂度和耦合度

**统一管理**:
- 统一的用户认证和权限管理
- 统一的UI风格和交互规范
- 统一的构建和部署流程

**灵活部署**:
- 可以选择性部署特定的子系统
- 支持多环境独立配置
- 便于系统的扩展和维护

### 2. 技术选型理由

**Vue 3 + Composition API**:
- 更好的TypeScript支持
- 更灵活的逻辑复用
- 更好的性能表现

**Vite构建工具**:
- 极快的冷启动速度
- 原生ES模块支持
- 丰富的插件生态

**Git子模块**:
- 版本控制粒度细化
- 独立的开发周期
- 便于团队协作

**Pinia状态管理**:
- Vue 3官方推荐
- 更简洁的API设计
- 更好的TypeScript支持

### 3. 注意事项

**子模块管理**:
- 需要注意子模块的分支同步
- 构建时需要确保子模块代码是最新的
- 子模块的依赖版本需要与主项目兼容

**环境配置**:
- 不同环境的API地址需要正确配置
- 子模块代码标识需要与实际目录结构匹配
- 静态资源路径需要按子模块正确组织

**权限控制**:
- 路由权限通过后端接口动态获取
- 按钮权限通过v-auth指令控制
- 不同子系统可能有不同的权限体系

这个架构设计实现了微前端的核心理念，既保持了各个子系统的独立性，又通过统一的主框架提供了一致的用户体验和开发体验。

## 详细操作指南

### 1. 子模块代码拉取详细流程

#### 手动拉取子模块
```bash
# 初始化所有子模块配置
git submodule init

# 拉取指定子模块
git submodule update --init --recursive src/pages/yd-library

# 拉取所有子模块
git submodule update --init --recursive

# 更新子模块到最新版本
git submodule update --remote src/pages/yd-library

# 切换子模块分支
cd src/pages/yd-library
git checkout main
cd ../../..

# 同步子模块URL配置
git submodule sync
```

#### 自动化拉取（推荐）
使用项目提供的脚本自动处理：

**开发环境**:
```bash
npm run server
# 选择需要的子模块，脚本会自动检查并拉取
```

**生产环境**:
```bash
node ./scripts/build yd-library uat
# 脚本会自动拉取并构建指定子模块
```

### 2. 开发环境详细配置

#### 启动开发服务器
```bash
# 方式1：交互式启动（推荐）
npm run server

# 方式2：直接启动Vite（需要手动配置环境）
npm run dev
```

#### 交互式启动流程详解
1. **选择子模块**: 从列表中选择要开发的子模块
2. **环境检查**: 检查子模块目录是否存在
3. **自动拉取**: 如果不存在，自动执行 `git submodule init` 和 `git submodule update`
4. **配置生成**: 读取 `env/development` 模板，替换 `{{}}` 为选择的子模块代码
5. **服务启动**: 启动 Vite 开发服务器，端口 9008

#### 环境变量配置
开发时会自动生成 `.env.development` 文件：
```bash
# 示例：选择 yd-library 后生成的配置
VITE_BASE_API = http://*************:9528
VITE_BASE_CODE = yd-library
VITE_BASE_SCREEN = http://*************:8100
# ... 其他配置
```

### 3. 生产构建详细流程

#### 构建命令详解
```bash
# 基本语法
node ./scripts/build [子模块代码] [环境名称]

# 实际示例
node ./scripts/build yd-library uat      # 构建图书馆系统测试版
node ./scripts/build yd-rfid main        # 构建RFID系统生产版
node ./scripts/build shLibrary dev       # 构建社会图书馆开发版
```

#### 构建过程详细步骤
1. **参数解析**: 解析命令行参数获取子模块代码和环境
2. **映射处理**: 处理特殊映射（shRFID → yd-rfid, shLibrary → yd-library）
3. **环境配置**: 读取 `env/[环境]` 文件，替换占位符生成 `.env.[环境]`
4. **认证处理**: 检查并添加Git仓库认证信息到 .gitmodules
5. **子模块同步**: 执行 `git submodule sync` 同步配置
6. **子模块拉取**: 执行 `git submodule update --init --recursive`
7. **分支切换**: 所有子模块切换到指定环境分支
8. **依赖安装**: 执行 `npm install --legacy-peer-deps`
9. **项目构建**: 执行 `npm run build:[环境]`

#### 构建输出
构建完成后，产物会输出到 `dist` 目录，包含：
- 静态资源文件
- 编译后的JavaScript和CSS
- 对应子模块的静态资源

### 4. 子模块开发规范

#### 子模块目录结构要求
每个子模块必须包含以下文件：
```
src/pages/[子模块名]/
├── index.vue           # 子模块入口组件（必需）
├── router/             # 子模块路由配置
├── components/         # 子模块组件
├── api/               # 子模块API接口
├── store/             # 子模块状态管理
└── assets/            # 子模块静态资源
```

#### 子模块入口组件规范
```vue
<!-- src/pages/yd-library/index.vue 示例 -->
<template>
  <div class="library-container">
    <router-view />
  </div>
</template>

<script setup name="YdLibrary">
// 子模块初始化逻辑
// 注意：name属性用于路由组件识别
</script>
```

#### 子模块路由集成
子模块的路由会通过主应用的动态路由机制自动集成：
```javascript
// 主应用会扫描所有 ../pages/**/**/index.vue 文件
const modules = import.meta.glob('../pages/**/**/index.vue', { import: 'default' })
```

### 5. 环境管理详解

#### 环境配置文件说明
- `env/dev`: 本地开发环境，连接开发服务器
- `env/development`: 开发环境模板，用于 `npm run server`
- `env/uat`: 测试环境，连接测试服务器
- `env/uatrelease`: 预发布环境，连接预发布服务器
- `env/main`: 生产环境，连接生产服务器

#### 配置文件格式
```bash
# 环境配置文件示例
VITE_BASE_API = http://api.example.com          # 主API地址
VITE_BASE_CODE = {{}}                           # 子模块代码占位符
VITE_BASE_SCREEN = http://screen.example.com    # 大屏API地址
VITE_BASE_API_CLOUD = http://cloud.example.com  # 云平台API地址
VITE_BASE_API_PERMISSION = http://perm.example.com # 权限API地址
```

#### 多环境部署策略
1. **开发环境**: 用于日常开发和调试
2. **测试环境**: 用于功能测试和集成测试
3. **预发布环境**: 用于上线前的最终验证
4. **生产环境**: 正式对外提供服务

### 6. 常见问题与解决方案

#### 子模块拉取失败
```bash
# 问题：子模块拉取失败，提示认证错误
# 解决：检查网络连接和Git认证配置
git config --global credential.helper store
git submodule update --init --force
```

#### 环境变量未生效
```bash
# 问题：修改环境配置后未生效
# 解决：重新生成环境文件
rm .env.development
npm run server  # 重新选择子模块
```

#### 构建失败
```bash
# 问题：构建过程中出现依赖错误
# 解决：清理依赖重新安装
rm -rf node_modules package-lock.json
npm install --legacy-peer-deps
```

#### 子模块版本冲突
```bash
# 问题：子模块版本与主项目不兼容
# 解决：检查并更新子模块到兼容版本
cd src/pages/[子模块名]
git checkout [兼容分支]
cd ../../..
git add src/pages/[子模块名]
git commit -m "update submodule version"
```

### 7. 性能优化建议

#### 开发环境优化
- 只拉取需要开发的子模块，减少项目体积
- 使用 Vite 的热更新功能，提高开发效率
- 合理使用浏览器缓存，避免重复加载资源

#### 生产环境优化
- 启用代码压缩和混淆（已配置 Terser）
- 使用 CDN 加速静态资源加载
- 合理配置缓存策略
- 按需加载子模块，减少首屏加载时间

#### 构建优化
- 使用 `--legacy-peer-deps` 解决依赖冲突
- 定期清理无用的依赖包
- 优化图片和静态资源大小

## 总结

YD-Admin 项目通过 Git 子模块机制实现了真正的微前端架构，具有以下核心优势：

1. **模块化**: 每个业务系统独立开发和维护
2. **统一性**: 统一的技术栈和开发规范
3. **灵活性**: 支持按需部署和多环境配置
4. **可扩展性**: 易于添加新的子系统
5. **可维护性**: 清晰的项目结构和规范的开发流程

该架构适合中大型企业的多系统集成场景，能够有效提高开发效率和系统可维护性。


# TIP

2024.12.12 - laihq

由于需求变更图书馆需要能够单独登录，不在依赖云平台基础数据，故图书馆入口新增社会登录入口，相关业务逻辑变动。涉及代码变动项目：（馆务`yd-library`，RFID, `yd-rfid`），一德后台等。

在此项目中`npm run server`启动新增两个选项：

    社会图书馆-FRID
    社会图书馆-馆务系统
    ...

对应的code标识： `shRFID` `shLibrary`,和之前的src/pages/ yd-library `or` yd-rfid 是一样的，差异业务通过登录后不同的路由处理。

与此同时两个新的入口登录页写在 `src/components/societyLogin` 通过 启动后的code标识判断，`#/shLibraryLogin` or `#/shRfidLogin`,退出登录，以及网络请求，或者store 都是通过此判断。

<hr/>

# 第三方登录授权

2025.5.20

讯飞第三方登录进入授权页：
http://*************:9008/#/socialLogin?source=IFlytek&ifuseriflyssost
`source`: `IFlytek` 为第三方登录平台标识（科大讯飞），`ifuseriflyssost` 为第三方登录平台返回的用户标识。

登录成功后，返回授权页

    1. 调用接口获取用户信息。
    2. 调用接口获取用户权限。
    3. 调用接口获取用户角色。
    4. localStorage 缓存source来源状态IFlytek,并跳转到图书馆首页。
    5. 其他来源的登录逻辑类似。

在`Header`组件中，通过判断 `localStorage` 缓存的`source`来源状态，来判断是否显示科大讯飞登录按钮，则隐藏“`修改密码按钮`、`修改用户信息按钮`、`前往云平台按钮`”的入口。

在yd-library 项目中，通过判断 localStorage 缓存的source来源状态，来判断是否是科大讯飞登录，则隐藏“`点击前往云平台`”按钮的入口。

<hr/>
