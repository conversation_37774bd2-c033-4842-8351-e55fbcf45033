## ETable组件用法

-   `操作列`的data-index指定为`operate`会自动`fixed固定`
-   hash: `每个路由路径下`需要是`唯一的` (一个路由地址只存在一个ETable时hash可不指定)
-   bodyCell插槽: `#<dataIndex>`。例如`#name`
-   headerCell/customFilterIcon/customFilterDropdown插槽(存在dataIndex的插槽): `#<slotName>-<dataIndex>`。例如`#headerCell-name`
-   其余插槽:`#<slotName>-slot`。例如`#summary-slot`
-   [在线演示地址](https://codesandbox.io/p/devbox/etable-demo-zyf792)

<img src="https://p1-juejin.byteimg.com/tos-cn-i-k3u1fbpfcp/60104e6cba6947b3b0b0271e95c82fcb~tplv-k3u1fbpfcp-jj-mark:0:0:0:0:q75.image#?w=810&h=642&s=41585&e=png&b=fdfdfd" width=30% />

**_columns示例_**

```ts
<ETable
    hash="unique"
    :columns="columns"
    // ...
>
    // header插槽
    <template #headerCell-name="{ title }">
        <span>{{ " (自定义)" + title }}</span>
    </template>
    // body插槽
    <template #sex="{ text }">
        <span>{{ text ? "男" : "女" }}</span>
    </template>
    // 其余插槽(总计...)
    <template #summary-slot>
        <a-table-summary-cell>合计：</a-table-summary-cell>
    </template>
</ETable>
```

**_template示例_**

```ts
<ETable
    hash="unique"
    :dataSource="data"
    // ...
>
    // header插槽
    <template #headerCell-name="{ title }">
        <span>{{ " (自定义)" + title }}</span>
    </template>
    // body
    <a-table-column title="性别" data-index="sex">
        <template #default="{ text }">
          <span>{{ text ? "男" : "女" }}</span>
        </template>
      </a-table-column>
    // 其余插槽(总计...)
    <template #summary-slot>
        <a-table-summary-cell>合计：</a-table-summary-cell>
    </template>
</ETable>
```
