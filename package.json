{"name": "library-admin", "private": true, "version": "1.0.0", "type": "module", "scripts": {"server": "node ./scripts/server", "dev": "vite", "build:dev": "vite build --mode dev", "build:uat": "vite build --mode uat", "build:uatrelease": "vite build --mode uatrelease", "build:main": "vite build --mode main", "preview": "vite preview", "prepare": "husky install"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "@inquirer/prompts": "^3.1.1", "@tinymce/tinymce-vue": "^5.1.0", "@vueuse/core": "^10.4.1", "ant-design-vue": "4.0.8", "axios": "^1.4.0", "colorpicker-v3": "^2.10.2", "cropperjs": "^1.6.1", "crypto-js": "^4.2.0", "crypto.js": "^3.2.2", "dayjs": "^1.11.9", "echarts": "^5.4.3", "encryptlong": "^3.1.4", "eventsource-parser": "^3.0.1", "isbn3": "^1.1.43", "jsencrypt": "^3.3.2", "pinia": "^2.1.6", "pinia-plugin-persistedstate": "^3.2.0", "print-js": "^1.6.0", "qs": "^6.11.2", "sortablejs": "^1.15.0", "terser": "^5.21.0", "tinymce": "^6.0.0", "vite-plugin-vue-setup-extend": "^0.4.0", "vue": "3.3.12", "vue-drag-resize": "^1.5.4", "vue-router": "4", "xgplayer": "^3.0.22", "ydutils": "git+http://root:<EMAIL>/web/ydutils.git#main"}, "devDependencies": {"@unocss/preset-rem-to-px": "^0.55.0", "@unocss/reset": "^0.55.0", "@vitejs/plugin-vue": "^4.2.3", "husky": "^8.0.3", "less": "^4.2.0", "lint-staged": "^14.0.1", "prettier": "3.0.3", "unocss": "^0.55.0", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^4.4.5", "vite-plugin-html": "^3.2.0"}, "overrides": {"pinia": {"vue": "3.3.12"}}}