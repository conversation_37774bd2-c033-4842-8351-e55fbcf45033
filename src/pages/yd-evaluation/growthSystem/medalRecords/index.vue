<template>
    <cont-header />
    <div p-20>
        <div pb-20>
            <a-form :model="query" autocomplete="off">
                <a-row :gutter="[25, 16]">
                    <YCol>
                        <a-form-item label="姓名：">
                            <a-input v-model:value.trim="query.personName" placeholder="请输入" />
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-form-item label="班级：">
                            <a-input v-model:value.trim="query.groupName" placeholder="请输入" />
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-form-item label="勋章名称：">
                            <a-input v-model:value.trim="query.medalName" placeholder="请输入" />
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-form-item label="发放形式：">
                            <a-select
                                :options="state.issuanceMethodList"
                                v-model:value="query.issuanceMethod"
                                placeholder="请输入"
                            ></a-select>
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-form-item label="勋章状态：">
                            <a-select
                                :options="state.issuanceStatusList"
                                v-model:value="query.issuanceStatus"
                                placeholder="请输入"
                            ></a-select>
                        </a-form-item>
                    </YCol>
                    <YCol :span="5">
                        <a-form-item label="获得时间：">
                            <RangePicker v-model:startTime="query.getStartTime" v-model:endTime="query.getEndTime"></RangePicker>
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-button type="primary" @click="queryData">
                            <SearchOutlined />
                            查 询
                        </a-button>
                        <a-button @click="resetData">
                            <reload-outlined />
                            重 置
                        </a-button>
                    </YCol>
                </a-row>
            </a-form>
        </div>
        <div mb-20 v-if="cloudState.codeId">
            <a-button @click="grantMedal" type="primary">发放勋章</a-button>
        </div>
        <div min-h-520 mb-20>
            <ETable
                hash="readersList"
                :loading="page.loading"
                :dataSource="page.list"
                :total="page.total"
                @paginationChange="paginationChange"
                :current="query.pageNo"
                @change="handleTableChange"
            >
                <a-table-column title="姓名" data-index="personName" :width="100" />
                <a-table-column title="班级" data-index="groupNameList" :width="150"></a-table-column>
                <a-table-column title="勋章名称" data-index="medalName"></a-table-column>
                <a-table-column title="发放形式" data-index="issuanceMethod">
                    <template #default="{ record, text }">
                        {{ getStatusObj(record.issuanceMethod) }}
                    </template>
                </a-table-column>
                <a-table-column title="勋章状态" data-index="issuanceStatus">
                    <template #default="{ record, text }">
                        {{ getStatusMedal(record.issuanceStatus) }}
                    </template>
                </a-table-column>
                <a-table-column title="获得积分" data-index="medalScore" :width="100">
                    <template #default="{ record, text }">
                        {{record.medalScore > 0 ? `+${record.medalScore}` : record.medalScore}}
                    </template>
                </a-table-column>
                <!-- <a-table-column title="勋章奖励" data-index="medalScore"></a-table-column> -->
                <a-table-column title="获得时间" data-index="getTime" :sorter="true"></a-table-column>
                <a-table-column title="回收时间" data-index="recoveryTime"></a-table-column>
                <!-- <a-table-column title="积分要求" data-index="idCard" :width="200"></a-table-column>
                <a-table-column title="勋章奖励" data-index="isDisabled" :width="100">
                    <template #default="{ text }">
                        {{ ['已启用', '已禁用'][text] }}
                    </template>
                </a-table-column>

                <a-table-column title="最新评价时间" data-index="libraryName" :width="100"></a-table-column> -->

                <a-table-column title="操作" data-index="operate" :width="100">
                    <template #default="{ record }">
                        <!-- 只有手动发放的 并且 状态是发放的 并且 没有回收时间的 才可以取消发放 -->
                        <a-button v-if="record.issuanceMethod === 2 && record.issuanceStatus === 1 && !record.recoveryTime" danger type="link" class="btn-link-color" @click="cancelIssuance(record)">取消发放</a-button>
                    </template>
                </a-table-column>
            </ETable>
        </div>
    </div>

    <!-- 这是发放勋章的抽屉 -->
    <a-drawer
        :width="500"
        v-model:open="state.grantMedalOpen"
        :keyboard="false"
        :destroyOnClose="true"
        title="发放勋章"
        @close="handleClose"
    >
        <div class="grantMedalDrawer">
            <a-form
                :model="grantMedalForm"
                ref="grantMedalFormRef"
                name="grantMedalForm"
                autocomplete="off"
                :label-col="{ span: 8 }"
                :wrapper-col="{ span: 16 }"
            >
                <a-form-item
                    label="选择发放学生："
                    name="cardScore"
                    :rules="[{ required: true, message: '请选择发放学生' }]"
                    :label-col="{ span: 24 }"
                    :wrapper-col="{ span: 24 }"
                >
                    <a-input v-model:value="grantMedalForm.cardScore" @click="handleSelectModel" placeholder="请选择" />
                </a-form-item>
            </a-form>
            <div>
                <div v-if="state.pages.medalList.length" mt-20>
                    <div mb-16>选择勋章：</div>
                    <div>
                        <a-row :gutter="[25, 16]">
                            <a-col :span="8" v-for="(item, index) in state.pages.medalList" :key="item.value" text-center>
                                <div class="medalbox_ch" @click="choiceMedal(item)">
                                    <div class="medalbox_radio_ch">
                                        <img v-if="item.isChoice" src="@/assets/images/admin/medal_yes.png" />
                                        <img v-else src="@/assets/images/admin/medal_no.png" />
                                    </div>
                                    <div class="medalbox_img_ch">
                                        <img :src="item.label" alt="" />
                                    </div>
                                </div>
                                <span class="text_name" :title="item.name">{{ item.name }}</span>
                            </a-col>
                        </a-row>
                    </div>
                </div>
                <div v-else mt-40 text-center w-full class="color-#5c5c5c">暂无可发放勋章</div>
            </div>
            <div>
                <a-button v-if="state.pages.pageNo * state.pages.pageSize < state.pages.total" @click="getMedal(true)">加载更多</a-button>
            </div>
        </div>
        <template #footer>
            <a-button @click="handleClose">取消</a-button>
            <a-button type="primary" @click="handleMedals" :disabled="!state.pages.medalList.length">确定</a-button>
        </template>
    </a-drawer>

    <!-- 选人组件 -->
    <ModelSelect v-model:openVisible="modelState.openVisible" :tabs="state.peopleTabs" :selected="state.selectEcho" />
</template>
<script setup name="medalRecords">
const cloudState = reactive({ ...getUrlParams() })
const state = reactive({
    formState: {
        identity: 0,
    },
    issuanceMethodList: [
        { value: 1, label: '自动发放' },
        { value: 2, label: '手动发放' },
    ],
    issuanceStatusList: [
        { value: null, label: '全部' },
        { value: 1, label: '发放' },
        { value: 2, label: '回收' },
    ],
    peopleTabs: [
        {
            tab: '学生',
            checked: true,
            checkVisible: 'all',
            id: 0,
            personField: { key: 'typeValue', value: ['student'] },
            single: false,
            searchOption: {
                show: true,
                displayMode: 'old',
            },
        },
    ],
    selectEcho: [],
    grantMedalOpen: false,
    pages: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
        medalList: [],
    },
})

const grantMedalForm = reactive({
    cardScore: '',
    medalCodeList: [],
})

const { query, page, getList, reset, paginationChange } = useList('/cloud/evalMedal/record/pageEvalMedalRecord', {
    ...state.formState,
    issuanceStatus: null,
    evalTypeId: cloudState.codeId || '',
})

getList()

const issuanceMethodObj = {
    1: '自动发放',
    2: '手动发放',
    default: '-',
}

const issuanceStatusObj = {
    1: '发放',
    2: '回收',
    default: '-',
}

function getStatusObj(key) {
    return issuanceMethodObj[key] || issuanceMethodObj.default
}


function getStatusMedal(key) {
    return issuanceStatusObj[key] || issuanceStatusObj.default
}

const queryData = () => {
    getList()
}

const resetData = () => {
    reset({
        ...state.formState,
        evalTypeId: cloudState.codeId || '',
    })
}

const orderType = {
    ascend: '1',
    descend: '2',
}

// table排序切换
const handleTableChange = (pag, filters, sorter) => {
    query.order = orderType[sorter.order]
    getList()
}

const handleSelectModel = () => {
    getSelectTree({ treeType: 1, businessType: 11 })
    modelState.openVisible = true
}

// 获取勋章
const getMedal = (data) => {
    if (data) {
        state.pages.pageNo++
    }
    const params = {
        medalStatus: 1,
        issuanceMethod: 2,
        ...state.pages,
        evalTypeId: cloudState.codeId,
    }
    http.post('/cloud/evalMedal/pageEvalMedal', params).then(res => {
        state.pages = { ...state.pages, ...res.data }
        const list = res.data.list.map(i => ({ label: i.medalIconUrl, value: i.medalCode, name: i.medalName }))
        state.pages.medalList.push(...list)
       
    })
}

// 打开发放勋章的弹窗
const grantMedal = () => {
    state.grantMedalOpen = true
    state.selectEcho = []
    getMedal()
}

// 关闭弹窗
const handleClose = () => {
    state.grantMedalOpen = false
    Object.keys(grantMedalForm).forEach(i => (grantMedalForm[i] = null))
    state.pages = {
        pageNo: 1,
        pageSize: 10,
        total: 0,
        medalList: [],
    }
}

const grantMedalFormRef = ref(null)
// 发放勋章
const handleMedals = () => {
    if (!state.pages.medalList.length) return YMessage.warning('暂无可发放勋章')
    grantMedalFormRef.value.validate().then(res => {
        if (!state.pages.medalList.some(item => item.isChoice === true)) return YMessage.warning('请选择勋章')
        const params = {
            personListDTO: state.selectEcho.map(i => ({
                id: i.id,
                name: i.name,
                typeValue: i.typeValue,
                identity: i._type,
            })),
            medalCodeList: state.pages.medalList.filter(item => item.isChoice === true).map(item => item.value),
        }
        http.post('/cloud/evalMedal/person/issuanceMedal', params).then(res => {
            YMessage.success('发放成功')
            handleClose()
            setTimeout(() => {
                resetData()
            }, 1000)
        })
    })
}

// 取消发放勋章
const cancelIssuance = async data => {
    let flag = await yConfirm('取消发放勋章', '确定取消发放？')
    if (flag) {
        const params = {
            personId: data.personId,
            identity: data.identity,
            medalCode: data.medalCode,
        }
        http.post('/cloud/evalMedal/person/cancelMedal', params).then(res => {
            YMessage.success('操作成功')
            setTimeout(() => {
                resetData()
            }, 1000)
        })
    }
}

// 关联弹窗
const modelState = reactive({
    openVisible: false, // 显示弹框
    dataSource: [], // 左侧数据源
    checkVisible: 'all', // 能选什么就选什么啊
    spinning: false, // loading
    disableSelect: [], // 禁止选择的ids
    searchTable: [], // 选人搜索 table 中显示
    globalID: '', // 最顶成id
})
provide('modelState', () => modelState)
provide('callbackFunction', () => ({
    search: searchSelect,
    toggleLevel,
    cancel: closeSelect,
    submit,
}))

// 获取到选人组件数据
function getSelectTree(options) {
    /**
     * 1. 教职工 treeType：2 ， businessType：21
     * 2. 学生  treeType：1 ， businessType：11
     * 3. 家长  treeType：1 ， businessType：12
     * 4. pid第一次为0.之后根据层级id请求
     */
    modelState.spinning = true
    const params = {
        treeType: 1,
        pid: 0,
        typeValue: null,
        businessType: 11,
        code: null,
        isRule: true,
        ...options,
    }

    http.post('/cloud/v3/tree/selectTree', params)
        .then(({ data }) => {
            setDataSource(data)
        })
        .finally(() => {
            modelState.spinning = false
        })
}
getSelectTree({ treeType: 1, businessType: 11 })

// 设置选人数据源
function setDataSource(data) {
    modelState.dataSource = data || []
}

// 请求下一级数据
function toggleLevel(tabId, item = {}, options) {
    console.log('options :>> ', options)
    const firstLevel = !options.index
    let params = {
        treeType: item.treeType,
        businessType: item.businessType,
        typeValue: item.typeValue,
        pid: item.id,
    }
    // 首层数据
    if (firstLevel) {
        params = {
            treeType: 1,
            businessType: 11,
        }
    }
    getSelectTree(params)
}

// 查找教职工 注意请求参数啊! 还没开始写
function searchSelect(tabId, name) {
    modelState.spinning = true
    const options = {
        treeType: 1,
        businessType: 11,
        code: null,
        isRule: true,
        searchKey: name,
        pageNo: 1,
        // 目前没有分页，最大条数
        pageSize: 100,
    }
    http.post('/cloud/v3/tree/selectTree/search', options)
        .then(({ data }) => {
            setDataSource(data.list || [])
        })
        .finally(() => {
            modelState.spinning = false
        })
}

const closeSelect = () => {}

// 多选勋章
const choiceMedal = data => {
    data.isChoice = !data.isChoice
}

// 点击确定的人员 最重要的是要知道这块是确认给谁的数据啊
const submit = checked => {
    console.log('确定好的人员checked', checked)
    state.selectEcho = checked
    grantMedalForm.cardScore = checked.map(i => i.name).join('、')
}
</script>
<style lang="less" scoped>
.medalbox {
    cursor: pointer;
    position: relative;
    .medalbox_img {
        width: 110px;
        // height: 110px;
        background: #ffffff;
        // border: 1px solid #f0f2f5;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        // border-radius: 50%;
        margin-bottom: 20px;
        img {
            width: 110px;
            height: 110px;
        }
        .text {
            width: 110px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-align: center;
        }
    }
    .medalbox_radio {
        right: 10px;
        width: 18px;
        height: 18px;
        position: absolute;
        img {
            width: 18px;
            height: 18px;
        }
    }
}

.text_name {
    display: inline-block;
    width: 110px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
}

.medalbox_ch {
    cursor: pointer;
    position: relative;
    .medalbox_img_ch {
        // width: 110px;
        height: 110px;
        background: #ffffff;
        border: 1px solid #f0f2f5;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        img {
            width: 110px;
            height: 110px;
        }
    }
    .medalbox_radio_ch {
        right: 0;
        width: 18px;
        height: 18px;
        position: absolute;
        img {
            width: 18px;
            height: 18px;
        }
    }
}
</style>
