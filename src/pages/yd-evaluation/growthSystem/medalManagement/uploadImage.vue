<template>
    <a-upload name="avatar" list-type="picture-card" class="avatar-uploader" :show-upload-list="false" :before-upload="beforeUpload">
        <div class="image-container" v-if="imageUrl" w-full h-full>
            <img w-full h-full :src="imageUrl" alt="avatar" />
        </div>
        <div v-else>
            <loading-outlined v-if="loading"></loading-outlined>
            <plus-outlined v-else></plus-outlined>
            <div class="tip_text">点击上传</div>
        </div>
    </a-upload>
</template>

<script setup>
// *********************
// Hooks
// *********************

const props = defineProps({
    limit: {
        type: Number,
        // M为单位
        default: 1,
    },
    url: {
        type: String,
        // M为单位
        default: '',
    },
})

const emit = defineEmits(['update:url'])
const loading = ref(false)
const imageUrl = ref(props.url)

// *********************
// Service Function
// *********************

// 上传图片
const uploadImages = async file => {
    http.form('/file/common/upload', { file, folderType: 'evalActivity' }, {})
        .then(res => {
            const { data } = res
            imageUrl.value = data[0].url
            emit('update:url', data[0].url)
        })
        .catch(() => {
            YMessage.error('图片上传失败!')
        })
        .finally(() => {
            loading.value = false
        })
}

// 判断图片大小以及转成base64
const beforeUpload = file => {
    loading.value = true
    const imageSize = file.size / 1024 / 1024
    if (imageSize > props.limit) {
        YMessage.error(`图片大小为${imageSize}M, 大于${props.limit}M`)
        loading.value = false
        return false
    }
    uploadImages(file)
    return false
}

// *********************
// Watch Function
// *********************
watch(
    () => props.url,
    newValue => {
        imageUrl.value = newValue
    },
)
</script>

<style scoped lang="less">
.image-container {
    position: relative;
    &:hover:before {
        content: '';
        position: absolute;
        background: #000000;
        top: 0;
        bottom: 0;
        right: 0;
        left: 0;
        border-radius: 4px;
        opacity: 0.6;
        z-index: 1;
    }
    &:hover:after {
        content: '更改';
        position: absolute;
        left: calc(50% - 20px);
        top: calc(50% - 10px);
        display: inline-block;
        box-sizing: border-box;
        width: 40px;
        height: 20px;
        background: #000000;
        opacity: 0.6;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 400;
        color: #ffffff;
        line-height: 14px;
        z-index: 2;
        transform: scale(0.8);
    }
}
.avatar-uploader {
    width: 100%;
    height: 100%;
}

.avatar-uploader {
    :deep(.ant-upload-select-picture-card) {
        width: 100% !important;
        height: 100% !important;
    }
}

.ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
}
.tip_text{
    font-weight: 400;
    font-size: 14px;
    color: rgba(0,0,0,0.65);
}
</style>
