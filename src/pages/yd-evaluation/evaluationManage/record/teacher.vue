<template>
    <div class="evaluationRecordPage">
        <div class="sumBox" p-20>
            <div class="sumBox_item">
                <div class="sumBox_item_title">参与部门</div>
                <div class="sumBox_item_sum">{{ state.count.gradeCount || '0' }}</div>
            </div>
            <div class="sumBox_item">
                <div class="sumBox_item_title">参与总人数</div>
                <div class="sumBox_item_sum">{{ state.count.personCount || '0' }}</div>
            </div>
            <div class="sumBox_item">
                <div class="sumBox_item_title">参与评价次数</div>
                <div class="sumBox_item_sum">{{ state.count.evalCount || '0' }}</div>
            </div>
            <div class="sumBox_item">
                <div class="sumBox_item_title">总获得积分</div>
                <div class="sumBox_item_sum">{{ state.count.scoreCount || '0' }}</div>
            </div>
        </div>

        <div class="sumBoxList">
            <div p-20>
                <div pb-20>
                    <a-form :model="query" autocomplete="off">
                        <a-row :gutter="[25, 16]">
                            <YCol>
                                <a-form-item label="姓名：">
                                    <a-input v-model:value="query.toPersonName" placeholder="请输入" />
                                </a-form-item>
                            </YCol>
                            <YCol>
                                <a-form-item label="部门：">
                                    <a-input v-model:value="query.classesName" placeholder="请输入" />
                                </a-form-item>
                            </YCol>
                            <YCol>
                                <a-form-item label="活动名称：">
                                    <a-input v-model:value="query.title" placeholder="请输入" />
                                </a-form-item>
                            </YCol>
                            <YCol v-if="!cloudState.codeId">
                                <a-form-item label="评价类型：">
                                    <a-select
                                        :options="state.evalTypeList"
                                        v-model:value="query.evalTypeId"
                                        placeholder="请输入"
                                        :field-names="{ label: 'name', value: 'id' }"
                                    ></a-select>
                                </a-form-item>
                            </YCol>
                            <YCol :span="5">
                                <a-form-item label="最近参与时间：">
                                    <RangePicker v-model:startTime="query.startTime" v-model:endTime="query.endTime"></RangePicker>
                                </a-form-item>
                            </YCol>
                            <YCol>
                                <a-button type="primary" @click="getList">
                                    <SearchOutlined />
                                    查 询
                                </a-button>
                                <a-button @click="getReset">
                                    <reload-outlined />
                                    重 置
                                </a-button>
                            </YCol>
                        </a-row>
                    </a-form>
                </div>
                <div mb-20 mt-20>
                    <ETable
                        hash="evaluationRecordTable"
                        :loading="page.loading"
                        :dataSource="page.list"
                        :total="page.total"
                        @paginationChange="paginationChange"
                        :current="query.pageNo"
                        @change="handleTableChange"
                        :row-selection="{
                            selectedRowKeys: state.selectedRowKeys,
                            onChange: value => {
                                state.selectedRowKeys = value
                            },
                        }"
                    >
                        <a-table-column title="姓名" data-index="toPersonName" :width="100" />
                        <a-table-column title="部门" data-index="classesName" :width="100" />
                        <!-- <a-table-column title="评价类型" data-index="classesName" :width="100" /> -->
                        <!-- <a-table-column title="活动名称" data-index="classesName" :width="100" /> -->
                        <a-table-column title="参与评价次数" data-index="partakeCount" :width="150">
                            <template #default="{ record }">{{ record.partakeCount || 0 }}/{{ record.totalCount || 0 }}</template>
                        </a-table-column>
                        <a-table-column title="总获得积分" data-index="totalScore" :width="100">
                            <template #default="{ text }">
                                <span>{{ text || '-' }}</span>
                            </template>
                        </a-table-column>
                        <!-- <a-table-column title="评价类型" data-index="evalTypeName" :width="100"></a-table-column> -->
                        <a-table-column title="最近参与评价活动" data-index="title"></a-table-column>
                        <a-table-column title="最近参与时间" data-index="scoreTime" :width="120" :sorter="true"></a-table-column>
                        <a-table-column title="操作" data-index="operate" :width="80">
                            <template #default="{ record }">
                                <a-button type="link" class="btn-link-color" @click="syncHumanFace(record)">查看评价</a-button>
                                <!-- <a-button type="link" danger class="btn-link-color" @click="delHumanFace(record)">立即评价</a-button> -->
                            </template>
                        </a-table-column>
                    </ETable>
                </div>
            </div>
        </div>
    </div>
    <evaluationDetailed ref="evaluationDetailedRef" />
</template>

<script setup>
import evaluationDetailed from './evaluationDetailed.vue'
const allOption = { id: '', name: '全部' }
const cloudState = reactive({ ...getUrlParams() })
const { query, page, getList, reset, paginationChange } = useList('/cloud/evalDayRulePerson/countRulePersonPage', {
    evalTypeId: cloudState.codeId || '',
    identity: 1,
})
getList()

const getReset = () => {
    reset({
        evalTypeId: cloudState.codeId || '',
        identity: 1,
    })
}

const state = reactive({
    count: {
        classesCount: 0,
        evalCount: 0,
        gradeCount: 0,
        personCount: 0,
        scoreCount: 0,
    },
    selectedRowKeys: [],
    evalTypeList: [],
})

const orderType = {
    ascend: '1',
    descend: '2',
}

// table排序切换
const handleTableChange = (pag, filters, sorter) => {
    query.sortType = orderType[sorter.order]
    query.field = sorter.field
    getList()
}

const evaluationDetailedRef = ref(null)
// 查看
const syncHumanFace = item => {
    evaluationDetailedRef.value.showDetailedModel(item, false, true)
}

// 立即评价
// const delHumanFace = item => {
//     evaluationDetailedRef.value.showDetailedModel(item, true, false)
// }

// 获取活动记录参与数据统计
const getRulePersonCount = () => {
    http.get('/cloud/evalDayRulePerson/getRulePersonCount', {
        identity: 1,
        evalTypeId: cloudState.codeId || '',
    }).then(res => {
        Object.assign(state.count, res.data)
    })
}
getRulePersonCount()

// 直接获取一个评价类型列表
const getEvalTypeList = () => {
    http.get('/cloud/evalType/listBySchool').then(res => {
        state.evalTypeList = [allOption, ...res.data]
    })
}
getEvalTypeList()
</script>

<style lang="less" scoped>
.sumBox {
    display: flex;
    .sumBox_item {
        padding: 16px 24px;
        width: 200px;
        height: 108px;
        background: #f0f2f5;
        margin-right: 20px;
        border-radius: 10px;
        .sumBox_item_title {
            font-weight: 400;
            font-size: 16px;
            padding-bottom: 16px;
            color: rgba(0, 0, 0, 0.45);
        }
        .sumBox_item_sum {
            font-size: 22px;
            color: #262626;
            font-weight: 460;
            color: #262626;
        }
    }
}
</style>
