<template>
    <cont-header type="menu" :menuList="menuList" v-model:activeKey="activeKey"></cont-header>
    <Student v-if="activeKey === 1"></Student>
    <Teacher v-if="activeKey === 2"></Teacher>
</template>
<script setup name="tagPrint">
import Student from './student.vue'
import Teacher from './teacher.vue'
const activeKey = ref(1)

const menuList = ref([
    { name: '学生', value: 1 },
    { name: '老师', value: 2 },
])
</script>
<style lang="less" scoped></style>
