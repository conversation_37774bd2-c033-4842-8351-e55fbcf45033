<template>
    <YDrawer class="drawer_box" v-model:open="state.open" title="查看记录" @closebtn="handleClose">
        <a-spin :spinning="state.spinning">
            <div class="card_box">
                <a-row :gutter="[16, 16]">
                    <a-col :span="12">
                        <div>
                            评价类型：
                            <span>{{ state.rulePersonDetails.evalTypeName }}</span>
                        </div>
                    </a-col>
                    <a-col :span="12">
                        <div>
                            活动名称：
                            <span>{{ state.rulePersonDetails.title }}</span>
                        </div>
                    </a-col>
                    <a-col :span="12">
                        <div>
                            姓名：
                            <span>{{ state.rulePersonDetails.toPersonName }}</span>
                        </div>
                    </a-col>
                    <a-col :span="12">
                        <div>
                            班级：
                            <span>{{ state.rulePersonDetails.orgName }}</span>
                        </div>
                    </a-col>
                    <a-col :span="24">
                        <div>
                            评语：
                            <span>{{ state.rulePersonDetails.comment }}</span>
                        </div>
                    </a-col>
                </a-row>
                <div class="final_core">
                    <div class="final_core_core">{{ state.rulePersonDetails.totalScore }}</div>
                    <div class="core_title">最终得分</div>
                </div>
            </div>
            <div class="listBox">
                <div class="list_arr">
                    <div class="list_item" v-for="(item, index) in state.tableList">
                        <a-row justify="space-between">
                            <a-col :span="6">
                                <div class="item_titleBox">
                                    <div class="item_line"></div>
                                    <div class="item_title">第{{ item.thisCount }}次评价</div>
                                </div>
                            </a-col>
                            <a-col :span="6">
                                <div class="list_item_sub">
                                    最后得分：
                                    <span>{{ item.score }}</span>
                                </div>
                            </a-col>
                            <a-col :span="6">
                                <div class="list_item_sub">
                                    得分时间：
                                    <span>{{ item.scoreTime }}</span>
                                </div>
                            </a-col>
                            <a-col :span="2">
                                <div class="list_item_sub spread" @click="showDetails(item)">
                                    评价详情
                                    <RightOutlined v-if="!item.showDetails" />
                                    <DownOutlined v-if="item.showDetails" />
                                </div>
                            </a-col>
                        </a-row>
                        <div class="onlyShowOne" v-show="item.showDetails">
                            <div class="detailsTableBox_table">
                                <a-table :dataSource="state.dataSource" :columns="columns" bordered :pagination="false">
                                    <template #headerCell="{ column }">
                                        <template v-if="column.key === 'othersIndicatorScore'">
                                            <a-tooltip color="red">
                                                <template #title>还有{{ state.notEvalScoreNum }}人未评价</template>
                                                他人评分
                                            </a-tooltip>
                                        </template>
                                    </template>
                                    <template #bodyCell="{ column, record }">
                                        <template v-if="column.key === 'minScore'">{{ record.minScore }}-{{ record.maxScore }}</template>

                                        <template v-if="column.key === 'comment'">
                                            <div>
                                                {{ record.comment }}
                                            </div>
                                            <div class="paths">
                                                <div v-if="getFirstMediaFile(record)" class="thumbnail-item">
                                                    <!-- 图片缩略图 -->
                                                    <img
                                                        v-if="getFirstMediaFile(record).type === 'image'"
                                                        :src="getFirstMediaFile(record).url"
                                                        alt="缩略图"
                                                    />
                                                    <!-- 视频缩略图 -->
                                                    <video
                                                        v-else-if="getFirstMediaFile(record).type === 'video'"
                                                        :src="getFirstMediaFile(record).url"
                                                        muted
                                                    ></video>
                                                    <div class="thumbnail-overlay">
                                                        <PlayCircleOutlined v-if="getFirstMediaFile(record).type === 'video'" />
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="moreBtn" @click="openPreviewModal(record)">
                                                更多
                                                <RightOutlined />
                                            </div>
                                        </template>
                                    </template>
                                </a-table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer_box">
                    <a-pagination
                        v-model:current="state.query.pageNo"
                        v-model:page-size="state.query.pageSize"
                        :hideOnSinglePage="true"
                        :showLessItems="true"
                        :showQuickJumper="true"
                        :showSizeChanger="true"
                        :pageSizeOptions="['10', '20', '50', '100']"
                        :total="state.query.total"
                        :show-total="total => `共 ${total} 条`"
                        @change="onPaginationChange"
                    />
                </div>
            </div>
        </a-spin>
        <template #footer v-if="state.isEdit">
            <a-button mr-5 @click="handleClose">取消</a-button>
            <a-button type="primary" @click="handleConfirm">确定</a-button>
        </template>
    </YDrawer>
    <PreviewModal ref="previewModalRef" />
</template>

<script setup>
import PreviewModal from '../components/PreviewModal.vue'
const previewModalRef = ref(null)
const state = reactive({
    open: false,
    isEdit: false,
    spinning: false,
    query: {
        pageNo: 1,
        pageSize: 10,
    },
    rulePersonDetails: {},
})

// 获取图片列表
const getImageList = imgPaths => {
    if (!imgPaths) return []
    return imgPaths.split(',').filter(path => path.trim())
}

// 获取视频列表
const getVideoList = videoPaths => {
    if (!videoPaths) return []
    return videoPaths.split(',').filter(path => path.trim())
}

// 获取第一个媒体文件
const getFirstMediaFile = record => {
    // 优先显示图片
    if (record.imgPaths) {
        const imageList = getImageList(record.imgPaths)
        if (imageList.length > 0) {
            return { type: 'image', url: imageList[0] }
        }
    }

    // 然后显示视频
    if (record.videoPaths) {
        const videoList = getVideoList(record.videoPaths)
        if (videoList.length > 0) {
            return { type: 'video', url: videoList[0] }
        }
    }

    return null
}

/**
 * 数据转换函数 - 将嵌套数据转换为扁平化表格数据
 */
const transformTableData = originalData => {
    const flatData = []

    originalData.forEach(firstLevel => {
        // 如果一级指标下有二级指标
        if (firstLevel.secondIndicators && firstLevel.secondIndicators.length > 0) {
            firstLevel.secondIndicators.forEach(secondLevel => {
                const indicatorScore = secondLevel.indicatorScore || {}

                flatData.push({
                    // 一级指标信息
                    firstIndicatorId: firstLevel.id,
                    firstIndicatorName: firstLevel.name,

                    // 二级指标信息
                    secondIndicatorId: secondLevel.id,
                    secondIndicatorName: secondLevel.name,

                    // 评分相关信息
                    content: indicatorScore.content || '',
                    minScore: indicatorScore.minScore || 0,
                    maxScore: indicatorScore.maxScore || 100,
                    scoreCardName: indicatorScore.scoreCardName || '',
                    evalScoreTypeList: indicatorScore.evalScoreTypeList || [],

                    // 评分数据
                    othersIndicatorScore: indicatorScore.othersIndicatorScore,
                    thisIndicatorScore: indicatorScore.thisIndicatorScore,
                    totalIndicatorScore: indicatorScore.totalIndicatorScore,

                    // 审核后得分
                    approveScore: indicatorScore.approveScore,

                    // 备注
                    remark: indicatorScore.remark,

                    // 其他信息
                    comment: indicatorScore.comment || '',
                    imgPaths: indicatorScore.imgPaths || '',
                    videoPaths: indicatorScore.videoPaths || '',
                    rulePersonId: indicatorScore.rulePersonId,
                    indicatorId: indicatorScore.id,

                    // 原始数据引用（用于数据回写）
                    _originalFirst: firstLevel,
                    _originalSecond: secondLevel,
                    _originalScore: indicatorScore,
                })
            })
        } else {
            // 如果一级指标下没有二级指标，直接显示一级指标
            flatData.push({
                firstIndicatorId: firstLevel.id,
                firstIndicatorName: firstLevel.name,
                secondIndicatorId: null,
                secondIndicatorName: '',
                content: '',
                minScore: null,
                maxScore: null,
                scoreCardName: '',
                evalScoreTypeList: [],
                othersIndicatorScore: null,
                thisIndicatorScore: null,
                totalIndicatorScore: null,
                comment: '',
                imgPaths: '',
                videoPaths: '',
                _originalFirst: firstLevel,
                _originalSecond: null,
                _originalScore: null,
            })
        }
    })

    return flatData
}

const columns = ref([
    {
        title: '一级指标',
        dataIndex: 'firstIndicatorName',
        key: 'firstIndicatorName',
        // 核心合并逻辑：根据一级指标ID进行行合并
        customCell: (record, rowIndex) => {
            // 计算当前一级指标需要合并的行数
            let rowSpan = 1

            // 向下查找相同的一级指标
            for (let i = rowIndex + 1; i < state.dataSource.length; i++) {
                if (state.dataSource[i].firstIndicatorId === record.firstIndicatorId) {
                    rowSpan++
                } else {
                    break
                }
            }

            // 如果当前行不是该一级指标的第一行，则隐藏
            if (rowIndex > 0 && state.dataSource[rowIndex - 1].firstIndicatorId === record.firstIndicatorId) {
                return { rowSpan: 0 }
            }

            return { rowSpan }
        },
    },
    {
        title: '二级指标',
        dataIndex: 'secondIndicatorName',
        key: 'secondIndicatorName',
    },
    {
        title: '评分标准',
        dataIndex: 'content',
        key: 'content',
    },
    {
        title: '评分范围',
        dataIndex: 'minScore',
        key: 'minScore',
    },
    {
        title: '积分卡',
        dataIndex: 'scoreCardName',
        key: 'scoreCardName',
    },
    {
        title: '他人评分',
        dataIndex: 'othersIndicatorScore',
        key: 'othersIndicatorScore',
    },
    {
        title: '本次评分',
        dataIndex: 'thisIndicatorScore',
        key: 'thisIndicatorScore',
    },
    {
        title: '最后得分',
        dataIndex: 'totalIndicatorScore',
        key: 'totalIndicatorScore',
    },
    {
        title: '评语/图片/视频',
        dataIndex: 'comment',
        key: 'comment',
    },
    {
        title: '审核后最终得分',
        dataIndex: 'approveScore',
        key: 'approveScore',
        customHeaderCell: () => ({
            style: {
                background: '#FCF1D2',
            },
        }),
        customCell: () => ({
            style: {
                background: '#FCF1D2',
            },
        }),
    },
    {
        title: '备注',
        dataIndex: 'remark',
        key: 'remark',
    },
])

const showDetailedModel = async item => {
    state.query.activityId = item.activityId
    state.query.toPersonId = item.toPersonId
    getPageDayPersonScore()
    getEvalRulePersonDetails(item.id)

    state.open = true
}

// 获取参与人评分历史分页
const getPageDayPersonScore = () => {
    http.post('/cloud/evalDayRulePerson/pageDayPersonScore', {
        activityId: state.query.activityId, // 活动id
        toPersonId: state.query.toPersonId, // 参与人Id
        pageNo: state.query.pageNo,
        pageSize: state.query.pageSize,
    }).then(res => {
        state.tableList = res.data.list
        state.query.total = res.data.total
    })
}

// 点击页码
const onPaginationChange = (page, pageSize) => {
    state.query.pageNo = page
    state.query.pageSize = pageSize
    getPageDayPersonScore()
}

// 获取活动评分规则数据
const getEvalRulePersonDetails = rulePersonId => {
    http.post('/cloud/evalDayRulePerson/getEvalRulePersonDetails', {
        rulePersonId: rulePersonId, // 规则人员id
    }).then(res => {
        state.rulePersonDetails = res.data
        state.notEvalScoreNum = res.data.notEvalScoreNum
    })
}

const showDetails = item => {
    state.tableList.forEach(i => {
        if (i.id === item.id) {
            i.showDetails = !item.showDetails
        } else {
            i.showDetails = false
        }
    })
    getEvalRulePersonDetails(item.rulePersonId)

    http.post('/cloud/evalDayRulePerson/getRulePersonScoreList', {
        activityId: item.activityId, // 活动id
        toPersonId: item.toPersonId, // 参与人Id
        rulePersonId: item.rulePersonId, // 参与规则Id
        queryThisFrom: false, // 是否只查询自己评价的数据
        isApprove: true,
    }).then(res => {
        // 转数据结构
        state.dataSource = transformTableData(res.data || [])
    })
}

const openPreviewModal = record => {
    const scoreRecordList = record._originalScore.scoreRecordList || []
    previewModalRef.value.showModel(scoreRecordList)
}

defineExpose({
    showDetailedModel,
})
</script>

<style scoped lang="less">
.card_box {
    padding: 24px;
    position: relative;
    background: #f0f2f5;
    border-radius: 10px;
}
.final_core {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: absolute;
    right: 10px;
    top: 10px;

    .final_core_core {
        font-weight: 500;
        font-size: 32px;
        color: #333333;
    }
    .core_title {
        font-weight: 400;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.45);
    }
}

.list_item {
    padding-top: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #d9d9d9;
    .item_titleBox {
        display: flex;
        align-items: center;
        .item_line {
            width: 2px;
            height: 16px;
            background: #00b781;
            margin-right: 10px;
        }
        .item_title {
            font-weight: 400;
            font-size: 16px;
            color: #333333;
        }
    }
}

.spread {
    cursor: pointer;
    color: #00b781;
}

.onlyShowOne {
    margin-top: 16px;
    background: #eaf9f5;
    padding: 16px;
}

.moreBtn {
    cursor: pointer;
    font-weight: 400;
    font-size: 14px;
    color: #00b781;
}

.thumbnail-item {
    position: relative;
    width: 100px;
    height: 100px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s;

    img,
    video {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .thumbnail-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 20px;
        opacity: 1;
        transition: opacity 0.3s;
    }

    &.video-thumbnail {
        .thumbnail-overlay {
            opacity: 0.8;
        }
    }
}

.list_arr {
    padding-bottom: 24px;
}

.footer_box {
    text-align: right;
}
:deep(.ant-tree .ant-tree-treenode) {
    padding: 0 0 12px 0;
}
</style>
