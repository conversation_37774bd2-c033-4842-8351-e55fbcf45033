<template>
  <div class="table-merge-demo">
    <h2>表格合并案例演示</h2>
    <p class="demo-description">
      这是一个完整的表格合并案例，展示了一级指标与二级指标的合并效果。
      一级指标会根据相同的ID进行行合并，二级指标正常显示。
    </p>
    
    <!-- 表格展示 -->
    <a-table 
      :dataSource="tableDataSource" 
      :columns="tableColumns" 
      :pagination="false"
      bordered
      size="middle"
      :scroll="{ x: 1200 }"
    >
      <!-- 自定义评分范围显示 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'scoreRange'">
          <span>{{ record.minScore }} - {{ record.maxScore }}</span>
        </template>
        
        <!-- 评分类型显示 -->
        <template v-else-if="column.dataIndex === 'evalScoreTypeList'">
          <a-tag 
            v-for="type in record.evalScoreTypeList" 
            :key="type" 
            :color="getTypeColor(type)"
          >
            {{ getTypeText(type) }}
          </a-tag>
        </template>
        
        <!-- 他人评分显示 -->
        <template v-else-if="column.dataIndex === 'othersIndicatorScore'">
          <span v-if="record.othersIndicatorScore !== null">
            {{ record.othersIndicatorScore }}
          </span>
          <span v-else class="text-gray">暂无评分</span>
        </template>
        
        <!-- 本次评分输入框 -->
        <template v-else-if="column.dataIndex === 'thisIndicatorScore'">
          <a-input-number
            v-model:value="record.thisIndicatorScore"
            :min="record.minScore"
            :max="record.maxScore"
            :precision="1"
            placeholder="请输入分数"
            style="width: 100%"
            @change="calculateTotalScore(record)"
          />
        </template>
        
        <!-- 最后得分显示 -->
        <template v-else-if="column.dataIndex === 'totalIndicatorScore'">
          <span 
            v-if="record.totalIndicatorScore !== null" 
            :class="getScoreClass(record.totalIndicatorScore, record.maxScore)"
          >
            {{ record.totalIndicatorScore }}
          </span>
          <span v-else class="text-gray">-</span>
        </template>
        
        <!-- 评语输入 -->
        <template v-else-if="column.dataIndex === 'comment'">
          <a-textarea
            v-model:value="record.comment"
            placeholder="请输入评语"
            :rows="2"
            :maxlength="200"
            show-count
          />
        </template>
        
        <!-- 图片/视频上传 -->
        <template v-else-if="column.dataIndex === 'mediaPaths'">
          <div class="media-upload">
            <a-button 
              v-if="record.evalScoreTypeList.includes('image')" 
              type="link" 
              size="small"
              @click="handleImageUpload(record)"
            >
              上传图片
            </a-button>
            <a-button 
              v-if="record.evalScoreTypeList.includes('video')" 
              type="link" 
              size="small"
              @click="handleVideoUpload(record)"
            >
              上传视频
            </a-button>
            <span v-if="!record.evalScoreTypeList.includes('image') && !record.evalScoreTypeList.includes('video')">
              不支持上传
            </span>
          </div>
        </template>
      </template>
    </a-table>
    
    <!-- 操作按钮 -->
    <div class="demo-actions">
      <a-button type="primary" @click="handleSubmit">提交评分</a-button>
      <a-button @click="handleReset" style="margin-left: 8px">重置数据</a-button>
      <a-button @click="handleExport" style="margin-left: 8px">导出数据</a-button>
    </div>
    
    <!-- 数据预览 -->
    <div class="data-preview" v-if="showDataPreview">
      <h3>当前数据结构预览：</h3>
      <pre>{{ JSON.stringify(tableDataSource, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { tableData, columns } from './mockTable.js'

// 组件名称
defineOptions({
  name: 'TableMergeDemo'
})

// 响应式数据
const showDataPreview = ref(false)
const tableDataSource = ref([])

// 表格列配置 - 包含合并逻辑
const tableColumns = computed(() => [
  {
    title: '一级指标',
    dataIndex: 'firstIndicatorName',
    key: 'firstIndicatorName',
    width: 150,
    align: 'center',
    // 核心合并逻辑：根据一级指标ID进行行合并
    customCell: (record, rowIndex) => {
      // 计算当前一级指标需要合并的行数
      let rowSpan = 1
      
      // 向下查找相同的一级指标
      for (let i = rowIndex + 1; i < tableDataSource.value.length; i++) {
        if (tableDataSource.value[i].firstIndicatorId === record.firstIndicatorId) {
          rowSpan++
        } else {
          break
        }
      }
      
      // 如果当前行不是该一级指标的第一行，则隐藏
      if (rowIndex > 0 && 
          tableDataSource.value[rowIndex - 1].firstIndicatorId === record.firstIndicatorId) {
        return { rowSpan: 0 }
      }
      
      return { rowSpan }
    }
  },
  {
    title: '二级指标',
    dataIndex: 'secondIndicatorName',
    key: 'secondIndicatorName',
    width: 150,
    align: 'center'
  },
  {
    title: '评分标准',
    dataIndex: 'content',
    key: 'content',
    width: 200,
    ellipsis: true
  },
  {
    title: '评分范围',
    dataIndex: 'scoreRange',
    key: 'scoreRange',
    width: 120,
    align: 'center'
  },
  {
    title: '积分卡',
    dataIndex: 'scoreCardName',
    key: 'scoreCardName',
    width: 120,
    align: 'center'
  },
  {
    title: '评分类型',
    dataIndex: 'evalScoreTypeList',
    key: 'evalScoreTypeList',
    width: 150,
    align: 'center'
  },
  {
    title: '他人评分',
    dataIndex: 'othersIndicatorScore',
    key: 'othersIndicatorScore',
    width: 100,
    align: 'center'
  },
  {
    title: '本次评分',
    dataIndex: 'thisIndicatorScore',
    key: 'thisIndicatorScore',
    width: 120,
    align: 'center'
  },
  {
    title: '最后得分',
    dataIndex: 'totalIndicatorScore',
    key: 'totalIndicatorScore',
    width: 100,
    align: 'center'
  },
  {
    title: '评语',
    dataIndex: 'comment',
    key: 'comment',
    width: 200
  },
  {
    title: '图片/视频',
    dataIndex: 'mediaPaths',
    key: 'mediaPaths',
    width: 120,
    align: 'center'
  }
])

/**
 * 数据转换函数 - 将嵌套数据转换为扁平化表格数据
 */
const transformTableData = (originalData) => {
  const flatData = []
  
  originalData.forEach((firstLevel) => {
    // 如果一级指标下有二级指标
    if (firstLevel.secondIndicators && firstLevel.secondIndicators.length > 0) {
      firstLevel.secondIndicators.forEach((secondLevel) => {
        const indicatorScore = secondLevel.indicatorScore || {}
        
        flatData.push({
          // 一级指标信息
          firstIndicatorId: firstLevel.id,
          firstIndicatorName: firstLevel.name,
          
          // 二级指标信息
          secondIndicatorId: secondLevel.id,
          secondIndicatorName: secondLevel.name,
          
          // 评分相关信息
          content: indicatorScore.content || '',
          minScore: indicatorScore.minScore || 0,
          maxScore: indicatorScore.maxScore || 100,
          scoreCardName: indicatorScore.scoreCardName || '',
          evalScoreTypeList: indicatorScore.evalScoreTypeList || [],
          
          // 评分数据
          othersIndicatorScore: indicatorScore.othersIndicatorScore,
          thisIndicatorScore: indicatorScore.thisIndicatorScore,
          totalIndicatorScore: indicatorScore.totalIndicatorScore,
          
          // 其他信息
          comment: indicatorScore.comment || '',
          imgPaths: indicatorScore.imgPaths || '',
          videoPaths: indicatorScore.videoPaths || '',
          
          // 原始数据引用（用于数据回写）
          _originalFirst: firstLevel,
          _originalSecond: secondLevel,
          _originalScore: indicatorScore
        })
      })
    } else {
      // 如果一级指标下没有二级指标，直接显示一级指标
      flatData.push({
        firstIndicatorId: firstLevel.id,
        firstIndicatorName: firstLevel.name,
        secondIndicatorId: null,
        secondIndicatorName: '无二级指标',
        content: '',
        minScore: 0,
        maxScore: 100,
        scoreCardName: '',
        evalScoreTypeList: [],
        othersIndicatorScore: null,
        thisIndicatorScore: null,
        totalIndicatorScore: null,
        comment: '',
        imgPaths: '',
        videoPaths: '',
        _originalFirst: firstLevel,
        _originalSecond: null,
        _originalScore: null
      })
    }
  })
  
  return flatData
}

/**
 * 获取评分类型的颜色
 */
const getTypeColor = (type) => {
  const colorMap = {
    'text': 'blue',
    'image': 'green',
    'video': 'orange'
  }
  return colorMap[type] || 'default'
}

/**
 * 获取评分类型的文本
 */
const getTypeText = (type) => {
  const textMap = {
    'text': '文字',
    'image': '图片',
    'video': '视频'
  }
  return textMap[type] || type
}

/**
 * 获取分数的样式类
 */
const getScoreClass = (score, maxScore) => {
  const percentage = (score / maxScore) * 100
  if (percentage >= 90) return 'score-excellent'
  if (percentage >= 80) return 'score-good'
  if (percentage >= 60) return 'score-normal'
  return 'score-poor'
}

/**
 * 计算总分
 */
const calculateTotalScore = (record) => {
  if (record.thisIndicatorScore !== null && record.thisIndicatorScore !== undefined) {
    // 这里可以根据业务逻辑计算总分
    // 示例：如果有他人评分，取平均值；否则直接使用本次评分
    if (record.othersIndicatorScore !== null) {
      record.totalIndicatorScore = ((record.thisIndicatorScore + record.othersIndicatorScore) / 2).toFixed(1)
    } else {
      record.totalIndicatorScore = record.thisIndicatorScore
    }
  } else {
    record.totalIndicatorScore = null
  }
}

/**
 * 处理图片上传
 */
const handleImageUpload = (record) => {
  message.info(`为 ${record.secondIndicatorName} 上传图片`)
  // 这里可以集成实际的图片上传逻辑
}

/**
 * 处理视频上传
 */
const handleVideoUpload = (record) => {
  message.info(`为 ${record.secondIndicatorName} 上传视频`)
  // 这里可以集成实际的视频上传逻辑
}

/**
 * 提交评分
 */
const handleSubmit = () => {
  // 验证必填项
  const incompleteItems = tableDataSource.value.filter(item => 
    item.thisIndicatorScore === null || item.thisIndicatorScore === undefined
  )
  
  if (incompleteItems.length > 0) {
    message.warning(`还有 ${incompleteItems.length} 项未评分，请完成后再提交`)
    return
  }
  
  // 构建提交数据
  const submitData = tableDataSource.value.map(item => ({
    secondIndicatorId: item.secondIndicatorId,
    thisIndicatorScore: item.thisIndicatorScore,
    totalIndicatorScore: item.totalIndicatorScore,
    comment: item.comment
  }))
  
  console.log('提交的评分数据：', submitData)
  message.success('评分提交成功！')
}

/**
 * 重置数据
 */
const handleReset = () => {
  tableDataSource.value = transformTableData(tableData)
  message.info('数据已重置')
}

/**
 * 导出数据
 */
const handleExport = () => {
  showDataPreview.value = !showDataPreview.value
  message.info(showDataPreview.value ? '显示数据预览' : '隐藏数据预览')
}

// 组件挂载时初始化数据
onMounted(() => {
  tableDataSource.value = transformTableData(tableData)
})
</script>

<style scoped>
.table-merge-demo {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
}

.demo-description {
  margin-bottom: 20px;
  padding: 12px;
  background: #f6f8fa;
  border-left: 4px solid #1890ff;
  color: #666;
  line-height: 1.6;
}

.demo-actions {
  margin-top: 20px;
  text-align: center;
}

.data-preview {
  margin-top: 20px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 4px;
  max-height: 400px;
  overflow-y: auto;
}

.data-preview pre {
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
}

.media-upload {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.text-gray {
  color: #999;
}

/* 分数样式 */
.score-excellent {
  color: #52c41a;
  font-weight: bold;
}

.score-good {
  color: #1890ff;
  font-weight: bold;
}

.score-normal {
  color: #faad14;
}

.score-poor {
  color: #ff4d4f;
}

/* 表格样式优化 */
:deep(.ant-table-tbody > tr > td) {
  vertical-align: middle;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}
</style>
