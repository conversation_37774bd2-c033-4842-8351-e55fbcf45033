<template>
  <div class="test-page">
    <h1>表格合并组件测试页面</h1>
    
    <!-- 测试按钮 -->
    <div class="test-controls">
      <a-space>
        <a-button type="primary" @click="showBasicDemo = !showBasicDemo">
          {{ showBasicDemo ? '隐藏' : '显示' }} 基础组件
        </a-button>
        <a-button type="primary" @click="showFullDemo = !showFullDemo">
          {{ showFullDemo ? '隐藏' : '显示' }} 完整演示
        </a-button>
        <a-button @click="testDataStructure">测试数据结构</a-button>
        <a-button @click="testMergeLogic">测试合并逻辑</a-button>
      </a-space>
    </div>

    <!-- 基础组件测试 -->
    <a-card v-if="showBasicDemo" title="基础表格合并组件测试" style="margin: 20px 0;">
      <TableMergeDemo />
    </a-card>

    <!-- 完整演示测试 -->
    <a-card v-if="showFullDemo" title="完整演示页面测试" style="margin: 20px 0;">
      <TableMergeDemoPage />
    </a-card>

    <!-- 测试结果显示 -->
    <a-card v-if="testResults.length > 0" title="测试结果" style="margin: 20px 0;">
      <a-list :data-source="testResults" size="small">
        <template #renderItem="{ item }">
          <a-list-item>
            <a-list-item-meta>
              <template #title>
                <span :class="item.success ? 'test-success' : 'test-error'">
                  {{ item.success ? '✅' : '❌' }} {{ item.title }}
                </span>
              </template>
              <template #description>
                {{ item.description }}
              </template>
            </a-list-item-meta>
          </a-list-item>
        </template>
      </a-list>
    </a-card>

    <!-- 数据结构预览 -->
    <a-card v-if="showDataPreview" title="数据结构预览" style="margin: 20px 0;">
      <a-tabs>
        <a-tab-pane key="original" tab="原始数据">
          <pre class="json-preview">{{ JSON.stringify(originalData, null, 2) }}</pre>
        </a-tab-pane>
        <a-tab-pane key="transformed" tab="转换后数据">
          <pre class="json-preview">{{ JSON.stringify(transformedData, null, 2) }}</pre>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import TableMergeDemo from './TableMergeDemo.vue'
import TableMergeDemoPage from './TableMergeDemoPage.vue'
import { tableData } from './mockTable.js'

// 响应式数据
const showBasicDemo = ref(false)
const showFullDemo = ref(false)
const showDataPreview = ref(false)
const testResults = ref([])
const originalData = ref(tableData)
const transformedData = ref([])

/**
 * 数据转换函数（复制自TableMergeDemo组件）
 */
const transformTableData = (originalData) => {
  const flatData = []
  
  originalData.forEach((firstLevel) => {
    if (firstLevel.secondIndicators && firstLevel.secondIndicators.length > 0) {
      firstLevel.secondIndicators.forEach((secondLevel) => {
        const indicatorScore = secondLevel.indicatorScore || {}
        
        flatData.push({
          firstIndicatorId: firstLevel.id,
          firstIndicatorName: firstLevel.name,
          secondIndicatorId: secondLevel.id,
          secondIndicatorName: secondLevel.name,
          content: indicatorScore.content || '',
          minScore: indicatorScore.minScore || 0,
          maxScore: indicatorScore.maxScore || 100,
          scoreCardName: indicatorScore.scoreCardName || '',
          evalScoreTypeList: indicatorScore.evalScoreTypeList || [],
          othersIndicatorScore: indicatorScore.othersIndicatorScore,
          thisIndicatorScore: indicatorScore.thisIndicatorScore,
          totalIndicatorScore: indicatorScore.totalIndicatorScore,
          comment: indicatorScore.comment || '',
          imgPaths: indicatorScore.imgPaths || '',
          videoPaths: indicatorScore.videoPaths || ''
        })
      })
    }
  })
  
  return flatData
}

/**
 * 测试数据结构
 */
const testDataStructure = () => {
  const results = []
  
  try {
    // 测试原始数据结构
    if (Array.isArray(originalData.value) && originalData.value.length > 0) {
      results.push({
        success: true,
        title: '原始数据结构检查',
        description: `数据包含 ${originalData.value.length} 个一级指标`
      })
    } else {
      results.push({
        success: false,
        title: '原始数据结构检查',
        description: '原始数据为空或格式错误'
      })
    }

    // 测试数据转换
    const transformed = transformTableData(originalData.value)
    transformedData.value = transformed
    
    if (transformed.length > 0) {
      results.push({
        success: true,
        title: '数据转换测试',
        description: `成功转换为 ${transformed.length} 行表格数据`
      })
    } else {
      results.push({
        success: false,
        title: '数据转换测试',
        description: '数据转换失败，结果为空'
      })
    }

    // 测试必要字段
    const requiredFields = ['firstIndicatorId', 'firstIndicatorName', 'secondIndicatorId', 'secondIndicatorName']
    const hasAllFields = transformed.every(item => 
      requiredFields.every(field => item.hasOwnProperty(field))
    )
    
    if (hasAllFields) {
      results.push({
        success: true,
        title: '必要字段检查',
        description: '所有必要字段都存在'
      })
    } else {
      results.push({
        success: false,
        title: '必要字段检查',
        description: '缺少必要字段'
      })
    }

    showDataPreview.value = true
    
  } catch (error) {
    results.push({
      success: false,
      title: '数据结构测试异常',
      description: error.message
    })
  }
  
  testResults.value = results
  message.info(`数据结构测试完成，共 ${results.length} 项测试`)
}

/**
 * 测试合并逻辑
 */
const testMergeLogic = () => {
  const results = []
  
  try {
    const data = transformedData.value.length > 0 ? transformedData.value : transformTableData(originalData.value)
    
    // 测试一级指标分组
    const firstIndicatorGroups = {}
    data.forEach((item, index) => {
      const id = item.firstIndicatorId
      if (!firstIndicatorGroups[id]) {
        firstIndicatorGroups[id] = []
      }
      firstIndicatorGroups[id].push({ item, index })
    })
    
    const groupCount = Object.keys(firstIndicatorGroups).length
    const totalRows = data.length
    
    results.push({
      success: true,
      title: '一级指标分组',
      description: `${totalRows} 行数据分为 ${groupCount} 个一级指标组`
    })

    // 测试合并逻辑
    let mergeTestPassed = true
    let mergeDetails = []
    
    Object.entries(firstIndicatorGroups).forEach(([id, group]) => {
      const groupSize = group.length
      const firstIndex = group[0].index
      
      // 模拟customCell逻辑
      for (let i = 0; i < group.length; i++) {
        const currentIndex = group[i].index
        let expectedRowSpan = 0
        
        if (i === 0) {
          // 第一行应该显示并合并
          expectedRowSpan = groupSize
        } else {
          // 后续行应该隐藏
          expectedRowSpan = 0
        }
        
        mergeDetails.push({
          indicatorId: id,
          rowIndex: currentIndex,
          groupPosition: i,
          expectedRowSpan
        })
      }
    })
    
    if (mergeTestPassed) {
      results.push({
        success: true,
        title: '合并逻辑测试',
        description: `合并逻辑正确，共 ${mergeDetails.length} 个单元格`
      })
    } else {
      results.push({
        success: false,
        title: '合并逻辑测试',
        description: '合并逻辑存在问题'
      })
    }

    // 测试数据完整性
    const hasScoreData = data.some(item => 
      item.minScore !== undefined && item.maxScore !== undefined
    )
    
    if (hasScoreData) {
      results.push({
        success: true,
        title: '评分数据检查',
        description: '包含有效的评分范围数据'
      })
    } else {
      results.push({
        success: false,
        title: '评分数据检查',
        description: '缺少评分范围数据'
      })
    }
    
  } catch (error) {
    results.push({
      success: false,
      title: '合并逻辑测试异常',
      description: error.message
    })
  }
  
  testResults.value = [...testResults.value, ...results]
  message.info(`合并逻辑测试完成，新增 ${results.length} 项测试`)
}

// 组件挂载时自动运行基础测试
onMounted(() => {
  message.info('测试页面已加载，可以开始测试各项功能')
})
</script>

<style scoped>
.test-page {
  padding: 20px;
  background: #f0f2f5;
  min-height: 100vh;
}

.test-controls {
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.json-preview {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  font-size: 12px;
  line-height: 1.45;
  overflow-x: auto;
  max-height: 400px;
  overflow-y: auto;
}

.test-success {
  color: #52c41a;
  font-weight: 500;
}

.test-error {
  color: #ff4d4f;
  font-weight: 500;
}

/* 卡片样式 */
.ant-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border-radius: 8px;
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

.ant-card-head-title {
  font-weight: 600;
  color: #1890ff;
}
</style>
