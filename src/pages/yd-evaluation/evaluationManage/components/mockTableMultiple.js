/**
 * 多层级表格合并测试数据
 * 数据结构：一级指标 → 二级指标 → 多个评分标准
 */

let multipleTableData = [
    {
        activityId: null,
        createTime: null,
        id: '1951522258897997824',
        name: '学习态度评价',
        pid: null,
        ruleId: null,
        schoolId: null,
        secondIndicators: [
            {
                activityId: '1951521822489055234',
                createTime: null,
                id: '1951522437843821496',
                name: '课堂参与度',
                pid: null,
                ruleId: '1951522258881220608',
                schoolId: '1516232248549449729',
                // indicatorScore 现在是数组，包含多个评分标准
                indicatorScore: [
                    {
                        activityId: null,
                        approveScore: null,
                        comment: '',
                        content: '积极参与课堂讨论，主动回答问题',
                        createBy: '',
                        createTime: null,
                        evalScoreType: 'text',
                        evalScoreTypeList: ['text'],
                        evaluatePersonNum: 0,
                        id: '1951522258860249090',
                        imgPaths: '',
                        indicatorId: null,
                        isOthersData: true,
                        maxScore: 10,
                        minRatingStep: '',
                        minScore: 8,
                        othersIndicatorScore: 9,
                        othersTotalScore: 0,
                        remark: '',
                        rulePersonId: '1951522436363232781',
                        schoolId: null,
                        scoreCardId: 540485598,
                        scoreCardName: '课堂表现积分卡',
                        scoreRecordList: [],
                        thisIndicatorScore: null,
                        totalIndicatorScore: null,
                        updateBy: '',
                        updateTime: null,
                        versionStamp: null,
                        videoPaths: '',
                    },
                    {
                        activityId: null,
                        approveScore: null,
                        comment: '',
                        content: '认真听讲，做好笔记',
                        createBy: '',
                        createTime: null,
                        evalScoreType: 'text,image',
                        evalScoreTypeList: ['text', 'image'],
                        evaluatePersonNum: 0,
                        id: '1951522258860249091',
                        imgPaths: '',
                        indicatorId: null,
                        isOthersData: true,
                        maxScore: 10,
                        minRatingStep: '',
                        minScore: 6,
                        othersIndicatorScore: 8,
                        othersTotalScore: 0,
                        remark: '',
                        rulePersonId: '1951522436363232781',
                        schoolId: null,
                        scoreCardId: 540485599,
                        scoreCardName: '听课质量积分卡',
                        scoreRecordList: [],
                        thisIndicatorScore: null,
                        totalIndicatorScore: null,
                        updateBy: '',
                        updateTime: null,
                        versionStamp: null,
                        videoPaths: '',
                    },
                    {
                        activityId: null,
                        approveScore: null,
                        comment: '',
                        content: '课堂纪律良好，不干扰他人',
                        createBy: '',
                        createTime: null,
                        evalScoreType: 'text',
                        evalScoreTypeList: ['text'],
                        evaluatePersonNum: 0,
                        id: '1951522258860249092',
                        imgPaths: '',
                        indicatorId: null,
                        isOthersData: true,
                        maxScore: 5,
                        minRatingStep: '',
                        minScore: 3,
                        othersIndicatorScore: 4,
                        othersTotalScore: 0,
                        remark: '',
                        rulePersonId: '1951522436363232781',
                        schoolId: null,
                        scoreCardId: 540485600,
                        scoreCardName: '纪律表现积分卡',
                        scoreRecordList: [],
                        thisIndicatorScore: null,
                        totalIndicatorScore: null,
                        updateBy: '',
                        updateTime: null,
                        versionStamp: null,
                        videoPaths: '',
                    }
                ],
                valuers: [],
            },
            {
                activityId: '1951521822489055234',
                createTime: null,
                id: '1951522437843821497',
                name: '作业完成质量',
                pid: null,
                ruleId: '1951522258881220608',
                schoolId: '1516232248549449729',
                indicatorScore: [
                    {
                        activityId: null,
                        approveScore: null,
                        comment: '',
                        content: '按时提交作业，无迟交现象',
                        createBy: '',
                        createTime: null,
                        evalScoreType: 'text',
                        evalScoreTypeList: ['text'],
                        evaluatePersonNum: 0,
                        id: '1951522258860249093',
                        imgPaths: '',
                        indicatorId: null,
                        isOthersData: true,
                        maxScore: 15,
                        minRatingStep: '',
                        minScore: 10,
                        othersIndicatorScore: 12,
                        othersTotalScore: 0,
                        remark: '',
                        rulePersonId: '1951522436363232781',
                        schoolId: null,
                        scoreCardId: 540485601,
                        scoreCardName: '作业及时性积分卡',
                        scoreRecordList: [],
                        thisIndicatorScore: null,
                        totalIndicatorScore: null,
                        updateBy: '',
                        updateTime: null,
                        versionStamp: null,
                        videoPaths: '',
                    },
                    {
                        activityId: null,
                        approveScore: null,
                        comment: '',
                        content: '作业内容完整，答案正确率高',
                        createBy: '',
                        createTime: null,
                        evalScoreType: 'text,image',
                        evalScoreTypeList: ['text', 'image'],
                        evaluatePersonNum: 0,
                        id: '1951522258860249094',
                        imgPaths: '',
                        indicatorId: null,
                        isOthersData: true,
                        maxScore: 20,
                        minRatingStep: '',
                        minScore: 15,
                        othersIndicatorScore: 18,
                        othersTotalScore: 0,
                        remark: '',
                        rulePersonId: '1951522436363232781',
                        schoolId: null,
                        scoreCardId: 540485602,
                        scoreCardName: '作业质量积分卡',
                        scoreRecordList: [],
                        thisIndicatorScore: null,
                        totalIndicatorScore: null,
                        updateBy: '',
                        updateTime: null,
                        versionStamp: null,
                        videoPaths: '',
                    }
                ],
                valuers: [],
            }
        ],
        valuers: [],
    },
    {
        activityId: null,
        createTime: null,
        id: '1951522258461790208',
        name: '学习能力评价',
        pid: null,
        ruleId: null,
        schoolId: null,
        secondIndicators: [
            {
                activityId: '1951521822489055234',
                createTime: null,
                id: '1951522436908492750',
                name: '理解能力',
                pid: null,
                ruleId: '1951522258327572480',
                schoolId: '1516232248549449729',
                indicatorScore: [
                    {
                        activityId: null,
                        approveScore: null,
                        comment: '',
                        content: '能够快速理解新概念和知识点',
                        createBy: '',
                        createTime: null,
                        evalScoreType: 'text,video',
                        evalScoreTypeList: ['text', 'video'],
                        evaluatePersonNum: 0,
                        id: '1951522258797334529',
                        imgPaths: '',
                        indicatorId: null,
                        isOthersData: true,
                        maxScore: 25,
                        minRatingStep: '',
                        minScore: 20,
                        othersIndicatorScore: 22,
                        othersTotalScore: 0,
                        remark: '',
                        rulePersonId: '1951522436363232781',
                        schoolId: null,
                        scoreCardId: 19273171,
                        scoreCardName: '理解能力积分卡',
                        scoreRecordList: [],
                        thisIndicatorScore: null,
                        totalIndicatorScore: null,
                        updateBy: '',
                        updateTime: null,
                        versionStamp: null,
                        videoPaths: '',
                    },
                    {
                        activityId: null,
                        approveScore: null,
                        comment: '',
                        content: '能够举一反三，灵活运用知识',
                        createBy: '',
                        createTime: null,
                        evalScoreType: 'text,image,video',
                        evalScoreTypeList: ['text', 'image', 'video'],
                        evaluatePersonNum: 0,
                        id: '1951522258797334530',
                        imgPaths: '',
                        indicatorId: null,
                        isOthersData: true,
                        maxScore: 30,
                        minRatingStep: '',
                        minScore: 25,
                        othersIndicatorScore: 27,
                        othersTotalScore: 0,
                        remark: '',
                        rulePersonId: '1951522436363232781',
                        schoolId: null,
                        scoreCardId: 19273172,
                        scoreCardName: '应用能力积分卡',
                        scoreRecordList: [],
                        thisIndicatorScore: null,
                        totalIndicatorScore: null,
                        updateBy: '',
                        updateTime: null,
                        versionStamp: null,
                        videoPaths: '',
                    }
                ],
                valuers: [],
            },
            {
                activityId: '1951521822489055234',
                createTime: null,
                id: '1951522436363232897',
                name: '创新思维',
                pid: null,
                ruleId: '1951522258327572480',
                schoolId: '1516232248549449729',
                indicatorScore: [
                    {
                        activityId: null,
                        approveScore: null,
                        comment: '',
                        content: '具有独特的思考角度和见解',
                        createBy: '',
                        createTime: null,
                        evalScoreType: 'text',
                        evalScoreTypeList: ['text'],
                        evaluatePersonNum: 0,
                        id: '1951522258407264258',
                        imgPaths: '',
                        indicatorId: null,
                        isOthersData: true,
                        maxScore: 20,
                        minRatingStep: '',
                        minScore: 15,
                        othersIndicatorScore: 17,
                        othersTotalScore: 0,
                        remark: '',
                        rulePersonId: '1951522436363232781',
                        schoolId: null,
                        scoreCardId: 185965,
                        scoreCardName: '创新思维积分卡',
                        scoreRecordList: [],
                        thisIndicatorScore: null,
                        totalIndicatorScore: null,
                        updateBy: '',
                        updateTime: null,
                        versionStamp: null,
                        videoPaths: '',
                    },
                    {
                        activityId: null,
                        approveScore: null,
                        comment: '',
                        content: '能够提出创新性的解决方案',
                        createBy: '',
                        createTime: null,
                        evalScoreType: 'text,image',
                        evalScoreTypeList: ['text', 'image'],
                        evaluatePersonNum: 0,
                        id: '1951522258407264259',
                        imgPaths: '',
                        indicatorId: null,
                        isOthersData: true,
                        maxScore: 25,
                        minRatingStep: '',
                        minScore: 20,
                        othersIndicatorScore: 23,
                        othersTotalScore: 0,
                        remark: '',
                        rulePersonId: '1951522436363232781',
                        schoolId: null,
                        scoreCardId: 185966,
                        scoreCardName: '解决方案积分卡',
                        scoreRecordList: [],
                        thisIndicatorScore: null,
                        totalIndicatorScore: null,
                        updateBy: '',
                        updateTime: null,
                        versionStamp: null,
                        videoPaths: '',
                    },
                    {
                        activityId: null,
                        approveScore: null,
                        comment: '',
                        content: '勇于尝试新方法，不怕失败',
                        createBy: '',
                        createTime: null,
                        evalScoreType: 'text,video',
                        evalScoreTypeList: ['text', 'video'],
                        evaluatePersonNum: 0,
                        id: '1951522258407264260',
                        imgPaths: '',
                        indicatorId: null,
                        isOthersData: true,
                        maxScore: 15,
                        minRatingStep: '',
                        minScore: 10,
                        othersIndicatorScore: 13,
                        othersTotalScore: 0,
                        remark: '',
                        rulePersonId: '1951522436363232781',
                        schoolId: null,
                        scoreCardId: 185967,
                        scoreCardName: '探索精神积分卡',
                        scoreRecordList: [],
                        thisIndicatorScore: null,
                        totalIndicatorScore: null,
                        updateBy: '',
                        updateTime: null,
                        versionStamp: null,
                        videoPaths: '',
                    }
                ],
                valuers: [],
            }
        ],
        valuers: [],
    }
]

// 表格列配置
const multipleColumns = [
    {
        title: '一级指标',
        dataIndex: 'firstIndicatorName',
        key: 'firstIndicatorName',
    },
    {
        title: '二级指标',
        dataIndex: 'secondIndicatorName',
        key: 'secondIndicatorName',
    },
    {
        title: '评分标准',
        dataIndex: 'content',
        key: 'content',
    },
    {
        title: '评分范围',
        dataIndex: 'scoreRange',
        key: 'scoreRange',
    },
    {
        title: '积分卡',
        dataIndex: 'scoreCardName',
        key: 'scoreCardName',
    },
    {
        title: '评分类型',
        dataIndex: 'evalScoreTypeList',
        key: 'evalScoreTypeList',
    },
    {
        title: '他人评分',
        dataIndex: 'othersIndicatorScore',
        key: 'othersIndicatorScore',
    },
    {
        title: '本次评分',
        dataIndex: 'thisIndicatorScore',
        key: 'thisIndicatorScore',
    },
    {
        title: '最后得分',
        dataIndex: 'totalIndicatorScore',
        key: 'totalIndicatorScore',
    },
    {
        title: '评语',
        dataIndex: 'comment',
        key: 'comment',
    },
    {
        title: '图片/视频',
        dataIndex: 'mediaPaths',
        key: 'mediaPaths',
    },
]

export { multipleTableData, multipleColumns }
