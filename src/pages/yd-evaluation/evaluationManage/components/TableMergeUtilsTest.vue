<template>
  <div class="table-merge-utils-test">
    <h1>表格合并工具库测试</h1>
    
    <!-- 测试控制面板 -->
    <a-card class="test-controls" title="🧪 测试控制面板">
      <a-space wrap>
        <a-button type="primary" @click="runAllTests">
          <template #icon><PlayCircleOutlined /></template>
          运行全部测试
        </a-button>
        <a-button @click="testBasicMerge">基础合并测试</a-button>
        <a-button @click="testMultiFieldMerge">多字段合并测试</a-button>
        <a-button @click="testHierarchyMerge">层级合并测试</a-button>
        <a-button @click="testConditionalMerge">条件合并测试</a-button>
        <a-button @click="testGroupMerge">分组合并测试</a-button>
        <a-button @click="testPerformance">性能测试</a-button>
        <a-button @click="clearResults">清空结果</a-button>
      </a-space>
    </a-card>

    <!-- 测试结果 -->
    <a-card v-if="testResults.length > 0" class="test-results" title="📊 测试结果">
      <a-list :data-source="testResults" size="small">
        <template #renderItem="{ item }">
          <a-list-item>
            <a-list-item-meta>
              <template #avatar>
                <a-avatar :style="{ backgroundColor: item.success ? '#52c41a' : '#ff4d4f' }">
                  {{ item.success ? '✓' : '✗' }}
                </a-avatar>
              </template>
              <template #title>
                <span :class="item.success ? 'test-success' : 'test-error'">
                  {{ item.title }}
                </span>
              </template>
              <template #description>
                <div>
                  <p>{{ item.description }}</p>
                  <div v-if="item.details" class="test-details">
                    <a-tag v-for="(value, key) in item.details" :key="key" color="blue">
                      {{ key }}: {{ value }}
                    </a-tag>
                  </div>
                </div>
              </template>
            </a-list-item-meta>
            <template #actions>
              <span class="test-time">{{ item.timestamp }}</span>
            </template>
          </a-list-item>
        </template>
      </a-list>
    </a-card>

    <!-- 示例展示区域 -->
    <div class="examples-section">
      <!-- 基础合并示例 -->
      <a-card v-if="showBasicExample" class="example-card" title="📋 基础合并示例">
        <a-table 
          :dataSource="basicExample.data" 
          :columns="basicExample.columns"
          :pagination="false"
          bordered
          size="small"
        />
      </a-card>

      <!-- 多字段合并示例 -->
      <a-card v-if="showMultiFieldExample" class="example-card" title="🔗 多字段合并示例">
        <a-table 
          :dataSource="multiFieldExample.data" 
          :columns="multiFieldExample.columns"
          :pagination="false"
          bordered
          size="small"
        />
      </a-card>

      <!-- 层级合并示例 -->
      <a-card v-if="showHierarchyExample" class="example-card" title="🏗️ 层级合并示例">
        <a-table 
          :dataSource="hierarchyExample.data" 
          :columns="hierarchyExample.columns"
          :pagination="false"
          bordered
          size="small"
          :scroll="{ x: 800 }"
        />
      </a-card>

      <!-- 条件合并示例 -->
      <a-card v-if="showConditionalExample" class="example-card" title="⚡ 条件合并示例">
        <a-table 
          :dataSource="conditionalExample.data" 
          :columns="conditionalExample.columns"
          :pagination="false"
          bordered
          size="small"
        />
      </a-card>

      <!-- 分组合并示例 -->
      <a-card v-if="showGroupExample" class="example-card" title="👥 分组合并示例">
        <a-table 
          :dataSource="groupExample.data" 
          :columns="groupExample.columns"
          :pagination="false"
          bordered
          size="small"
        />
      </a-card>

      <!-- 完整应用示例 -->
      <a-card v-if="showCompleteExample" class="example-card" title="🎯 完整应用示例">
        <a-table 
          :dataSource="completeExample.data" 
          :columns="completeExample.columns"
          :pagination="false"
          bordered
          size="small"
          :scroll="{ x: 1200 }"
        />
      </a-card>
    </div>

    <!-- 性能测试结果 -->
    <a-card v-if="performanceResults.length > 0" class="performance-results" title="⚡ 性能测试结果">
      <a-table 
        :dataSource="performanceResults" 
        :columns="performanceColumns"
        :pagination="false"
        size="small"
      />
    </a-card>

    <!-- API 使用示例 -->
    <a-card class="api-examples" title="📚 API 使用示例">
      <a-tabs>
        <a-tab-pane key="basic" tab="基础API">
          <pre class="code-example">{{ basicApiExample }}</pre>
        </a-tab-pane>
        <a-tab-pane key="advanced" tab="高级API">
          <pre class="code-example">{{ advancedApiExample }}</pre>
        </a-tab-pane>
        <a-tab-pane key="performance" tab="性能优化">
          <pre class="code-example">{{ performanceApiExample }}</pre>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PlayCircleOutlined } from '@ant-design/icons-vue'

// 导入工具库和示例
import {
  calculateRowSpan,
  calculateMultiFieldRowSpan,
  calculateHierarchyRowSpan,
  calculateConditionalRowSpan,
  calculateGroupRowSpan,
  calculateSmartRowSpan,
  createMergeColumn,
  generateMergeConfigs,
  DataProcessor,
  PerformanceMonitor
} from '@/utils/tableMergeUtils.js'

import {
  BasicMergeExample,
  MultiFieldMergeExample,
  HierarchyMergeExample,
  ConditionalMergeExample,
  GroupMergeExample,
  CompleteApplicationExample,
  PerformanceExample
} from '@/utils/tableMergeExamples.js'

// 响应式数据
const testResults = ref([])
const performanceResults = ref([])
const showBasicExample = ref(false)
const showMultiFieldExample = ref(false)
const showHierarchyExample = ref(false)
const showConditionalExample = ref(false)
const showGroupExample = ref(false)
const showCompleteExample = ref(false)

// 示例数据
const basicExample = ref({})
const multiFieldExample = ref({})
const hierarchyExample = ref({})
const conditionalExample = ref({})
const groupExample = ref({})
const completeExample = ref({})

// 性能测试表格列
const performanceColumns = [
  { title: '测试项目', dataIndex: 'testName', key: 'testName' },
  { title: '数据量', dataIndex: 'dataSize', key: 'dataSize', align: 'center' },
  { title: '执行时间(ms)', dataIndex: 'duration', key: 'duration', align: 'center' },
  { title: '平均时间(ms)', dataIndex: 'avgTime', key: 'avgTime', align: 'center' },
  { title: '状态', dataIndex: 'status', key: 'status', align: 'center' }
]

// API 示例代码
const basicApiExample = `// 基础合并使用
import { createMergeColumn } from '@/utils/tableMergeUtils.js'

const columns = [
  {
    title: '类别',
    dataIndex: 'category',
    customCell: createMergeColumn('category', 'category', {
      type: 'basic',
      dataSource: tableData
    })
  }
]

// 自定义比较函数
const customColumn = {
  title: '自定义合并',
  dataIndex: 'field',
  customCell: createMergeColumn('field', (record, index) => {
    return record.category + '-' + record.type
  }, {
    type: 'basic',
    dataSource: tableData
  })
}`

const advancedApiExample = `// 多层级合并
import { DataProcessor, createMergeColumn } from '@/utils/tableMergeUtils.js'

// 1. 数据扁平化
const flatData = DataProcessor.flattenNestedData(nestedData, {
  levelConfigs: [
    { childrenField: 'children', idField: 'level1Id', nameField: 'level1Name' },
    { childrenField: 'children', idField: 'level2Id', nameField: 'level2Name' },
    { idField: 'level3Id', nameField: 'level3Name' }
  ]
})

// 2. 层级合并配置
const levelFields = ['level1Id', 'level2Id', 'level3Id']

const columns = [
  {
    title: '一级指标',
    dataIndex: 'level1Name',
    customCell: createMergeColumn('level1Name', levelFields, {
      type: 'hierarchy',
      targetLevel: 0,
      dataSource: flatData
    })
  },
  {
    title: '二级指标',
    dataIndex: 'level2Name',
    customCell: createMergeColumn('level2Name', levelFields, {
      type: 'hierarchy',
      targetLevel: 1,
      dataSource: flatData
    })
  }
]`

const performanceApiExample = `// 性能优化配置
import { createMergeColumn, PerformanceMonitor } from '@/utils/tableMergeUtils.js'

// 1. 启用缓存
const optimizedColumn = {
  customCell: createMergeColumn('field', 'compareField', {
    cacheResults: true,  // 启用缓存
    maxSpan: 100,       // 限制最大合并行数
    dataSource: tableData
  })
}

// 2. 性能监控
PerformanceMonitor.start('mergeCalculation')
const result = calculateComplexMerge(data)
const duration = PerformanceMonitor.end('mergeCalculation')

// 3. 批量配置
const mergeConfigs = [
  { field: 'category', type: 'basic' },
  { field: ['field1', 'field2'], type: 'multi' }
]
const mergeResults = generateMergeConfigs(data, mergeConfigs)`

/**
 * 添加测试结果
 */
const addTestResult = (title, description, success, details = null) => {
  testResults.value.push({
    title,
    description,
    success,
    details,
    timestamp: new Date().toLocaleTimeString()
  })
}

/**
 * 测试基础合并功能
 */
const testBasicMerge = () => {
  try {
    const example = BasicMergeExample.simpleFieldMerge()
    basicExample.value = example
    showBasicExample.value = true

    // 测试计算函数
    const testData = example.data
    const rowSpan = calculateRowSpan(testData, 0, 'category')
    
    addTestResult(
      '基础合并测试',
      `成功计算行合并，第一行合并数: ${rowSpan}`,
      true,
      { '数据行数': testData.length, '合并行数': rowSpan }
    )
  } catch (error) {
    addTestResult('基础合并测试', `测试失败: ${error.message}`, false)
  }
}

/**
 * 测试多字段合并功能
 */
const testMultiFieldMerge = () => {
  try {
    const example = MultiFieldMergeExample.andOperatorMerge()
    multiFieldExample.value = example
    showMultiFieldExample.value = true

    const testData = example.data
    const rowSpan = calculateMultiFieldRowSpan(testData, 0, ['category', 'type'])
    
    addTestResult(
      '多字段合并测试',
      `成功计算多字段合并，第一行合并数: ${rowSpan}`,
      true,
      { '字段数': 2, '合并行数': rowSpan }
    )
  } catch (error) {
    addTestResult('多字段合并测试', `测试失败: ${error.message}`, false)
  }
}

/**
 * 测试层级合并功能
 */
const testHierarchyMerge = () => {
  try {
    const example = HierarchyMergeExample.threeLayerMerge()
    hierarchyExample.value = example
    showHierarchyExample.value = true

    const testData = example.data
    const levelFields = ['firstLevelId', 'secondLevelId', 'standardId']
    const firstLevelSpan = calculateHierarchyRowSpan(testData, 0, levelFields, 0)
    const secondLevelSpan = calculateHierarchyRowSpan(testData, 0, levelFields, 1)
    
    addTestResult(
      '层级合并测试',
      `成功计算层级合并`,
      true,
      { 
        '数据行数': testData.length,
        '一级合并数': firstLevelSpan,
        '二级合并数': secondLevelSpan
      }
    )
  } catch (error) {
    addTestResult('层级合并测试', `测试失败: ${error.message}`, false)
  }
}

/**
 * 测试条件合并功能
 */
const testConditionalMerge = () => {
  try {
    const example = ConditionalMergeExample.statusConditionMerge()
    conditionalExample.value = example
    showConditionalExample.value = true

    const testData = example.data
    const rowSpan = calculateConditionalRowSpan(
      testData, 
      0, 
      'category',
      (record) => record.status === 'active'
    )
    
    addTestResult(
      '条件合并测试',
      `成功计算条件合并，第一行合并数: ${rowSpan}`,
      true,
      { '条件': 'status === active', '合并行数': rowSpan }
    )
  } catch (error) {
    addTestResult('条件合并测试', `测试失败: ${error.message}`, false)
  }
}

/**
 * 测试分组合并功能
 */
const testGroupMerge = () => {
  try {
    const example = GroupMergeExample.departmentPositionMerge()
    groupExample.value = example
    showGroupExample.value = true

    const testData = example.data
    const rowSpan = calculateGroupRowSpan(testData, 0, 'department', 'position')
    
    addTestResult(
      '分组合并测试',
      `成功计算分组合并，第一行合并数: ${rowSpan}`,
      true,
      { '分组字段': 'department', '合并字段': 'position', '合并行数': rowSpan }
    )
  } catch (error) {
    addTestResult('分组合并测试', `测试失败: ${error.message}`, false)
  }
}

/**
 * 测试性能
 */
const testPerformance = () => {
  try {
    const results = PerformanceExample.largeDataPerformance()
    
    const performanceData = []
    Object.entries(results).forEach(([size, result]) => {
      performanceData.push({
        testName: `基础合并-${size}条`,
        dataSize: size,
        duration: result.basic.toFixed(2),
        avgTime: (result.basic / size).toFixed(4),
        status: '完成'
      })
      performanceData.push({
        testName: `多字段合并-${size}条`,
        dataSize: size,
        duration: result.multi.toFixed(2),
        avgTime: (result.multi / size).toFixed(4),
        status: '完成'
      })
      performanceData.push({
        testName: `智能合并-${size}条`,
        dataSize: size,
        duration: result.smart.toFixed(2),
        avgTime: (result.smart / size).toFixed(4),
        status: '完成'
      })
    })

    performanceResults.value = performanceData
    
    addTestResult(
      '性能测试',
      `完成多种数据量的性能测试`,
      true,
      { '测试场景': performanceData.length, '最大数据量': '5000条' }
    )
  } catch (error) {
    addTestResult('性能测试', `测试失败: ${error.message}`, false)
  }
}

/**
 * 运行全部测试
 */
const runAllTests = async () => {
  clearResults()
  message.info('开始运行全部测试...')
  
  const tests = [
    testBasicMerge,
    testMultiFieldMerge,
    testHierarchyMerge,
    testConditionalMerge,
    testGroupMerge,
    testPerformance
  ]

  for (const test of tests) {
    await new Promise(resolve => setTimeout(resolve, 100))
    test()
  }

  // 显示完整应用示例
  const completeApp = CompleteApplicationExample.educationEvaluationSystem()
  completeExample.value = completeApp
  showCompleteExample.value = true
  
  message.success('全部测试完成！')
}

/**
 * 清空测试结果
 */
const clearResults = () => {
  testResults.value = []
  performanceResults.value = []
  showBasicExample.value = false
  showMultiFieldExample.value = false
  showHierarchyExample.value = false
  showConditionalExample.value = false
  showGroupExample.value = false
  showCompleteExample.value = false
}

// 组件挂载时初始化
onMounted(() => {
  message.info('表格合并工具库测试页面已加载')
})
</script>

<style scoped>
.table-merge-utils-test {
  padding: 20px;
  background: #f0f2f5;
  min-height: 100vh;
}

.test-controls {
  margin-bottom: 20px;
}

.test-results {
  margin-bottom: 20px;
}

.test-success {
  color: #52c41a;
  font-weight: 500;
}

.test-error {
  color: #ff4d4f;
  font-weight: 500;
}

.test-details {
  margin-top: 8px;
}

.test-time {
  color: #999;
  font-size: 12px;
}

.examples-section {
  margin-bottom: 20px;
}

.example-card {
  margin-bottom: 20px;
}

.performance-results {
  margin-bottom: 20px;
}

.api-examples {
  margin-bottom: 20px;
}

.code-example {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  font-size: 12px;
  line-height: 1.45;
  overflow-x: auto;
  white-space: pre-wrap;
}

/* 表格样式优化 */
:deep(.ant-table-tbody > tr > td) {
  vertical-align: middle;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-merge-utils-test {
    padding: 16px;
  }
  
  .example-card {
    margin-bottom: 16px;
  }
}
</style>
