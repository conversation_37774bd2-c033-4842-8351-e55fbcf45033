<template>
    <div class="media-preview-test">
        <h2>媒体预览组件测试</h2>
        
        <!-- 测试按钮 -->
        <div class="test-controls">
            <a-button type="primary" @click="openPreview">打开媒体预览</a-button>
            <a-button @click="addTestMedia">添加测试媒体</a-button>
            <a-button @click="clearMedia">清空媒体</a-button>
        </div>
        
        <!-- 当前媒体列表 -->
        <div class="current-media">
            <h3>当前媒体列表 ({{ testImgPaths.length + testVideoPaths.length }} 个文件)</h3>
            <div class="media-info">
                <div v-if="testImgPaths.length > 0">
                    <strong>图片 ({{ testImgPaths.length }} 个):</strong>
                    <ul>
                        <li v-for="(img, index) in testImgPaths" :key="'img-' + index">
                            {{ img.substring(img.lastIndexOf('/') + 1) }}
                        </li>
                    </ul>
                </div>
                <div v-if="testVideoPaths.length > 0">
                    <strong>视频 ({{ testVideoPaths.length }} 个):</strong>
                    <ul>
                        <li v-for="(video, index) in testVideoPaths" :key="'video-' + index">
                            {{ video.substring(video.lastIndexOf('/') + 1) }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 功能说明 -->
        <div class="feature-description">
            <h3>新增功能</h3>
            <ul>
                <li>✅ 自定义箭头导航 (左右箭头)</li>
                <li>✅ 缩略图导航 (96x96px 缩略图)</li>
                <li>✅ 激活状态样式 (绿色边框 #00B781)</li>
                <li>✅ 响应式适配 (移动端优化)</li>
                <li>✅ 视频缩略图播放图标</li>
                <li>✅ 点击缩略图切换</li>
            </ul>
        </div>
        
        <!-- 媒体预览组件 -->
        <MediaPreviewModal
            v-model:open="previewOpen"
            :img-paths="testImgPaths"
            :video-paths="testVideoPaths"
        />
    </div>
</template>

<script setup name="MediaPreviewTest">
import MediaPreviewModal from './MediaPreviewModal.vue'

// 测试数据
const previewOpen = ref(false)
const testImgPaths = ref([
    'https://via.placeholder.com/800x600/4CAF50/white?text=Image+1',
    'https://via.placeholder.com/800x600/2196F3/white?text=Image+2',
    'https://via.placeholder.com/800x600/FF9800/white?text=Image+3',
])

const testVideoPaths = ref([
    'https://158-minio.yyide.vip/cloud-yide/eval/activity/b9f599c4b33147a4a0955bd315b6db21.mp4',
    'https://www.w3schools.com/html/mov_bbb.mp4',
])

// 更多测试媒体
const additionalImages = [
    'https://via.placeholder.com/800x600/E91E63/white?text=Image+4',
    'https://via.placeholder.com/800x600/9C27B0/white?text=Image+5',
    'https://via.placeholder.com/800x600/673AB7/white?text=Image+6',
]

const additionalVideos = [
    'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
]

// 打开预览
const openPreview = () => {
    if (testImgPaths.value.length === 0 && testVideoPaths.value.length === 0) {
        YMessage.warning('请先添加一些测试媒体')
        return
    }
    previewOpen.value = true
}

// 添加测试媒体
const addTestMedia = () => {
    // 随机添加图片或视频
    const random = Math.random()
    if (random > 0.5 && additionalImages.length > 0) {
        const img = additionalImages.shift()
        testImgPaths.value.push(img)
        YMessage.success('添加了一张图片')
    } else if (additionalVideos.length > 0) {
        const video = additionalVideos.shift()
        testVideoPaths.value.push(video)
        YMessage.success('添加了一个视频')
    } else {
        YMessage.info('没有更多测试媒体了')
    }
}

// 清空媒体
const clearMedia = () => {
    testImgPaths.value = []
    testVideoPaths.value = []
    YMessage.success('已清空所有媒体')
}
</script>

<style lang="less" scoped>
.media-preview-test {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
    
    h2 {
        color: #333;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .test-controls {
        display: flex;
        gap: 12px;
        margin-bottom: 20px;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .current-media {
        background: #f5f5f5;
        padding: 16px;
        border-radius: 8px;
        margin-bottom: 20px;
        
        h3 {
            margin-bottom: 12px;
            color: #555;
        }
        
        .media-info {
            div {
                margin-bottom: 12px;
                
                strong {
                    color: #333;
                }
                
                ul {
                    margin: 8px 0 0 20px;
                    
                    li {
                        color: #666;
                        font-size: 14px;
                        margin-bottom: 4px;
                    }
                }
            }
        }
    }
    
    .feature-description {
        background: #e6f7ff;
        padding: 16px;
        border-radius: 8px;
        border-left: 4px solid #1890ff;
        
        h3 {
            margin-bottom: 12px;
            color: #1890ff;
        }
        
        ul {
            margin: 0;
            padding-left: 20px;
            
            li {
                margin-bottom: 8px;
                color: #333;
                
                &::marker {
                    color: #52c41a;
                }
            }
        }
    }
}

// 响应式适配
@media (max-width: 768px) {
    .media-preview-test {
        padding: 10px;
        
        .test-controls {
            flex-direction: column;
            align-items: center;
        }
    }
}
</style>
