export let tableData = [
    {
        activityId: null,
        createTime: null,
        id: '1951522258897997824',
        indicatorScore: null,
        name: '我的11111',
        pid: null,
        ruleId: null,
        schoolId: null,
        secondIndicators: [
            {
                activityId: '1951521822489055234',
                createTime: null,
                id: '1951522437843821496',
                indicatorScore: {
                    activityId: null,
                    approveScore: null,
                    comment: '',
                    content: '标准新一个',
                    createBy: '',
                    createTime: null,
                    evalScoreType: 'text',
                    evalScoreTypeList: ['text'],
                    evaluatePersonNum: 0,
                    id: '1951522258860249090',
                    imgPaths: '',
                    indicatorId: null,
                    isOthersData: true,
                    maxScore: 66,
                    minRatingStep: '',
                    minScore: 44,
                    othersIndicatorScore: null,
                    othersTotalScore: 0,
                    remark: '',
                    rulePersonId: '1951522436363232781',
                    schoolId: null,
                    scoreCardId: 540485598,
                    scoreCardName: '10分积分卡',
                    scoreRecordList: [],
                    thisIndicatorScore: null,
                    totalIndicatorScore: null,
                    updateBy: '',
                    updateTime: null,
                    versionStamp: null,
                    videoPaths: '',
                },
                name: '2222',
                pid: null,
                ruleId: '1951522258881220608',
                schoolId: '1516232248549449729',
                secondIndicators: [],
                valuers: [],
            },
        ],
        valuers: [],
    },
    {
        activityId: null,
        createTime: null,
        id: '1951522258461790208',
        indicatorScore: null,
        name: '第一',
        pid: null,
        ruleId: null,
        schoolId: null,
        secondIndicators: [
            {
                activityId: '1951521822489055234',
                createTime: null,
                id: '1951522436908492750',
                indicatorScore: {
                    activityId: null,
                    approveScore: null,
                    comment: '',
                    content: '标准2',
                    createBy: '',
                    createTime: null,
                    evalScoreType: 'image,text',
                    evalScoreTypeList: ['image', 'text'],
                    evaluatePersonNum: 0,
                    id: '1951522258797334529',
                    imgPaths: '',
                    indicatorId: null,
                    isOthersData: true,
                    maxScore: 44,
                    minRatingStep: '',
                    minScore: 22,
                    othersIndicatorScore: null,
                    othersTotalScore: 0,
                    remark: '',
                    rulePersonId: '1951522436363232781',
                    schoolId: null,
                    scoreCardId: 19273171,
                    scoreCardName: '兑换积分卡',
                    scoreRecordList: [],
                    thisIndicatorScore: null,
                    totalIndicatorScore: null,
                    updateBy: '',
                    updateTime: null,
                    versionStamp: null,
                    videoPaths: '',
                },
                name: '第3',
                pid: null,
                ruleId: '1951522258327572480',
                schoolId: '1516232248549449729',
                secondIndicators: [],
                valuers: [],
            },
            {
                activityId: '1951521822489055234',
                createTime: null,
                id: '1951522436363232897',
                indicatorScore: {
                    activityId: null,
                    approveScore: null,
                    comment: '',
                    content: '标准1',
                    createBy: '',
                    createTime: null,
                    evalScoreType: 'image,video,text',
                    evalScoreTypeList: ['image', 'video', 'text'],
                    evaluatePersonNum: 0,
                    id: '1951522258407264258',
                    imgPaths: '',
                    indicatorId: null,
                    isOthersData: true,
                    maxScore: 22,
                    minRatingStep: '',
                    minScore: 11,
                    othersIndicatorScore: null,
                    othersTotalScore: 0,
                    remark: '',
                    rulePersonId: '1951522436363232781',
                    schoolId: null,
                    scoreCardId: 185965,
                    scoreCardName: '德育兑换卡',
                    scoreRecordList: [],
                    thisIndicatorScore: null,
                    totalIndicatorScore: null,
                    updateBy: '',
                    updateTime: null,
                    versionStamp: null,
                    videoPaths: '',
                },
                name: '第2',
                pid: null,
                ruleId: '1951522258327572480',
                schoolId: '1516232248549449729',
                secondIndicators: [],
                valuers: [],
            },
        ],
        valuers: [],
    },
]

export const columns = [
    {
        title: '一级指标',
        dataIndex: 'firstIndicatorName',
        key: 'firstIndicatorName',
    },
    {
        title: '二级指标',
        dataIndex: 'secondIndicatorName',

        key: 'secondIndicatorName',
    },
    {
        title: '评分标准',
        dataIndex: 'content',
        key: 'content',
    },
    {
        title: '评分范围',
        dataIndex: 'minScore',
        key: 'minScore',
    },
    {
        title: '积分卡',
        dataIndex: 'scoreCardName',
        key: 'scoreCardName',
    },
    {
        title: '他人评分',
        dataIndex: 'othersIndicatorScore',
        key: 'othersIndicatorScore',
    },
    {
        title: '本次评分',
        dataIndex: 'thisIndicatorScore',
        key: 'thisIndicatorScore',
    },
    {
        title: '最后得分',
        dataIndex: 'totalIndicatorScore',
        key: 'totalIndicatorScore',
    },
    {
        title: '评语',
        dataIndex: 'comment',
        key: 'comment',
    },
    {
        title: '图片/视频',
        dataIndex: 'Paths',
        key: 'Paths',
    },
]
