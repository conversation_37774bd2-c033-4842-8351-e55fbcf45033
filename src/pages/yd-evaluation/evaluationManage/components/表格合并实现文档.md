# 表格合并实现文档

## 概述

本文档详细说明了如何在 Vue 3 + Ant Design Vue 中实现表格行合并功能，特别是针对一级指标和二级指标的层级数据展示。

## 核心实现原理

### 1. 数据结构转换

原始数据是嵌套结构：
```javascript
// 原始数据结构
[
  {
    id: '一级指标ID',
    name: '一级指标名称',
    secondIndicators: [
      {
        id: '二级指标ID',
        name: '二级指标名称',
        indicatorScore: {
          content: '评分标准',
          minScore: 最小分数,
          maxScore: 最大分数,
          // ... 其他评分相关字段
        }
      }
    ]
  }
]
```

需要转换为扁平化结构：
```javascript
// 转换后的扁平化数据
[
  {
    firstIndicatorId: '一级指标ID',
    firstIndicatorName: '一级指标名称',
    secondIndicatorId: '二级指标ID',
    secondIndicatorName: '二级指标名称',
    content: '评分标准',
    minScore: 最小分数,
    maxScore: 最大分数,
    // ... 其他字段
  }
]
```

### 2. 行合并逻辑

使用 `customCell` 属性实现行合并：

```javascript
{
  title: '一级指标',
  dataIndex: 'firstIndicatorName',
  customCell: (record, rowIndex) => {
    // 计算需要合并的行数
    let rowSpan = 1
    
    // 向下查找相同的一级指标
    for (let i = rowIndex + 1; i < dataSource.length; i++) {
      if (dataSource[i].firstIndicatorId === record.firstIndicatorId) {
        rowSpan++
      } else {
        break
      }
    }
    
    // 如果不是第一行，则隐藏
    if (rowIndex > 0 && 
        dataSource[rowIndex - 1].firstIndicatorId === record.firstIndicatorId) {
      return { rowSpan: 0 }
    }
    
    return { rowSpan }
  }
}
```

## 关键功能实现

### 1. 数据转换函数

```javascript
const transformTableData = (originalData) => {
  const flatData = []
  
  originalData.forEach((firstLevel) => {
    if (firstLevel.secondIndicators && firstLevel.secondIndicators.length > 0) {
      firstLevel.secondIndicators.forEach((secondLevel) => {
        const indicatorScore = secondLevel.indicatorScore || {}
        
        flatData.push({
          // 一级指标信息
          firstIndicatorId: firstLevel.id,
          firstIndicatorName: firstLevel.name,
          
          // 二级指标信息
          secondIndicatorId: secondLevel.id,
          secondIndicatorName: secondLevel.name,
          
          // 评分相关信息
          content: indicatorScore.content || '',
          minScore: indicatorScore.minScore || 0,
          maxScore: indicatorScore.maxScore || 100,
          // ... 其他字段映射
        })
      })
    }
  })
  
  return flatData
}
```

### 2. 自定义单元格渲染

```vue
<template #bodyCell="{ column, record }">
  <!-- 评分范围显示 -->
  <template v-if="column.dataIndex === 'scoreRange'">
    <span>{{ record.minScore }} - {{ record.maxScore }}</span>
  </template>
  
  <!-- 评分类型标签 -->
  <template v-else-if="column.dataIndex === 'evalScoreTypeList'">
    <a-tag 
      v-for="type in record.evalScoreTypeList" 
      :key="type" 
      :color="getTypeColor(type)"
    >
      {{ getTypeText(type) }}
    </a-tag>
  </template>
  
  <!-- 评分输入框 -->
  <template v-else-if="column.dataIndex === 'thisIndicatorScore'">
    <a-input-number
      v-model:value="record.thisIndicatorScore"
      :min="record.minScore"
      :max="record.maxScore"
      @change="calculateTotalScore(record)"
    />
  </template>
</template>
```

### 3. 分数计算逻辑

```javascript
const calculateTotalScore = (record) => {
  if (record.thisIndicatorScore !== null) {
    // 如果有他人评分，计算平均值
    if (record.othersIndicatorScore !== null) {
      record.totalIndicatorScore = (
        (record.thisIndicatorScore + record.othersIndicatorScore) / 2
      ).toFixed(1)
    } else {
      record.totalIndicatorScore = record.thisIndicatorScore
    }
  } else {
    record.totalIndicatorScore = null
  }
}
```

## 样式优化

### 1. 表格样式

```css
/* 单元格垂直居中 */
:deep(.ant-table-tbody > tr > td) {
  vertical-align: middle;
}

/* 表头样式 */
:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}
```

### 2. 分数颜色标识

```css
.score-excellent { color: #52c41a; font-weight: bold; } /* 优秀：绿色 */
.score-good { color: #1890ff; font-weight: bold; }      /* 良好：蓝色 */
.score-normal { color: #faad14; }                       /* 一般：橙色 */
.score-poor { color: #ff4d4f; }                         /* 较差：红色 */
```

## 使用方法

### 1. 引入组件

```vue
<template>
  <TableMergeDemo />
</template>

<script setup>
import TableMergeDemo from './components/TableMergeDemo.vue'
</script>
```

### 2. 数据准备

确保数据符合以下结构：
- 一级指标：包含 `id` 和 `name` 字段
- 二级指标：包含 `id`、`name` 和 `indicatorScore` 字段
- 评分信息：包含评分标准、分数范围、积分卡等信息

### 3. 自定义配置

可以根据需要修改以下配置：
- 表格列定义：调整 `tableColumns` 数组
- 合并逻辑：修改 `customCell` 函数
- 数据转换：调整 `transformTableData` 函数

## 注意事项

### 1. 数据一致性
- 确保一级指标的 ID 在同一组内保持一致
- 二级指标的 ID 必须唯一
- 评分数据的类型要正确（数字类型）

### 2. 性能优化
- 对于大量数据，考虑使用虚拟滚动
- 避免在 `customCell` 中进行复杂计算
- 使用 `computed` 缓存计算结果

### 3. 用户体验
- 提供数据验证和错误提示
- 支持批量操作和快捷键
- 添加加载状态和进度指示

## 扩展功能

### 1. 导出功能
```javascript
const handleExport = () => {
  // 将表格数据导出为 Excel 或 CSV
  const exportData = tableDataSource.value.map(item => ({
    '一级指标': item.firstIndicatorName,
    '二级指标': item.secondIndicatorName,
    '评分标准': item.content,
    '评分范围': `${item.minScore}-${item.maxScore}`,
    '本次评分': item.thisIndicatorScore,
    '最后得分': item.totalIndicatorScore
  }))
  
  // 调用导出工具函数
  exportToExcel(exportData, '评分表格.xlsx')
}
```

### 2. 批量操作
```javascript
const handleBatchScore = (score) => {
  tableDataSource.value.forEach(item => {
    if (item.thisIndicatorScore === null) {
      item.thisIndicatorScore = score
      calculateTotalScore(item)
    }
  })
}
```

### 3. 数据持久化
```javascript
const saveData = async () => {
  try {
    const saveData = tableDataSource.value.map(item => ({
      secondIndicatorId: item.secondIndicatorId,
      thisIndicatorScore: item.thisIndicatorScore,
      comment: item.comment
    }))
    
    await api.saveEvaluationData(saveData)
    message.success('保存成功')
  } catch (error) {
    message.error('保存失败：' + error.message)
  }
}
```

## 总结

这个表格合并实现提供了：
1. 完整的数据转换和展示逻辑
2. 灵活的行合并算法
3. 丰富的交互功能
4. 良好的用户体验
5. 可扩展的架构设计

通过这个实现，可以轻松处理复杂的层级数据展示需求，并支持各种评分和数据操作功能。
