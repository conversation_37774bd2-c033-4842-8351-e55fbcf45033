<template>
  <div class="multiple-demo-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>多层级表格合并功能演示</h1>
      <p class="page-description">
        展示一级指标 → 二级指标 → 多个评分标准的三层数据结构合并效果。
        支持复杂的层级数据展示、多维度评分和完整的数据管理功能。
      </p>
    </div>

    <!-- 功能特性卡片 -->
    <a-row :gutter="16" class="feature-cards">
      <a-col :span="6">
        <a-card size="small" class="feature-card">
          <template #title>
            <span class="feature-title">🔗 三层合并</span>
          </template>
          <p>一级指标和二级指标智能合并，评分标准独立显示，实现清晰的层级结构。</p>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card size="small" class="feature-card">
          <template #title>
            <span class="feature-title">📊 多维评分</span>
          </template>
          <p>每个二级指标包含多个评分标准，支持细粒度的评价体系。</p>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card size="small" class="feature-card">
          <template #title>
            <span class="feature-title">🎯 智能计算</span>
          </template>
          <p>自动计算各层级得分，支持权重分配和综合评价。</p>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card size="small" class="feature-card">
          <template #title>
            <span class="feature-title">📱 响应式</span>
          </template>
          <p>完全响应式设计，支持各种屏幕尺寸和设备类型。</p>
        </a-card>
      </a-col>
    </a-row>

    <!-- 数据结构说明 -->
    <a-card class="structure-card" title="📋 数据结构说明">
      <a-row :gutter="24">
        <a-col :span="8">
          <div class="structure-item">
            <h4>🏷️ 一级指标</h4>
            <ul>
              <li>学习态度评价</li>
              <li>学习能力评价</li>
            </ul>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="structure-item">
            <h4>🎯 二级指标</h4>
            <ul>
              <li>课堂参与度</li>
              <li>作业完成质量</li>
              <li>理解能力</li>
              <li>创新思维</li>
            </ul>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="structure-item">
            <h4>📝 评分标准</h4>
            <ul>
              <li>每个二级指标包含2-3个评分标准</li>
              <li>总计{{ totalScoreStandards }}个评分标准</li>
              <li>支持不同评分类型和权重</li>
            </ul>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 操作工具栏 -->
    <a-card class="toolbar-card" size="small">
      <template #title>
        <span>🛠️ 操作工具</span>
      </template>
      <a-space wrap>
        <a-button type="primary" @click="handleQuickDemo">
          <template #icon><PlayCircleOutlined /></template>
          快速演示
        </a-button>
        <a-button @click="handleClearAll">
          <template #icon><ClearOutlined /></template>
          清空评分
        </a-button>
        <a-button @click="handleRandomFill">
          <template #icon><ExperimentOutlined /></template>
          随机填充
        </a-button>
        <a-button @click="handleCalculateStats">
          <template #icon><CalculatorOutlined /></template>
          计算统计
        </a-button>
        <a-button @click="toggleComparison">
          <template #icon><SwapOutlined /></template>
          {{ showComparison ? '隐藏' : '显示' }}对比
        </a-button>
        <a-button @click="toggleAdvanced">
          <template #icon><SettingOutlined /></template>
          {{ showAdvanced ? '隐藏' : '显示' }}高级功能
        </a-button>
      </a-space>
    </a-card>

    <!-- 统计信息面板 -->
    <a-row :gutter="16" class="stats-panel">
      <a-col :span="4">
        <a-statistic 
          title="完成率" 
          :value="completionRate" 
          suffix="%" 
          :value-style="{ color: completionRate === 100 ? '#52c41a' : '#faad14' }"
        />
      </a-col>
      <a-col :span="4">
        <a-statistic 
          title="平均分" 
          :value="averageScore" 
          :precision="1"
          :value-style="{ color: '#1890ff' }"
        />
      </a-col>
      <a-col :span="4">
        <a-statistic 
          title="最高分" 
          :value="maxScore" 
          :value-style="{ color: '#52c41a' }"
        />
      </a-col>
      <a-col :span="4">
        <a-statistic 
          title="最低分" 
          :value="minScore" 
          :value-style="{ color: '#ff4d4f' }"
        />
      </a-col>
      <a-col :span="4">
        <a-statistic 
          title="总分" 
          :value="totalScore" 
          :value-style="{ color: '#722ed1' }"
        />
      </a-col>
      <a-col :span="4">
        <a-statistic 
          title="评分项" 
          :value="totalItems" 
          :value-style="{ color: '#13c2c2' }"
        />
      </a-col>
    </a-row>

    <!-- 主要演示组件 -->
    <a-card class="main-demo-card">
      <template #title>
        <span>📊 多层级表格合并演示</span>
      </template>
      <template #extra>
        <a-space>
          <a-tag color="blue">三层合并</a-tag>
          <a-tag color="green">智能计算</a-tag>
          <a-tag color="orange">多维评分</a-tag>
        </a-space>
      </template>
      
      <TableMergeMultipleDemo ref="multipleDemoRef" @data-change="handleDataChange" />
    </a-card>

    <!-- 对比分析面板 -->
    <a-card v-if="showComparison" class="comparison-card" title="📈 数据对比分析">
      <a-row :gutter="16">
        <a-col :span="12">
          <h4>一级指标得分对比</h4>
          <div class="comparison-chart">
            <div v-for="item in firstLevelStats" :key="item.name" class="chart-item">
              <span class="chart-label">{{ item.name }}</span>
              <div class="chart-bar">
                <div 
                  class="chart-fill" 
                  :style="{ width: (item.score / item.maxScore * 100) + '%' }"
                ></div>
              </div>
              <span class="chart-value">{{ item.score }}/{{ item.maxScore }}</span>
            </div>
          </div>
        </a-col>
        <a-col :span="12">
          <h4>二级指标完成情况</h4>
          <div class="completion-stats">
            <div v-for="item in secondLevelStats" :key="item.name" class="stats-item">
              <span class="stats-label">{{ item.name }}</span>
              <a-progress 
                :percent="item.completion" 
                :status="item.completion === 100 ? 'success' : 'active'"
                size="small"
              />
            </div>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 高级功能面板 -->
    <a-card v-if="showAdvanced" class="advanced-card" title="⚙️ 高级功能">
      <a-tabs>
        <a-tab-pane key="export" tab="数据导出">
          <a-space direction="vertical" style="width: 100%">
            <a-button-group>
              <a-button @click="exportToJSON">导出JSON</a-button>
              <a-button @click="exportToCSV">导出CSV</a-button>
              <a-button @click="exportToExcel">导出Excel</a-button>
            </a-button-group>
            <a-textarea 
              v-model:value="exportData" 
              placeholder="导出数据将显示在这里..."
              :rows="6"
              readonly
            />
          </a-space>
        </a-tab-pane>
        
        <a-tab-pane key="import" tab="数据导入">
          <a-space direction="vertical" style="width: 100%">
            <a-upload-dragger @change="handleImport">
              <p class="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p class="ant-upload-hint">支持JSON格式的评分数据文件</p>
            </a-upload-dragger>
          </a-space>
        </a-tab-pane>
        
        <a-tab-pane key="settings" tab="设置">
          <a-form layout="vertical">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="评分精度">
                  <a-select v-model:value="scorePrecision">
                    <a-select-option :value="0">整数</a-select-option>
                    <a-select-option :value="1">一位小数</a-select-option>
                    <a-select-option :value="2">两位小数</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="自动保存">
                  <a-switch v-model:checked="autoSave" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="显示提示">
                  <a-switch v-model:checked="showTips" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import { 
  PlayCircleOutlined,
  ClearOutlined,
  ExperimentOutlined,
  CalculatorOutlined,
  SwapOutlined,
  SettingOutlined,
  InboxOutlined
} from '@ant-design/icons-vue'
import TableMergeMultipleDemo from './TableMergeMultipleDemo.vue'
import { multipleTableData } from './mockTableMultiple.js'

// 组件引用
const multipleDemoRef = ref(null)

// 响应式数据
const showComparison = ref(false)
const showAdvanced = ref(false)
const currentTableData = ref([])
const exportData = ref('')
const scorePrecision = ref(1)
const autoSave = ref(false)
const showTips = ref(true)

// 计算属性
const totalScoreStandards = computed(() => {
  let count = 0
  multipleTableData.forEach(first => {
    first.secondIndicators?.forEach(second => {
      count += second.indicatorScore?.length || 0
    })
  })
  return count
})

const completionRate = computed(() => {
  if (currentTableData.value.length === 0) return 0
  const completed = currentTableData.value.filter(item => 
    item.thisIndicatorScore !== null && item.thisIndicatorScore !== undefined
  ).length
  return Math.round((completed / currentTableData.value.length) * 100)
})

const averageScore = computed(() => {
  const scores = currentTableData.value
    .filter(item => item.thisIndicatorScore !== null)
    .map(item => item.thisIndicatorScore)
  
  if (scores.length === 0) return 0
  return scores.reduce((sum, score) => sum + score, 0) / scores.length
})

const maxScore = computed(() => {
  const scores = currentTableData.value
    .filter(item => item.thisIndicatorScore !== null)
    .map(item => item.thisIndicatorScore)
  
  return scores.length > 0 ? Math.max(...scores) : 0
})

const minScore = computed(() => {
  const scores = currentTableData.value
    .filter(item => item.thisIndicatorScore !== null)
    .map(item => item.thisIndicatorScore)
  
  return scores.length > 0 ? Math.min(...scores) : 0
})

const totalScore = computed(() => {
  return currentTableData.value
    .filter(item => item.thisIndicatorScore !== null)
    .reduce((sum, item) => sum + item.thisIndicatorScore, 0)
})

const totalItems = computed(() => currentTableData.value.length)

// 一级指标统计
const firstLevelStats = computed(() => {
  const stats = {}
  currentTableData.value.forEach(item => {
    if (!stats[item.firstIndicatorId]) {
      stats[item.firstIndicatorId] = {
        name: item.firstIndicatorName,
        score: 0,
        maxScore: 0,
        count: 0
      }
    }
    
    if (item.thisIndicatorScore !== null) {
      stats[item.firstIndicatorId].score += item.thisIndicatorScore
    }
    stats[item.firstIndicatorId].maxScore += item.maxScore
    stats[item.firstIndicatorId].count++
  })
  
  return Object.values(stats)
})

// 二级指标统计
const secondLevelStats = computed(() => {
  const stats = {}
  currentTableData.value.forEach(item => {
    if (!stats[item.secondIndicatorId]) {
      stats[item.secondIndicatorId] = {
        name: item.secondIndicatorName,
        total: 0,
        completed: 0
      }
    }
    
    stats[item.secondIndicatorId].total++
    if (item.thisIndicatorScore !== null) {
      stats[item.secondIndicatorId].completed++
    }
  })
  
  return Object.values(stats).map(item => ({
    ...item,
    completion: Math.round((item.completed / item.total) * 100)
  }))
})

/**
 * 处理数据变化
 */
const handleDataChange = (newData) => {
  currentTableData.value = newData
}

/**
 * 快速演示
 */
const handleQuickDemo = () => {
  if (multipleDemoRef.value) {
    multipleDemoRef.value.handleBatchFill()
    message.success('快速演示完成！已填充示例数据')
  }
}

/**
 * 清空所有评分
 */
const handleClearAll = () => {
  if (multipleDemoRef.value) {
    multipleDemoRef.value.resetData()
    message.info('所有评分已清空')
  }
}

/**
 * 随机填充
 */
const handleRandomFill = () => {
  currentTableData.value.forEach(item => {
    item.thisIndicatorScore = Math.floor(Math.random() * (item.maxScore - item.minScore + 1)) + item.minScore
    if (multipleDemoRef.value) {
      multipleDemoRef.value.calculateTotalScore(item)
    }
  })
  message.success('随机填充完成！')
}

/**
 * 计算统计信息
 */
const handleCalculateStats = () => {
  const stats = {
    completionRate: completionRate.value,
    averageScore: averageScore.value,
    totalScore: totalScore.value,
    firstLevelCount: firstLevelStats.value.length,
    secondLevelCount: secondLevelStats.value.length
  }
  
  message.info(`统计完成：完成率${stats.completionRate}%，平均分${stats.averageScore.toFixed(1)}`)
}

/**
 * 切换对比显示
 */
const toggleComparison = () => {
  showComparison.value = !showComparison.value
}

/**
 * 切换高级功能
 */
const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value
}

/**
 * 导出JSON
 */
const exportToJSON = () => {
  exportData.value = JSON.stringify(currentTableData.value, null, 2)
  message.success('JSON数据已生成')
}

/**
 * 导出CSV
 */
const exportToCSV = () => {
  const headers = ['一级指标', '二级指标', '评分标准', '评分范围', '本次评分', '最后得分', '评语']
  const rows = currentTableData.value.map(item => [
    item.firstIndicatorName,
    item.secondIndicatorName,
    item.content,
    `${item.minScore}-${item.maxScore}`,
    item.thisIndicatorScore || '',
    item.totalIndicatorScore || '',
    item.comment || ''
  ])
  
  const csvContent = [headers, ...rows].map(row => row.join(',')).join('\n')
  exportData.value = csvContent
  message.success('CSV数据已生成')
}

/**
 * 导出Excel
 */
const exportToExcel = () => {
  message.info('Excel导出功能需要集成相关库')
}

/**
 * 处理文件导入
 */
const handleImport = (info) => {
  message.info('文件导入功能开发中...')
}

// 组件挂载时初始化
onMounted(() => {
  message.info('多层级表格合并演示页面已加载')
})

// 监听数据变化，自动保存
watch(currentTableData, (newData) => {
  if (autoSave.value && newData.length > 0) {
    localStorage.setItem('multipleTableData', JSON.stringify(newData))
  }
}, { deep: true })
</script>

<style scoped>
.multiple-demo-page {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 24px;
}

.page-header h1 {
  color: #1890ff;
  margin-bottom: 8px;
}

.page-description {
  color: #666;
  font-size: 14px;
  max-width: 900px;
  margin: 0 auto;
  line-height: 1.6;
}

.feature-cards {
  margin-bottom: 24px;
}

.feature-card {
  height: 100%;
  transition: all 0.3s;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.feature-title {
  font-size: 14px;
  font-weight: 600;
}

.structure-card {
  margin-bottom: 24px;
}

.structure-item h4 {
  color: #1890ff;
  margin-bottom: 8px;
}

.structure-item ul {
  margin: 0;
  padding-left: 20px;
}

.structure-item li {
  margin: 4px 0;
  color: #666;
}

.toolbar-card {
  margin-bottom: 16px;
}

.stats-panel {
  margin-bottom: 24px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stats-panel .ant-col {
  text-align: center;
}

.main-demo-card {
  margin-bottom: 24px;
}

.comparison-card {
  margin-bottom: 24px;
}

.comparison-chart {
  space-y: 12px;
}

.chart-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.chart-label {
  width: 120px;
  font-size: 12px;
  color: #666;
}

.chart-bar {
  flex: 1;
  height: 20px;
  background: #f0f0f0;
  border-radius: 10px;
  margin: 0 12px;
  overflow: hidden;
}

.chart-fill {
  height: 100%;
  background: linear-gradient(90deg, #1890ff, #52c41a);
  transition: width 0.3s;
}

.chart-value {
  width: 60px;
  font-size: 12px;
  color: #1890ff;
  font-weight: 500;
}

.completion-stats {
  space-y: 12px;
}

.stats-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.stats-label {
  width: 120px;
  font-size: 12px;
  color: #666;
}

.advanced-card {
  margin-bottom: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .multiple-demo-page {
    padding: 16px;
  }
  
  .feature-cards .ant-col {
    margin-bottom: 16px;
  }
  
  .stats-panel .ant-col {
    margin-bottom: 16px;
  }
}
</style>
