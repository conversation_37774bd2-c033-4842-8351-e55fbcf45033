<template>
  <div class="table-merge-multiple-demo">
    <h2>多层级表格合并案例演示</h2>
    <p class="demo-description">
      这是一个多层级表格合并案例，展示了一级指标 → 二级指标 → 多个评分标准的三层合并效果。
      一级指标和二级指标都会根据相同的ID进行行合并，评分标准正常显示。
    </p>
    
    <!-- 数据统计信息 -->
    <a-row :gutter="16" class="stats-info">
      <a-col :span="6">
        <a-statistic 
          title="一级指标数量" 
          :value="dataStats.firstIndicatorCount" 
          :value-style="{ color: '#1890ff' }"
        />
      </a-col>
      <a-col :span="6">
        <a-statistic 
          title="二级指标数量" 
          :value="dataStats.secondIndicatorCount" 
          :value-style="{ color: '#52c41a' }"
        />
      </a-col>
      <a-col :span="6">
        <a-statistic 
          title="评分标准数量" 
          :value="dataStats.scoreStandardCount" 
          :value-style="{ color: '#faad14' }"
        />
      </a-col>
      <a-col :span="6">
        <a-statistic 
          title="总行数" 
          :value="tableDataSource.length" 
          :value-style="{ color: '#722ed1' }"
        />
      </a-col>
    </a-row>
    
    <!-- 表格展示 -->
    <a-table 
      :dataSource="tableDataSource" 
      :columns="tableColumns" 
      :pagination="false"
      bordered
      size="middle"
      :scroll="{ x: 1400, y: 600 }"
      class="multiple-merge-table"
    >
      <!-- 自定义单元格渲染 -->
      <template #bodyCell="{ column, record }">
        <!-- 评分范围显示 -->
        <template v-if="column.dataIndex === 'scoreRange'">
          <span class="score-range">{{ record.minScore }} - {{ record.maxScore }}</span>
        </template>
        
        <!-- 评分类型标签 -->
        <template v-else-if="column.dataIndex === 'evalScoreTypeList'">
          <div class="type-tags">
            <a-tag 
              v-for="type in record.evalScoreTypeList" 
              :key="type" 
              :color="getTypeColor(type)"
              size="small"
            >
              {{ getTypeText(type) }}
            </a-tag>
          </div>
        </template>
        
        <!-- 他人评分显示 -->
        <template v-else-if="column.dataIndex === 'othersIndicatorScore'">
          <span v-if="record.othersIndicatorScore !== null" class="others-score">
            {{ record.othersIndicatorScore }}
          </span>
          <span v-else class="text-gray">暂无</span>
        </template>
        
        <!-- 本次评分输入 -->
        <template v-else-if="column.dataIndex === 'thisIndicatorScore'">
          <a-input-number
            v-model:value="record.thisIndicatorScore"
            :min="record.minScore"
            :max="record.maxScore"
            :precision="1"
            placeholder="请输入"
            size="small"
            style="width: 100%"
            @change="calculateTotalScore(record)"
          />
        </template>
        
        <!-- 最后得分显示 -->
        <template v-else-if="column.dataIndex === 'totalIndicatorScore'">
          <span 
            v-if="record.totalIndicatorScore !== null" 
            :class="getScoreClass(record.totalIndicatorScore, record.maxScore)"
          >
            {{ record.totalIndicatorScore }}
          </span>
          <span v-else class="text-gray">-</span>
        </template>
        
        <!-- 评语输入 -->
        <template v-else-if="column.dataIndex === 'comment'">
          <a-textarea
            v-model:value="record.comment"
            placeholder="请输入评语"
            :rows="1"
            :maxlength="100"
            size="small"
          />
        </template>
        
        <!-- 媒体文件上传 -->
        <template v-else-if="column.dataIndex === 'mediaPaths'">
          <div class="media-upload">
            <a-button 
              v-if="record.evalScoreTypeList.includes('image') || record.evalScoreTypeList.includes('video')" 
              type="link" 
              size="small"
              @click="handleMediaUpload(record)"
            >
              上传文件
            </a-button>
            <span v-else class="text-gray">不支持</span>
          </div>
        </template>
      </template>
    </a-table>
    
    <!-- 操作按钮区域 -->
    <div class="demo-actions">
      <a-space>
        <a-button type="primary" @click="handleSubmit">
          <template #icon><CheckOutlined /></template>
          提交评分
        </a-button>
        <a-button @click="handleReset">
          <template #icon><ReloadOutlined /></template>
          重置数据
        </a-button>
        <a-button @click="handleBatchFill">
          <template #icon><ThunderboltOutlined /></template>
          批量填充
        </a-button>
        <a-button @click="handleExportData">
          <template #icon><DownloadOutlined /></template>
          导出数据
        </a-button>
        <a-button @click="toggleDataPreview">
          <template #icon><EyeOutlined /></template>
          {{ showDataPreview ? '隐藏' : '显示' }}预览
        </a-button>
      </a-space>
    </div>
    
    <!-- 数据预览面板 -->
    <a-card v-if="showDataPreview" class="data-preview-card" title="数据结构预览">
      <a-tabs>
        <a-tab-pane key="current" tab="当前表格数据">
          <pre class="json-preview">{{ JSON.stringify(tableDataSource.slice(0, 3), null, 2) }}...</pre>
        </a-tab-pane>
        <a-tab-pane key="original" tab="原始数据结构">
          <pre class="json-preview">{{ JSON.stringify(originalData.slice(0, 1), null, 2) }}...</pre>
        </a-tab-pane>
        <a-tab-pane key="submit" tab="提交数据格式">
          <pre class="json-preview">{{ JSON.stringify(submitData, null, 2) }}</pre>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  CheckOutlined, 
  ReloadOutlined, 
  ThunderboltOutlined, 
  DownloadOutlined,
  EyeOutlined 
} from '@ant-design/icons-vue'
import { multipleTableData, multipleColumns } from './mockTableMultiple.js'

// 组件名称
defineOptions({
  name: 'TableMergeMultipleDemo'
})

// 响应式数据
const tableDataSource = ref([])
const originalData = ref(multipleTableData)
const showDataPreview = ref(false)

// 数据统计
const dataStats = computed(() => {
  const firstIndicatorIds = new Set()
  const secondIndicatorIds = new Set()
  
  tableDataSource.value.forEach(item => {
    firstIndicatorIds.add(item.firstIndicatorId)
    secondIndicatorIds.add(item.secondIndicatorId)
  })
  
  return {
    firstIndicatorCount: firstIndicatorIds.size,
    secondIndicatorCount: secondIndicatorIds.size,
    scoreStandardCount: tableDataSource.value.length,
  }
})

// 提交数据格式
const submitData = computed(() => {
  return tableDataSource.value
    .filter(item => item.thisIndicatorScore !== null)
    .map(item => ({
      scoreStandardId: item.scoreStandardId,
      secondIndicatorId: item.secondIndicatorId,
      firstIndicatorId: item.firstIndicatorId,
      thisIndicatorScore: item.thisIndicatorScore,
      totalIndicatorScore: item.totalIndicatorScore,
      comment: item.comment || ''
    }))
})

// 表格列配置 - 包含多层级合并逻辑
const tableColumns = computed(() => [
  {
    title: '一级指标',
    dataIndex: 'firstIndicatorName',
    key: 'firstIndicatorName',
    width: 120,
    align: 'center',
    // 一级指标合并逻辑
    customCell: (record, rowIndex) => {
      let rowSpan = 1
      
      // 向下查找相同的一级指标
      for (let i = rowIndex + 1; i < tableDataSource.value.length; i++) {
        if (tableDataSource.value[i].firstIndicatorId === record.firstIndicatorId) {
          rowSpan++
        } else {
          break
        }
      }
      
      // 如果不是该一级指标的第一行，则隐藏
      if (rowIndex > 0 && 
          tableDataSource.value[rowIndex - 1].firstIndicatorId === record.firstIndicatorId) {
        return { rowSpan: 0 }
      }
      
      return { rowSpan }
    }
  },
  {
    title: '二级指标',
    dataIndex: 'secondIndicatorName',
    key: 'secondIndicatorName',
    width: 120,
    align: 'center',
    // 二级指标合并逻辑
    customCell: (record, rowIndex) => {
      let rowSpan = 1
      
      // 向下查找相同的二级指标
      for (let i = rowIndex + 1; i < tableDataSource.value.length; i++) {
        if (tableDataSource.value[i].secondIndicatorId === record.secondIndicatorId) {
          rowSpan++
        } else {
          break
        }
      }
      
      // 如果不是该二级指标的第一行，则隐藏
      if (rowIndex > 0 && 
          tableDataSource.value[rowIndex - 1].secondIndicatorId === record.secondIndicatorId) {
        return { rowSpan: 0 }
      }
      
      return { rowSpan }
    }
  },
  {
    title: '评分标准',
    dataIndex: 'content',
    key: 'content',
    width: 200,
    ellipsis: true
  },
  {
    title: '评分范围',
    dataIndex: 'scoreRange',
    key: 'scoreRange',
    width: 100,
    align: 'center'
  },
  {
    title: '积分卡',
    dataIndex: 'scoreCardName',
    key: 'scoreCardName',
    width: 120,
    align: 'center',
    ellipsis: true
  },
  {
    title: '评分类型',
    dataIndex: 'evalScoreTypeList',
    key: 'evalScoreTypeList',
    width: 120,
    align: 'center'
  },
  {
    title: '他人评分',
    dataIndex: 'othersIndicatorScore',
    key: 'othersIndicatorScore',
    width: 90,
    align: 'center'
  },
  {
    title: '本次评分',
    dataIndex: 'thisIndicatorScore',
    key: 'thisIndicatorScore',
    width: 100,
    align: 'center'
  },
  {
    title: '最后得分',
    dataIndex: 'totalIndicatorScore',
    key: 'totalIndicatorScore',
    width: 90,
    align: 'center'
  },
  {
    title: '评语',
    dataIndex: 'comment',
    key: 'comment',
    width: 150
  },
  {
    title: '图片/视频',
    dataIndex: 'mediaPaths',
    key: 'mediaPaths',
    width: 100,
    align: 'center'
  }
])

/**
 * 多层级数据转换函数
 * 将三层嵌套数据转换为扁平化表格数据
 */
const transformMultipleTableData = (originalData) => {
  const flatData = []
  
  originalData.forEach((firstLevel) => {
    // 遍历一级指标下的二级指标
    if (firstLevel.secondIndicators && firstLevel.secondIndicators.length > 0) {
      firstLevel.secondIndicators.forEach((secondLevel) => {
        // 遍历二级指标下的评分标准数组
        if (secondLevel.indicatorScore && Array.isArray(secondLevel.indicatorScore)) {
          secondLevel.indicatorScore.forEach((scoreStandard) => {
            flatData.push({
              // 一级指标信息
              firstIndicatorId: firstLevel.id,
              firstIndicatorName: firstLevel.name,
              
              // 二级指标信息
              secondIndicatorId: secondLevel.id,
              secondIndicatorName: secondLevel.name,
              
              // 评分标准信息
              scoreStandardId: scoreStandard.id,
              content: scoreStandard.content || '',
              minScore: scoreStandard.minScore || 0,
              maxScore: scoreStandard.maxScore || 100,
              scoreCardName: scoreStandard.scoreCardName || '',
              evalScoreTypeList: scoreStandard.evalScoreTypeList || [],
              
              // 评分数据
              othersIndicatorScore: scoreStandard.othersIndicatorScore,
              thisIndicatorScore: scoreStandard.thisIndicatorScore,
              totalIndicatorScore: scoreStandard.totalIndicatorScore,
              
              // 其他信息
              comment: scoreStandard.comment || '',
              imgPaths: scoreStandard.imgPaths || '',
              videoPaths: scoreStandard.videoPaths || '',
              
              // 原始数据引用
              _originalFirst: firstLevel,
              _originalSecond: secondLevel,
              _originalScore: scoreStandard
            })
          })
        }
      })
    }
  })
  
  return flatData
}

/**
 * 获取评分类型颜色
 */
const getTypeColor = (type) => {
  const colorMap = {
    'text': 'blue',
    'image': 'green',
    'video': 'orange'
  }
  return colorMap[type] || 'default'
}

/**
 * 获取评分类型文本
 */
const getTypeText = (type) => {
  const textMap = {
    'text': '文字',
    'image': '图片',
    'video': '视频'
  }
  return textMap[type] || type
}

/**
 * 获取分数样式类
 */
const getScoreClass = (score, maxScore) => {
  const percentage = (score / maxScore) * 100
  if (percentage >= 90) return 'score-excellent'
  if (percentage >= 80) return 'score-good'
  if (percentage >= 60) return 'score-normal'
  return 'score-poor'
}

/**
 * 计算总分
 */
const calculateTotalScore = (record) => {
  if (record.thisIndicatorScore !== null && record.thisIndicatorScore !== undefined) {
    if (record.othersIndicatorScore !== null) {
      record.totalIndicatorScore = ((record.thisIndicatorScore + record.othersIndicatorScore) / 2).toFixed(1)
    } else {
      record.totalIndicatorScore = record.thisIndicatorScore
    }
  } else {
    record.totalIndicatorScore = null
  }
}

/**
 * 处理媒体文件上传
 */
const handleMediaUpload = (record) => {
  message.info(`为评分标准"${record.content}"上传媒体文件`)
}

/**
 * 提交评分
 */
const handleSubmit = () => {
  const incompleteItems = tableDataSource.value.filter(item => 
    item.thisIndicatorScore === null || item.thisIndicatorScore === undefined
  )
  
  if (incompleteItems.length > 0) {
    message.warning(`还有 ${incompleteItems.length} 项评分标准未评分，请完成后再提交`)
    return
  }
  
  console.log('提交的多层级评分数据：', submitData.value)
  message.success('多层级评分提交成功！')
}

/**
 * 重置数据
 */
const handleReset = () => {
  tableDataSource.value = transformMultipleTableData(multipleTableData)
  message.info('数据已重置')
}

/**
 * 批量填充示例数据
 */
const handleBatchFill = () => {
  tableDataSource.value.forEach((item, index) => {
    item.thisIndicatorScore = Math.floor(Math.random() * (item.maxScore - item.minScore + 1)) + item.minScore
    item.comment = `评分标准${index + 1}的评语示例`
    calculateTotalScore(item)
  })
  message.success('批量填充完成！')
}

/**
 * 导出数据
 */
const handleExportData = () => {
  const exportData = {
    statistics: dataStats.value,
    tableData: tableDataSource.value,
    submitData: submitData.value
  }
  console.log('导出的多层级数据：', exportData)
  message.success('数据已导出到控制台')
}

/**
 * 切换数据预览
 */
const toggleDataPreview = () => {
  showDataPreview.value = !showDataPreview.value
}

// 组件挂载时初始化数据
onMounted(() => {
  tableDataSource.value = transformMultipleTableData(multipleTableData)
  message.info('多层级表格合并演示已加载')
})

// 暴露给父组件的方法
defineExpose({
  calculateTotalScore,
  transformMultipleTableData,
  getTableData: () => tableDataSource.value,
  resetData: handleReset
})
</script>

<style scoped>
.table-merge-multiple-demo {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
}

.demo-description {
  margin-bottom: 20px;
  padding: 12px;
  background: #f0f9ff;
  border-left: 4px solid #1890ff;
  color: #666;
  line-height: 1.6;
}

.stats-info {
  margin-bottom: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.stats-info .ant-col {
  text-align: center;
}

.multiple-merge-table {
  margin-bottom: 20px;
}

.demo-actions {
  margin-bottom: 20px;
  text-align: center;
}

.data-preview-card {
  margin-top: 20px;
}

.json-preview {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 4px;
  padding: 12px;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
}

/* 样式类 */
.score-range {
  font-weight: 500;
  color: #1890ff;
}

.type-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
}

.others-score {
  color: #52c41a;
  font-weight: 500;
}

.text-gray {
  color: #999;
}

.media-upload {
  text-align: center;
}

/* 分数样式 */
.score-excellent {
  color: #52c41a;
  font-weight: bold;
}

.score-good {
  color: #1890ff;
  font-weight: bold;
}

.score-normal {
  color: #faad14;
}

.score-poor {
  color: #ff4d4f;
}

/* 表格样式优化 */
:deep(.ant-table-tbody > tr > td) {
  vertical-align: middle;
  padding: 8px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
  text-align: center;
}

:deep(.ant-table-cell) {
  border-right: 1px solid #f0f0f0;
}
</style>
