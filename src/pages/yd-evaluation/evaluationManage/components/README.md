# 评价管理组件库

## 📋 组件概述

本目录包含了评价管理系统的核心组件，包括表格合并功能、评语输入、多媒体上传等完整的业务功能。

## 🚀 新增组件

### 1. 基础表格合并组件 🆕
- **TableMergeDemo.vue** - 二层合并：一级指标 → 二级指标
- **TableMergeDemoPage.vue** - 基础合并完整演示页面
- **表格合并实现文档.md** - 基础合并技术文档

### 2. 多层级表格合并组件 🆕🔥
- **TableMergeMultipleDemo.vue** - 三层合并：一级指标 → 二级指标 → 多个评分标准
- **TableMergeMultiplePage.vue** - 多层级合并完整演示页面
- **多层级表格合并实现文档.md** - 多层级合并技术文档
- **mockTableMultiple.js** - 多层级测试数据
- **testMultiple.vue** - 多层级功能测试页面

### 3. 原有功能组件
- **evaluationDetailed.vue** - 主评价组件
- **VideoPlayer.vue** - xgplayer视频播放组件
- **MediaPreviewModal.vue** - 通用媒体预览组件

## 🔧 表格合并功能说明

### 基础合并特性（二层结构）
- ✅ **一级指标行合并**：相同一级指标自动合并显示
- ✅ **数据结构转换**：嵌套数据转扁平化表格数据
- ✅ **实时评分计算**：支持评分输入和自动计算总分
- ✅ **多种评分类型**：支持文字、图片、视频评分
- ✅ **样式优化**：分数颜色标识、响应式布局
- ✅ **批量操作**：快速填充、清空、随机评分
- ✅ **数据预览**：JSON格式数据结构预览

### 多层级合并特性（三层结构）🆕
- ✅ **双重行合并**：一级指标和二级指标同时合并
- ✅ **三层数据处理**：一级指标 → 二级指标 → 多个评分标准
- ✅ **智能合并算法**：自动计算各层级的合并行数
- ✅ **复杂数据转换**：处理 indicatorScore 数组结构
- ✅ **多维度统计**：层级数据的完整统计分析
- ✅ **性能优化**：大数据量的高效处理
- ✅ **完整测试**：专门的测试页面和性能监控

### 使用方法

#### 基础二层合并
```vue
<template>
  <!-- 基础使用 -->
  <TableMergeDemo />

  <!-- 完整演示页面 -->
  <TableMergeDemoPage />
</template>

<script setup>
import TableMergeDemo from './TableMergeDemo.vue'
import TableMergeDemoPage from './TableMergeDemoPage.vue'
</script>
```

#### 多层级三层合并 🆕
```vue
<template>
  <!-- 多层级基础使用 -->
  <TableMergeMultipleDemo />

  <!-- 多层级完整演示 -->
  <TableMergeMultiplePage />

  <!-- 多层级功能测试 -->
  <testMultiple />
</template>

<script setup>
import TableMergeMultipleDemo from './TableMergeMultipleDemo.vue'
import TableMergeMultiplePage from './TableMergeMultiplePage.vue'
import testMultiple from './testMultiple.vue'
</script>
```

## 📊 数据格式

### 基础二层数据结构（来自mockTable.js）
```javascript
[
  {
    id: '一级指标ID',
    name: '一级指标名称',
    secondIndicators: [
      {
        id: '二级指标ID',
        name: '二级指标名称',
        indicatorScore: {  // 🔸 对象格式
          content: '评分标准',
          minScore: 0,
          maxScore: 100,
          scoreCardName: '积分卡名称',
          evalScoreTypeList: ['text', 'image', 'video'],
          othersIndicatorScore: 85,
          // ... 其他评分字段
        }
      }
    ]
  }
]
```

### 多层级三层数据结构（来自mockTableMultiple.js）🆕
```javascript
[
  {
    id: '一级指标ID',
    name: '一级指标名称',
    secondIndicators: [
      {
        id: '二级指标ID',
        name: '二级指标名称',
        indicatorScore: [  // 🔥 数组格式 - 关键变化
          {
            id: '评分标准ID1',
            content: '评分标准1描述',
            minScore: 0,
            maxScore: 100,
            scoreCardName: '积分卡名称1',
            evalScoreTypeList: ['text'],
            othersIndicatorScore: 85,
            // ... 其他字段
          },
          {
            id: '评分标准ID2',
            content: '评分标准2描述',
            minScore: 0,
            maxScore: 100,
            scoreCardName: '积分卡名称2',
            evalScoreTypeList: ['text', 'image'],
            othersIndicatorScore: 90,
            // ... 其他字段
          }
          // ... 更多评分标准
        ]
      }
    ]
  }
]
```

### 输出数据格式

#### 基础二层输出
```javascript
[
  {
    secondIndicatorId: '二级指标ID',
    thisIndicatorScore: 85,
    totalIndicatorScore: 87.5,
    comment: '评语内容'
  }
]
```

#### 多层级三层输出 🆕
```javascript
[
  {
    scoreStandardId: '评分标准ID',
    secondIndicatorId: '二级指标ID',
    firstIndicatorId: '一级指标ID',
    thisIndicatorScore: 85,
    totalIndicatorScore: 87.5,
    comment: '评语内容'
  }
]
```

## 🎯 核心实现原理

### 表格行合并算法
```javascript
customCell: (record, rowIndex) => {
  let rowSpan = 1

  // 向下查找相同的一级指标
  for (let i = rowIndex + 1; i < dataSource.length; i++) {
    if (dataSource[i].firstIndicatorId === record.firstIndicatorId) {
      rowSpan++
    } else break
  }

  // 如果不是第一行，则隐藏
  if (rowIndex > 0 &&
      dataSource[rowIndex - 1].firstIndicatorId === record.firstIndicatorId) {
    return { rowSpan: 0 }
  }

  return { rowSpan }
}
```

## 📁 文件结构

```
components/
├── TableMergeDemo.vue          # 🆕 核心表格合并组件
├── TableMergeDemoPage.vue      # 🆕 完整演示页面
├── 表格合并实现文档.md          # 🆕 详细技术文档
├── evaluationDetailed.vue     # 主评价组件
├── VideoPlayer.vue            # 视频播放组件
├── MediaPreviewModal.vue      # 媒体预览组件
├── mockTable.js               # 测试数据
└── README.md                  # 使用指南（本文件）
```

## 🔧 原有功能说明

### 1. 评语功能 (Comment列)
- **触发条件**: 当 `record.evalScoreTypeList` 包含 `'text'` 时显示
- **组件**: 使用 `a-textarea` 组件
- **功能**: 
  - 支持多行文本输入
  - 最大字符数限制：200字符
  - 显示字符计数
  - 数据绑定到 `record.comment` 字段

### 2. 统一文件上传功能 (Paths列) 🆕
- **触发条件**: 当 `record.evalScoreTypeList` 包含 `'image'` 或 `'video'` 时显示统一上传按钮
- **智能配置**:
  - `['image']`: 按钮文字"上传图片"，accept="image/*"
  - `['video']`: 按钮文字"上传视频"，accept="video/*"
  - `['image', 'video']`: 按钮文字"上传文件"，accept="image/*,video/*"
- **文件类型验证**: 在before-upload中检查文件类型是否符合evalScoreTypeList
- **上传接口**: 使用标准格式 `http.form('/file/common/upload', { file, folderType: 'evalActivity' }, {})`

### 3. 优化的缩略图显示 🆕
- **尺寸**: 100px × 100px
- **显示逻辑**:
  - 只显示第一个文件的缩略图（优先图片）
  - 多文件时显示"+N"指示器
  - 支持点击预览所有文件
  - 悬停显示预览图标

### 4. 通用预览组件 🆕
- **组件**: 独立的 `MediaPreviewModal.vue` 组件
- **功能**:
  - 接收imgPaths和videoPaths参数
  - 轮播图展示所有图片和视频
  - 使用xgplayer播放视频
  - 支持导航按钮和指示器

## 数据字段说明

### 输入字段
- `evalScoreTypeList`: 数组，支持的评分类型 `['text', 'image', 'video']`

### 输出字段
- `comment`: 字符串，评语内容
- `imgPaths`: 字符串，多个图片URL用逗号分隔
- `videoPaths`: 字符串，多个视频URL用逗号分隔

## 组件结构

### 主要文件
1. `evaluationDetailed.vue` - 主评价组件
2. `VideoPlayer.vue` - xgplayer视频播放组件 🆕
3. `MediaPreviewModal.vue` - 通用媒体预览组件 🆕
4. `test-data.js` - 测试数据和验证清单

### VideoPlayer组件特性 🆕
- **播放器**: 直接使用xgplayer 3.0.22
- **功能**: 完整的播放控制、全屏、音量调节等
- **样式**: 自定义主题色彩，圆角设计
- **事件**: 支持ready、play、pause、ended等事件

### MediaPreviewModal组件特性 🆕
- **通用性**: 可在任何地方复用
- **参数**: imgPaths（图片路径）、videoPaths（视频路径）
- **格式支持**: 数组或逗号分隔字符串
- **交互**: 轮播切换、导航按钮、指示器

## 使用示例

### 基础用法
```javascript
// 表格数据示例
const tableData = [
  {
    id: 1,
    evalScoreTypeList: ['text'], // 只支持文字评语
    comment: '',
    imgPaths: '',
    videoPaths: ''
  },
  {
    id: 2,
    evalScoreTypeList: ['image', 'text'], // 支持图片和文字
    comment: '',
    imgPaths: '',
    videoPaths: ''
  },
  {
    id: 3,
    evalScoreTypeList: ['image', 'video', 'text'], // 支持全部类型
    comment: '',
    imgPaths: '',
    videoPaths: ''
  }
]
```

### 上传配置
- **上传接口**: `/file/common/upload`
- **文件夹类型**: `evalActivity`
- **支持格式**: 
  - 图片: `image/*`
  - 视频: `video/*`

## 样式说明

### CSS类名
- `.paths-container`: 图片/视频容器
- `.upload-buttons`: 上传按钮区域
- `.thumbnail-container`: 缩略图容器
- `.thumbnail-item`: 单个缩略图
- `.thumbnail-overlay`: 缩略图悬停遮罩
- `.preview-container`: 预览Modal容器

### 响应式设计
- 缩略图自适应排列
- 预览Modal支持不同屏幕尺寸
- 移动端友好的触摸操作

## 安装依赖

### xgplayer（可选）
```bash
npm install xgplayer@3.0.22
```

如果安装失败，组件会自动回退到HTML5 video标签。

## 测试建议

1. **功能测试**:
   - 测试不同 `evalScoreTypeList` 组合
   - 验证上传功能
   - 检查预览功能

2. **兼容性测试**:
   - 不同浏览器的视频播放
   - 移动端触摸操作
   - 文件上传限制

3. **性能测试**:
   - 大文件上传
   - 多媒体文件预览
   - 长列表滚动性能

## 注意事项

1. **文件大小限制**: 建议在上传前添加文件大小检查
2. **网络优化**: 大文件建议使用CDN加速
3. **浏览器兼容**: 某些老版本浏览器可能不支持部分视频格式
4. **安全考虑**: 上传文件需要进行安全检查和格式验证

## 后续优化建议

1. 添加图片压缩功能
2. 支持拖拽上传
3. 添加上传进度显示
4. 支持批量删除媒体文件
5. 添加更多视频播放控制选项
