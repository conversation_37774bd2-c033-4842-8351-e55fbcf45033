<template>
    <div class="video-player-test">
        <h2>VideoPlayer 组件测试</h2>
        <p>测试视频URL: {{ testVideoUrl }}</p>
        
        <!-- 测试不同尺寸的容器 -->
        <div class="test-containers">
            <!-- 小容器测试 -->
            <div class="test-section">
                <h3>小容器 (300x200)</h3>
                <div class="small-container">
                    <VideoPlayer 
                        :src="testVideoUrl" 
                        :fluid="true"
                        :fit-mode="'contain'"
                        :autoplay="false"
                        :muted="true"
                    />
                </div>
            </div>
            
            <!-- 中等容器测试 -->
            <div class="test-section">
                <h3>中等容器 (600x400)</h3>
                <div class="medium-container">
                    <VideoPlayer 
                        :src="testVideoUrl" 
                        :fluid="true"
                        :fit-mode="'contain'"
                        :autoplay="false"
                        :muted="true"
                    />
                </div>
            </div>
            
            <!-- 大容器测试 -->
            <div class="test-section">
                <h3>大容器 (800x500)</h3>
                <div class="large-container">
                    <VideoPlayer 
                        :src="testVideoUrl" 
                        :fluid="true"
                        :fit-mode="'contain'"
                        :autoplay="false"
                        :muted="true"
                    />
                </div>
            </div>
            
            <!-- 响应式容器测试 -->
            <div class="test-section">
                <h3>响应式容器 (100% width, 400px height)</h3>
                <div class="responsive-container">
                    <VideoPlayer 
                        :src="testVideoUrl" 
                        :fluid="true"
                        :fit-mode="'contain'"
                        :autoplay="false"
                        :muted="true"
                    />
                </div>
            </div>
            
            <!-- 不同适配模式测试 -->
            <div class="test-section">
                <h3>不同适配模式测试</h3>
                <div class="fit-mode-tests">
                    <div class="fit-test">
                        <h4>contain 模式</h4>
                        <div class="fit-container">
                            <VideoPlayer 
                                :src="testVideoUrl" 
                                :fluid="true"
                                :fit-mode="'contain'"
                                :autoplay="false"
                                :muted="true"
                            />
                        </div>
                    </div>
                    
                    <div class="fit-test">
                        <h4>cover 模式</h4>
                        <div class="fit-container">
                            <VideoPlayer 
                                :src="testVideoUrl" 
                                :fluid="true"
                                :fit-mode="'cover'"
                                :autoplay="false"
                                :muted="true"
                            />
                        </div>
                    </div>
                    
                    <div class="fit-test">
                        <h4>fill 模式</h4>
                        <div class="fit-container">
                            <VideoPlayer 
                                :src="testVideoUrl" 
                                :fluid="true"
                                :fit-mode="'fill'"
                                :autoplay="false"
                                :muted="true"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 控制面板 -->
        <div class="control-panel">
            <h3>控制面板</h3>
            <div class="controls">
                <a-button @click="changeVideoUrl">切换视频</a-button>
                <a-button @click="toggleContainerSize">切换容器大小</a-button>
            </div>
        </div>
    </div>
</template>

<script setup name="VideoPlayerTest">
import VideoPlayer from './VideoPlayer.vue'

// 测试视频URL
const testVideoUrl = ref('https://158-minio.yyide.vip/cloud-yide/eval/activity/b9f599c4b33147a4a0955bd315b6db21.mp4')

// 备用视频URL列表
const videoUrls = [
    'https://158-minio.yyide.vip/cloud-yide/eval/activity/b9f599c4b33147a4a0955bd315b6db21.mp4',
    'https://www.w3schools.com/html/mov_bbb.mp4',
    'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
]

let currentVideoIndex = 0
const isLargeContainer = ref(false)

// 切换视频
const changeVideoUrl = () => {
    currentVideoIndex = (currentVideoIndex + 1) % videoUrls.length
    testVideoUrl.value = videoUrls[currentVideoIndex]
}

// 切换容器大小
const toggleContainerSize = () => {
    isLargeContainer.value = !isLargeContainer.value
}
</script>

<style lang="less" scoped>
.video-player-test {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
    
    h2 {
        color: #333;
        margin-bottom: 20px;
    }
    
    .test-containers {
        display: flex;
        flex-direction: column;
        gap: 30px;
        
        .test-section {
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
            
            h3 {
                margin-bottom: 15px;
                color: #555;
            }
            
            h4 {
                margin-bottom: 10px;
                color: #666;
                font-size: 14px;
            }
        }
    }
    
    // 不同尺寸的容器
    .small-container {
        width: 300px;
        height: 200px;
        border: 2px solid #1890ff;
        border-radius: 6px;
    }
    
    .medium-container {
        width: 600px;
        height: 400px;
        border: 2px solid #52c41a;
        border-radius: 6px;
    }
    
    .large-container {
        width: 800px;
        height: 500px;
        border: 2px solid #fa8c16;
        border-radius: 6px;
    }
    
    .responsive-container {
        width: 100%;
        height: 400px;
        border: 2px solid #722ed1;
        border-radius: 6px;
    }
    
    // 适配模式测试
    .fit-mode-tests {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        
        .fit-test {
            .fit-container {
                width: 300px;
                height: 200px;
                border: 2px solid #eb2f96;
                border-radius: 6px;
            }
        }
    }
    
    // 控制面板
    .control-panel {
        margin-top: 30px;
        padding: 20px;
        background: #f0f0f0;
        border-radius: 8px;
        
        .controls {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
    }
}

// 响应式适配
@media (max-width: 768px) {
    .video-player-test {
        padding: 10px;
        
        .small-container,
        .medium-container,
        .large-container {
            width: 100%;
            max-width: 100%;
        }
        
        .fit-mode-tests {
            grid-template-columns: 1fr;
            
            .fit-container {
                width: 100%;
            }
        }
    }
}
</style>
