<template>
  <div class="demo-page">
    <div class="page-header">
      <h1>表格合并功能演示页面</h1>
      <p class="page-description">
        这个页面展示了完整的表格合并功能，包括一级指标合并、二级指标展示、评分输入等功能。
        数据来源于 mockTable.js 文件，展示了真实的业务场景。
      </p>
    </div>

    <!-- 功能说明卡片 -->
    <a-row :gutter="16" class="feature-cards">
      <a-col :span="8">
        <a-card title="🔗 行合并功能" size="small">
          <p>一级指标自动合并相同项，二级指标正常显示，实现层级数据的清晰展示。</p>
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card title="📝 评分功能" size="small">
          <p>支持本次评分输入，自动计算最终得分，包含评语和多媒体上传。</p>
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card title="🎨 样式优化" size="small">
          <p>分数颜色标识，评分类型标签，响应式布局，提升用户体验。</p>
        </a-card>
      </a-col>
    </a-row>

    <!-- 操作工具栏 -->
    <a-card class="toolbar-card" size="small">
      <template #title>
        <span>🛠️ 操作工具</span>
      </template>
      <a-space>
        <a-button type="primary" @click="handleQuickFill">
          <template #icon><ThunderboltOutlined /></template>
          快速填充示例数据
        </a-button>
        <a-button @click="handleClearAll">
          <template #icon><ClearOutlined /></template>
          清空所有评分
        </a-button>
        <a-button @click="handleRandomScore">
          <template #icon><ExperimentOutlined /></template>
          随机生成评分
        </a-button>
        <a-button @click="togglePreview">
          <template #icon><EyeOutlined /></template>
          {{ showPreview ? '隐藏' : '显示' }}数据预览
        </a-button>
      </a-space>
    </a-card>

    <!-- 统计信息 -->
    <a-row :gutter="16" class="stats-row">
      <a-col :span="6">
        <a-statistic 
          title="一级指标总数" 
          :value="stats.firstIndicatorCount" 
          :value-style="{ color: '#1890ff' }"
        />
      </a-col>
      <a-col :span="6">
        <a-statistic 
          title="二级指标总数" 
          :value="stats.secondIndicatorCount" 
          :value-style="{ color: '#52c41a' }"
        />
      </a-col>
      <a-col :span="6">
        <a-statistic 
          title="已评分项目" 
          :value="stats.scoredCount" 
          :value-style="{ color: '#faad14' }"
        />
      </a-col>
      <a-col :span="6">
        <a-statistic 
          title="评分完成率" 
          :value="stats.completionRate" 
          suffix="%" 
          :value-style="{ color: stats.completionRate === 100 ? '#52c41a' : '#ff4d4f' }"
        />
      </a-col>
    </a-row>

    <!-- 表格合并演示组件 -->
    <a-card class="table-card">
      <template #title>
        <span>📊 表格合并演示</span>
      </template>
      <template #extra>
        <a-space>
          <a-tag color="blue">Vue 3 Composition API</a-tag>
          <a-tag color="green">Ant Design Vue 4.x</a-tag>
          <a-tag color="orange">表格行合并</a-tag>
        </a-space>
      </template>
      
      <TableMergeDemo ref="tableDemoRef" @data-change="handleDataChange" />
    </a-card>

    <!-- 数据预览面板 -->
    <a-card v-if="showPreview" class="preview-card">
      <template #title>
        <span>🔍 数据结构预览</span>
      </template>
      <template #extra>
        <a-button size="small" @click="copyToClipboard">
          <template #icon><CopyOutlined /></template>
          复制数据
        </a-button>
      </template>
      
      <a-tabs>
        <a-tab-pane key="current" tab="当前表格数据">
          <pre class="json-preview">{{ JSON.stringify(currentTableData, null, 2) }}</pre>
        </a-tab-pane>
        <a-tab-pane key="original" tab="原始数据结构">
          <pre class="json-preview">{{ JSON.stringify(originalData, null, 2) }}</pre>
        </a-tab-pane>
        <a-tab-pane key="submit" tab="提交数据格式">
          <pre class="json-preview">{{ JSON.stringify(submitData, null, 2) }}</pre>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 实现说明 -->
    <a-card class="implementation-card">
      <template #title>
        <span>💡 实现要点说明</span>
      </template>
      
      <a-collapse>
        <a-collapse-panel key="merge" header="1. 表格行合并实现原理">
          <p><strong>核心思路：</strong>使用 customCell 属性控制单元格的 rowSpan 值</p>
          <ul>
            <li>计算每个一级指标需要合并的行数</li>
            <li>第一行显示内容并设置 rowSpan</li>
            <li>后续相同指标的行设置 rowSpan 为 0（隐藏）</li>
          </ul>
          <a-typography-paragraph copyable>
            <pre>customCell: (record, rowIndex) => {
  let rowSpan = 1
  // 向下查找相同指标
  for (let i = rowIndex + 1; i < data.length; i++) {
    if (data[i].firstIndicatorId === record.firstIndicatorId) {
      rowSpan++
    } else break
  }
  // 非首行隐藏
  if (rowIndex > 0 && data[rowIndex - 1].firstIndicatorId === record.firstIndicatorId) {
    return { rowSpan: 0 }
  }
  return { rowSpan }
}</pre>
          </a-typography-paragraph>
        </a-collapse-panel>
        
        <a-collapse-panel key="data" header="2. 数据结构转换">
          <p><strong>转换目的：</strong>将嵌套的树形数据转换为扁平化的表格数据</p>
          <ul>
            <li>保持一级指标和二级指标的关联关系</li>
            <li>提取评分相关的所有字段</li>
            <li>添加必要的辅助字段用于合并判断</li>
          </ul>
        </a-collapse-panel>
        
        <a-collapse-panel key="interaction" header="3. 交互功能实现">
          <p><strong>功能特点：</strong>丰富的用户交互和数据处理</p>
          <ul>
            <li>实时评分计算和验证</li>
            <li>多种评分类型支持（文字、图片、视频）</li>
            <li>批量操作和数据导出</li>
            <li>响应式设计和样式优化</li>
          </ul>
        </a-collapse-panel>
      </a-collapse>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  ThunderboltOutlined, 
  ClearOutlined, 
  ExperimentOutlined, 
  EyeOutlined,
  CopyOutlined 
} from '@ant-design/icons-vue'
import TableMergeDemo from './TableMergeDemo.vue'
import { tableData } from './mockTable.js'

// 组件引用
const tableDemoRef = ref(null)

// 响应式数据
const showPreview = ref(false)
const currentTableData = ref([])
const originalData = ref(tableData)

// 统计信息
const stats = computed(() => {
  const firstIndicatorIds = new Set()
  let scoredCount = 0
  
  currentTableData.value.forEach(item => {
    firstIndicatorIds.add(item.firstIndicatorId)
    if (item.thisIndicatorScore !== null && item.thisIndicatorScore !== undefined) {
      scoredCount++
    }
  })
  
  const total = currentTableData.value.length
  const completionRate = total > 0 ? Math.round((scoredCount / total) * 100) : 0
  
  return {
    firstIndicatorCount: firstIndicatorIds.size,
    secondIndicatorCount: total,
    scoredCount,
    completionRate
  }
})

// 提交数据格式
const submitData = computed(() => {
  return currentTableData.value
    .filter(item => item.thisIndicatorScore !== null)
    .map(item => ({
      secondIndicatorId: item.secondIndicatorId,
      thisIndicatorScore: item.thisIndicatorScore,
      totalIndicatorScore: item.totalIndicatorScore,
      comment: item.comment || ''
    }))
})

/**
 * 处理数据变化
 */
const handleDataChange = (newData) => {
  currentTableData.value = newData
}

/**
 * 快速填充示例数据
 */
const handleQuickFill = () => {
  if (tableDemoRef.value) {
    // 为每个项目填充示例评分
    currentTableData.value.forEach((item, index) => {
      item.thisIndicatorScore = Math.floor(Math.random() * (item.maxScore - item.minScore + 1)) + item.minScore
      item.comment = `这是第${index + 1}项的评语示例，展示评分标准的执行情况。`
      
      // 触发分数计算
      if (tableDemoRef.value.calculateTotalScore) {
        tableDemoRef.value.calculateTotalScore(item)
      }
    })
    
    message.success('示例数据填充完成！')
  }
}

/**
 * 清空所有评分
 */
const handleClearAll = () => {
  currentTableData.value.forEach(item => {
    item.thisIndicatorScore = null
    item.totalIndicatorScore = null
    item.comment = ''
  })
  message.info('所有评分已清空')
}

/**
 * 随机生成评分
 */
const handleRandomScore = () => {
  currentTableData.value.forEach(item => {
    // 随机生成在评分范围内的分数
    const randomScore = Math.floor(Math.random() * (item.maxScore - item.minScore + 1)) + item.minScore
    item.thisIndicatorScore = randomScore
    
    // 计算总分
    if (item.othersIndicatorScore !== null) {
      item.totalIndicatorScore = ((randomScore + item.othersIndicatorScore) / 2).toFixed(1)
    } else {
      item.totalIndicatorScore = randomScore
    }
  })
  message.success('随机评分生成完成！')
}

/**
 * 切换预览显示
 */
const togglePreview = () => {
  showPreview.value = !showPreview.value
}

/**
 * 复制数据到剪贴板
 */
const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(JSON.stringify(currentTableData.value, null, 2))
    message.success('数据已复制到剪贴板')
  } catch (error) {
    message.error('复制失败，请手动复制')
  }
}

// 组件挂载时初始化
onMounted(() => {
  // 这里可以添加初始化逻辑
})
</script>

<style scoped>
.demo-page {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 24px;
}

.page-header h1 {
  color: #1890ff;
  margin-bottom: 8px;
}

.page-description {
  color: #666;
  font-size: 14px;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.feature-cards {
  margin-bottom: 24px;
}

.feature-cards .ant-card {
  height: 100%;
}

.toolbar-card {
  margin-bottom: 16px;
}

.stats-row {
  margin-bottom: 24px;
}

.stats-row .ant-col {
  text-align: center;
}

.table-card {
  margin-bottom: 24px;
}

.preview-card {
  margin-bottom: 24px;
}

.json-preview {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  font-size: 12px;
  line-height: 1.45;
  overflow-x: auto;
  max-height: 400px;
  overflow-y: auto;
}

.implementation-card {
  margin-bottom: 24px;
}

.implementation-card ul {
  margin: 8px 0;
  padding-left: 20px;
}

.implementation-card li {
  margin: 4px 0;
  color: #666;
}

.implementation-card pre {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 4px;
  padding: 12px;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .demo-page {
    padding: 16px;
  }
  
  .feature-cards .ant-col {
    margin-bottom: 16px;
  }
  
  .stats-row .ant-col {
    margin-bottom: 16px;
  }
}
</style>
