<template>
  <div class="test-multiple-page">
    <h1>多层级表格合并功能测试</h1>
    
    <!-- 测试控制面板 -->
    <a-card class="test-controls" title="🧪 测试控制面板">
      <a-space wrap>
        <a-button type="primary" @click="runAllTests">
          <template #icon><PlayCircleOutlined /></template>
          运行全部测试
        </a-button>
        <a-button @click="testDataStructure">
          <template #icon><DatabaseOutlined /></template>
          测试数据结构
        </a-button>
        <a-button @click="testMergeLogic">
          <template #icon><MergeCellsOutlined /></template>
          测试合并逻辑
        </a-button>
        <a-button @click="testPerformance">
          <template #icon><DashboardOutlined /></template>
          性能测试
        </a-button>
        <a-button @click="clearResults">
          <template #icon><ClearOutlined /></template>
          清空结果
        </a-button>
        <a-button @click="showComponents = !showComponents">
          <template #icon><EyeOutlined /></template>
          {{ showComponents ? '隐藏' : '显示' }}组件
        </a-button>
      </a-space>
    </a-card>

    <!-- 测试结果显示 -->
    <a-card v-if="testResults.length > 0" class="test-results" title="📊 测试结果">
      <a-list :data-source="testResults" size="small">
        <template #renderItem="{ item }">
          <a-list-item>
            <a-list-item-meta>
              <template #avatar>
                <a-avatar :style="{ backgroundColor: item.success ? '#52c41a' : '#ff4d4f' }">
                  {{ item.success ? '✓' : '✗' }}
                </a-avatar>
              </template>
              <template #title>
                <span :class="item.success ? 'test-success' : 'test-error'">
                  {{ item.title }}
                </span>
              </template>
              <template #description>
                <div>
                  <p>{{ item.description }}</p>
                  <div v-if="item.details" class="test-details">
                    <a-tag v-for="(value, key) in item.details" :key="key" color="blue">
                      {{ key }}: {{ value }}
                    </a-tag>
                  </div>
                </div>
              </template>
            </a-list-item-meta>
            <template #actions>
              <span class="test-time">{{ item.timestamp }}</span>
            </template>
          </a-list-item>
        </template>
      </a-list>
    </a-card>

    <!-- 组件演示区域 -->
    <div v-if="showComponents" class="components-demo">
      <!-- 基础多层级组件 -->
      <a-card class="demo-card" title="🔗 基础多层级表格合并">
        <TableMergeMultipleDemo ref="multipleDemoRef" @data-change="handleDataChange" />
      </a-card>

      <!-- 完整演示页面 -->
      <a-card class="demo-card" title="📊 完整多层级演示页面">
        <TableMergeMultiplePage />
      </a-card>
    </div>

    <!-- 数据分析面板 -->
    <a-card v-if="currentData.length > 0" class="data-analysis" title="📈 数据分析">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-statistic 
            title="数据总行数" 
            :value="currentData.length" 
            :value-style="{ color: '#1890ff' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic 
            title="一级指标数" 
            :value="uniqueFirstIndicators" 
            :value-style="{ color: '#52c41a' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic 
            title="二级指标数" 
            :value="uniqueSecondIndicators" 
            :value-style="{ color: '#faad14' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic 
            title="评分标准数" 
            :value="totalScoreStandards" 
            :value-style="{ color: '#722ed1' }"
          />
        </a-col>
      </a-row>

      <!-- 层级结构分析 -->
      <a-divider>层级结构分析</a-divider>
      <a-table 
        :dataSource="hierarchyAnalysis" 
        :columns="analysisColumns"
        :pagination="false"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'distribution'">
            <a-progress 
              :percent="record.percentage" 
              size="small"
              :status="record.percentage > 80 ? 'success' : 'normal'"
            />
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 性能监控 -->
    <a-card v-if="performanceData.length > 0" class="performance-monitor" title="⚡ 性能监控">
      <a-table 
        :dataSource="performanceData" 
        :columns="performanceColumns"
        :pagination="false"
        size="small"
      />
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  PlayCircleOutlined,
  DatabaseOutlined,
  MergeCellsOutlined,
  DashboardOutlined,
  ClearOutlined,
  EyeOutlined
} from '@ant-design/icons-vue'
import TableMergeMultipleDemo from './TableMergeMultipleDemo.vue'
import TableMergeMultiplePage from './TableMergeMultiplePage.vue'
import { multipleTableData } from './mockTableMultiple.js'

// 组件引用
const multipleDemoRef = ref(null)

// 响应式数据
const showComponents = ref(false)
const testResults = ref([])
const currentData = ref([])
const performanceData = ref([])

// 计算属性
const uniqueFirstIndicators = computed(() => {
  const ids = new Set(currentData.value.map(item => item.firstIndicatorId))
  return ids.size
})

const uniqueSecondIndicators = computed(() => {
  const ids = new Set(currentData.value.map(item => item.secondIndicatorId))
  return ids.size
})

const totalScoreStandards = computed(() => {
  return currentData.value.length
})

// 层级结构分析
const hierarchyAnalysis = computed(() => {
  const firstLevelGroups = {}
  
  currentData.value.forEach(item => {
    if (!firstLevelGroups[item.firstIndicatorId]) {
      firstLevelGroups[item.firstIndicatorId] = {
        name: item.firstIndicatorName,
        secondIndicators: new Set(),
        scoreStandards: 0
      }
    }
    
    firstLevelGroups[item.firstIndicatorId].secondIndicators.add(item.secondIndicatorId)
    firstLevelGroups[item.firstIndicatorId].scoreStandards++
  })
  
  const total = currentData.value.length
  
  return Object.values(firstLevelGroups).map(group => ({
    firstIndicator: group.name,
    secondIndicatorCount: group.secondIndicators.size,
    scoreStandardCount: group.scoreStandards,
    percentage: Math.round((group.scoreStandards / total) * 100)
  }))
})

// 表格列配置
const analysisColumns = [
  { title: '一级指标', dataIndex: 'firstIndicator', key: 'firstIndicator' },
  { title: '二级指标数', dataIndex: 'secondIndicatorCount', key: 'secondIndicatorCount', align: 'center' },
  { title: '评分标准数', dataIndex: 'scoreStandardCount', key: 'scoreStandardCount', align: 'center' },
  { title: '占比分布', dataIndex: 'distribution', key: 'distribution', width: 200 }
]

const performanceColumns = [
  { title: '测试项目', dataIndex: 'testName', key: 'testName' },
  { title: '执行时间(ms)', dataIndex: 'duration', key: 'duration', align: 'center' },
  { title: '数据量', dataIndex: 'dataSize', key: 'dataSize', align: 'center' },
  { title: '状态', dataIndex: 'status', key: 'status', align: 'center' }
]

/**
 * 数据转换函数（复制自组件）
 */
const transformMultipleTableData = (originalData) => {
  const flatData = []
  
  originalData.forEach((firstLevel) => {
    if (firstLevel.secondIndicators && firstLevel.secondIndicators.length > 0) {
      firstLevel.secondIndicators.forEach((secondLevel) => {
        if (secondLevel.indicatorScore && Array.isArray(secondLevel.indicatorScore)) {
          secondLevel.indicatorScore.forEach((scoreStandard) => {
            flatData.push({
              firstIndicatorId: firstLevel.id,
              firstIndicatorName: firstLevel.name,
              secondIndicatorId: secondLevel.id,
              secondIndicatorName: secondLevel.name,
              scoreStandardId: scoreStandard.id,
              content: scoreStandard.content || '',
              minScore: scoreStandard.minScore || 0,
              maxScore: scoreStandard.maxScore || 100,
              scoreCardName: scoreStandard.scoreCardName || '',
              evalScoreTypeList: scoreStandard.evalScoreTypeList || [],
              othersIndicatorScore: scoreStandard.othersIndicatorScore,
              thisIndicatorScore: scoreStandard.thisIndicatorScore,
              totalIndicatorScore: scoreStandard.totalIndicatorScore,
              comment: scoreStandard.comment || ''
            })
          })
        }
      })
    }
  })
  
  return flatData
}

/**
 * 添加测试结果
 */
const addTestResult = (title, description, success, details = null) => {
  testResults.value.push({
    title,
    description,
    success,
    details,
    timestamp: new Date().toLocaleTimeString()
  })
}

/**
 * 测试数据结构
 */
const testDataStructure = () => {
  const startTime = performance.now()
  
  try {
    // 测试原始数据格式
    if (!Array.isArray(multipleTableData) || multipleTableData.length === 0) {
      addTestResult('原始数据检查', '原始数据为空或格式错误', false)
      return
    }
    
    addTestResult('原始数据检查', `包含 ${multipleTableData.length} 个一级指标`, true, {
      '一级指标数': multipleTableData.length
    })

    // 测试二级指标结构
    let totalSecondIndicators = 0
    let totalScoreStandards = 0
    
    multipleTableData.forEach(first => {
      if (first.secondIndicators && Array.isArray(first.secondIndicators)) {
        totalSecondIndicators += first.secondIndicators.length
        
        first.secondIndicators.forEach(second => {
          if (second.indicatorScore && Array.isArray(second.indicatorScore)) {
            totalScoreStandards += second.indicatorScore.length
          }
        })
      }
    })
    
    addTestResult('层级结构检查', '三层数据结构完整', true, {
      '二级指标总数': totalSecondIndicators,
      '评分标准总数': totalScoreStandards
    })

    // 测试数据转换
    const transformedData = transformMultipleTableData(multipleTableData)
    currentData.value = transformedData
    
    if (transformedData.length > 0) {
      addTestResult('数据转换测试', `成功转换为 ${transformedData.length} 行扁平化数据`, true, {
        '转换行数': transformedData.length,
        '原始层级': '3层',
        '目标格式': '扁平化'
      })
    } else {
      addTestResult('数据转换测试', '数据转换失败', false)
    }

    // 测试必要字段
    const requiredFields = [
      'firstIndicatorId', 'firstIndicatorName',
      'secondIndicatorId', 'secondIndicatorName', 
      'scoreStandardId', 'content'
    ]
    
    const hasAllFields = transformedData.every(item => 
      requiredFields.every(field => item.hasOwnProperty(field))
    )
    
    addTestResult('字段完整性检查', hasAllFields ? '所有必要字段完整' : '缺少必要字段', hasAllFields)

    const endTime = performance.now()
    performanceData.value.push({
      testName: '数据结构测试',
      duration: Math.round(endTime - startTime),
      dataSize: transformedData.length,
      status: '完成'
    })
    
  } catch (error) {
    addTestResult('数据结构测试异常', error.message, false)
  }
}

/**
 * 测试合并逻辑
 */
const testMergeLogic = () => {
  const startTime = performance.now()
  
  try {
    const data = currentData.value.length > 0 ? currentData.value : transformMultipleTableData(multipleTableData)
    
    // 测试一级指标合并
    const firstLevelGroups = {}
    data.forEach((item, index) => {
      const id = item.firstIndicatorId
      if (!firstLevelGroups[id]) {
        firstLevelGroups[id] = []
      }
      firstLevelGroups[id].push({ item, index })
    })
    
    const firstLevelGroupCount = Object.keys(firstLevelGroups).length
    addTestResult('一级指标分组', `${data.length} 行数据分为 ${firstLevelGroupCount} 个一级指标组`, true, {
      '总行数': data.length,
      '一级指标组数': firstLevelGroupCount
    })

    // 测试二级指标合并
    const secondLevelGroups = {}
    data.forEach((item, index) => {
      const id = item.secondIndicatorId
      if (!secondLevelGroups[id]) {
        secondLevelGroups[id] = []
      }
      secondLevelGroups[id].push({ item, index })
    })
    
    const secondLevelGroupCount = Object.keys(secondLevelGroups).length
    addTestResult('二级指标分组', `${data.length} 行数据分为 ${secondLevelGroupCount} 个二级指标组`, true, {
      '总行数': data.length,
      '二级指标组数': secondLevelGroupCount
    })

    // 测试合并计算逻辑
    let mergeTestPassed = true
    let mergeDetails = {
      '一级指标合并点': 0,
      '二级指标合并点': 0,
      '独立评分标准': 0
    }
    
    Object.values(firstLevelGroups).forEach(group => {
      if (group.length > 1) {
        mergeDetails['一级指标合并点']++
      }
    })
    
    Object.values(secondLevelGroups).forEach(group => {
      if (group.length > 1) {
        mergeDetails['二级指标合并点']++
      }
    })
    
    mergeDetails['独立评分标准'] = data.length
    
    addTestResult('合并逻辑验证', '多层级合并逻辑正确', mergeTestPassed, mergeDetails)

    const endTime = performance.now()
    performanceData.value.push({
      testName: '合并逻辑测试',
      duration: Math.round(endTime - startTime),
      dataSize: data.length,
      status: '完成'
    })
    
  } catch (error) {
    addTestResult('合并逻辑测试异常', error.message, false)
  }
}

/**
 * 性能测试
 */
const testPerformance = () => {
  const startTime = performance.now()
  
  try {
    // 测试大数据量转换性能
    const largeData = []
    for (let i = 0; i < 10; i++) {
      largeData.push(...multipleTableData)
    }
    
    const transformStart = performance.now()
    const transformedLargeData = transformMultipleTableData(largeData)
    const transformEnd = performance.now()
    
    const transformDuration = transformEnd - transformStart
    
    addTestResult('大数据量转换性能', `${largeData.length} 条原始数据转换为 ${transformedLargeData.length} 行，耗时 ${transformDuration.toFixed(2)}ms`, true, {
      '原始数据量': largeData.length,
      '转换后行数': transformedLargeData.length,
      '转换耗时': `${transformDuration.toFixed(2)}ms`
    })

    // 测试合并计算性能
    const mergeStart = performance.now()
    
    // 模拟合并计算
    const mergeResults = transformedLargeData.map((item, index) => {
      // 模拟一级指标合并计算
      let firstRowSpan = 1
      for (let i = index + 1; i < transformedLargeData.length; i++) {
        if (transformedLargeData[i].firstIndicatorId === item.firstIndicatorId) {
          firstRowSpan++
        } else {
          break
        }
      }
      
      // 模拟二级指标合并计算
      let secondRowSpan = 1
      for (let i = index + 1; i < transformedLargeData.length; i++) {
        if (transformedLargeData[i].secondIndicatorId === item.secondIndicatorId) {
          secondRowSpan++
        } else {
          break
        }
      }
      
      return { firstRowSpan, secondRowSpan }
    })
    
    const mergeEnd = performance.now()
    const mergeDuration = mergeEnd - mergeStart
    
    addTestResult('合并计算性能', `${transformedLargeData.length} 行数据合并计算耗时 ${mergeDuration.toFixed(2)}ms`, true, {
      '计算行数': transformedLargeData.length,
      '计算耗时': `${mergeDuration.toFixed(2)}ms`,
      '平均耗时': `${(mergeDuration / transformedLargeData.length).toFixed(4)}ms/行`
    })

    const endTime = performance.now()
    performanceData.value.push({
      testName: '性能测试',
      duration: Math.round(endTime - startTime),
      dataSize: transformedLargeData.length,
      status: '完成'
    })
    
  } catch (error) {
    addTestResult('性能测试异常', error.message, false)
  }
}

/**
 * 运行全部测试
 */
const runAllTests = async () => {
  clearResults()
  message.info('开始运行全部测试...')
  
  await new Promise(resolve => setTimeout(resolve, 100))
  testDataStructure()
  
  await new Promise(resolve => setTimeout(resolve, 100))
  testMergeLogic()
  
  await new Promise(resolve => setTimeout(resolve, 100))
  testPerformance()
  
  message.success('全部测试完成！')
}

/**
 * 清空测试结果
 */
const clearResults = () => {
  testResults.value = []
  performanceData.value = []
  currentData.value = []
}

/**
 * 处理数据变化
 */
const handleDataChange = (newData) => {
  currentData.value = newData
}

// 组件挂载时自动运行基础测试
onMounted(() => {
  message.info('多层级表格合并测试页面已加载')
  // 自动加载基础数据
  currentData.value = transformMultipleTableData(multipleTableData)
})
</script>

<style scoped>
.test-multiple-page {
  padding: 20px;
  background: #f0f2f5;
  min-height: 100vh;
}

.test-controls {
  margin-bottom: 20px;
}

.test-results {
  margin-bottom: 20px;
}

.test-success {
  color: #52c41a;
  font-weight: 500;
}

.test-error {
  color: #ff4d4f;
  font-weight: 500;
}

.test-details {
  margin-top: 8px;
}

.test-time {
  color: #999;
  font-size: 12px;
}

.components-demo {
  margin-bottom: 20px;
}

.demo-card {
  margin-bottom: 20px;
}

.data-analysis {
  margin-bottom: 20px;
}

.data-analysis .ant-col {
  text-align: center;
  margin-bottom: 16px;
}

.performance-monitor {
  margin-bottom: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .test-multiple-page {
    padding: 16px;
  }
  
  .data-analysis .ant-col {
    margin-bottom: 16px;
  }
}
</style>
