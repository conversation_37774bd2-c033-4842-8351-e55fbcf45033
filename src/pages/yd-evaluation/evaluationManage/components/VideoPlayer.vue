<template>
    <div class="video-player-container">
        <!-- xgplayer容器 -->
        <div ref="xgplayerRef" class="xgplayer-container"></div>
    </div>
</template>

<script setup name="VideoPlayer">
/**
 * 视频播放组件
 * 使用xgplayer 3.0.22实现
 */

import Player from 'xgplayer'
import 'xgplayer/dist/index.min.css'

const props = defineProps({
    // 视频源地址
    src: {
        type: String,
        required: true,
    },
    // 播放器宽度
    width: {
        type: [String, Number],
        default: '100%',
    },
    // 播放器高度
    height: {
        type: [String, Number],
        default: '400px',
    },
    // 是否自动播放
    autoplay: {
        type: Boolean,
        default: false,
    },
    // 是否静音
    muted: {
        type: Boolean,
        default: false,
    },
    // 是否循环播放
    loop: {
        type: Boolean,
        default: false,
    },
    // 是否显示控制栏
    controls: {
        type: Boolean,
        default: true,
    },
})

const emit = defineEmits(['loadedmetadata', 'error', 'play', 'pause', 'ended', 'ready'])

const xgplayerRef = ref(null)
let playerInstance = null
let resizeObserver = null

// 获取容器尺寸
const getContainerSize = () => {
    if (!xgplayerRef.value) return { width: 0, height: 0 }

    const container = xgplayerRef.value.parentElement
    const rect = container.getBoundingClientRect()

    return {
        width: rect.width || 800,
        height: rect.height || 450,
    }
}

// 计算播放器配置
const getPlayerConfig = () => {
    const containerSize = getContainerSize()

    // 如果启用流体布局，使用容器尺寸
    if (props.fluid) {
        return {
            width: containerSize.width,
            height: containerSize.height,
            fluid: true,
            fitVideoSize: props.fitMode,
        }
    }

    // 否则使用props指定的尺寸
    return {
        width: props.width,
        height: props.height,
        fluid: false,
        fitVideoSize: props.fitMode,
    }
}

// 初始化xgplayer
const initPlayer = () => {
    if (!xgplayerRef.value || !props.src) return

    try {
        // 如果已有实例，先销毁
        if (playerInstance) {
            playerInstance.destroy()
            playerInstance = null
        }

        const playerConfig = getPlayerConfig()

        playerInstance = new Player({
            el: xgplayerRef.value,
            url: props.src,
            ...playerConfig,
            autoplay: props.autoplay,
            muted: props.muted,
            loop: props.loop,
            playsinline: true,
            controls: props.controls,
            // 基础配置
            lang: 'zh-cn',
            // 响应式配置
            responsive: true,
            // 视频适配配置
            videoInit: true,
            // 事件监听
            onReady: () => {
                console.log('xgplayer ready')
                // 确保播放器适应容器
                resizePlayer()
                // 设置视频适配模式
                updateVideoFitMode()
                emit('ready')
            },
            onLoadedMetadata: () => {
                emit('loadedmetadata', {
                    duration: playerInstance?.duration,
                    videoWidth: playerInstance?.videoWidth,
                    videoHeight: playerInstance?.videoHeight,
                })
            },
            onPlay: () => emit('play'),
            onPause: () => emit('pause'),
            onEnded: () => emit('ended'),
            onError: (error) => {
                console.error('xgplayer error:', error)
                emit('error', error)
                YMessage.error('视频播放出错')
            },
        })
    } catch (error) {
        console.error('xgplayer初始化失败:', error)
        YMessage.error('视频播放器初始化失败')
    }
}

// 调整播放器尺寸
const resizePlayer = () => {
    if (!playerInstance || !props.fluid) return

    nextTick(() => {
        const containerSize = getContainerSize()
        if (containerSize.width > 0 && containerSize.height > 0) {
            try {
                playerInstance.resize(containerSize.width, containerSize.height)
                // 同时更新视频适配模式
                updateVideoFitMode()
            } catch (error) {
                console.warn('播放器尺寸调整失败:', error)
            }
        }
    })
}

// 更新视频适配模式
const updateVideoFitMode = () => {
    if (!playerInstance) return

    nextTick(() => {
        const videoElement = xgplayerRef.value?.querySelector('video')
        if (videoElement) {
            videoElement.style.objectFit = props.fitMode
        }
    })
}

// 播放视频
const play = () => {
    return playerInstance?.play()
}

// 暂停视频
const pause = () => {
    playerInstance?.pause()
}

// 设置当前播放时间
const setCurrentTime = (time) => {
    if (playerInstance) {
        playerInstance.currentTime = time
    }
}

// 获取当前播放时间
const getCurrentTime = () => {
    return playerInstance?.currentTime || 0
}

// 获取视频总时长
const getDuration = () => {
    return playerInstance?.duration || 0
}

// 设置音量 (0-1)
const setVolume = (volume) => {
    if (playerInstance) {
        playerInstance.volume = Math.max(0, Math.min(1, volume))
    }
}

// 获取音量
const getVolume = () => {
    return playerInstance?.volume || 0
}

// 设置播放速度
const setPlaybackRate = (rate) => {
    if (playerInstance) {
        playerInstance.playbackRate = rate
    }
}

// 进入全屏
const requestFullscreen = () => {
    if (playerInstance && typeof playerInstance.requestFullscreen === 'function') {
        playerInstance.requestFullscreen()
    }
}

// 监听props变化
watch(() => props.src, (newSrc) => {
    if (newSrc) {
        nextTick(() => {
            initPlayer()
        })
    }
})

// 监听适配模式变化
watch(() => props.fitMode, () => {
    updateVideoFitMode()
})

// 设置容器尺寸监听
const setupResizeObserver = () => {
    if (!props.fluid || !xgplayerRef.value) return

    // 清理旧的观察器
    if (resizeObserver) {
        resizeObserver.disconnect()
    }

    // 创建新的观察器
    resizeObserver = new ResizeObserver(() => {
        // 防抖处理，避免频繁调整
        clearTimeout(resizeObserver.timer)
        resizeObserver.timer = setTimeout(() => {
            resizePlayer()
        }, 100)
    })

    // 观察容器元素
    const container = xgplayerRef.value.parentElement
    if (container) {
        resizeObserver.observe(container)
    }
}

// 组件挂载后初始化
onMounted(() => {
    nextTick(() => {
        initPlayer()
        // 设置尺寸监听
        if (props.fluid) {
            setupResizeObserver()
        }
    })
})

// 组件卸载时清理
onUnmounted(() => {
    // 清理播放器实例
    if (playerInstance && typeof playerInstance.destroy === 'function') {
        playerInstance.destroy()
        playerInstance = null
    }

    // 清理尺寸观察器
    if (resizeObserver) {
        clearTimeout(resizeObserver.timer)
        resizeObserver.disconnect()
        resizeObserver = null
    }
})

// 暴露方法给父组件
defineExpose({
    play,
    pause,
    setCurrentTime,
    getCurrentTime,
    getDuration,
    setVolume,
    getVolume,
    setPlaybackRate,
    requestFullscreen,
    playerInstance: () => playerInstance,
})
</script>

<style lang="less" scoped>
.video-player-container {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 200px; // 最小高度
    display: flex;
    align-items: center;
    justify-content: center;
    background: #000;
    border-radius: 6px;
    overflow: hidden;

    .xgplayer-container {
        position: relative;
        width: 100%;
        height: 100%;

        // xgplayer样式覆盖
        :deep(.xgplayer) {
            position: relative !important;
            width: 100% !important;
            height: 100% !important;
            border-radius: 6px;
            background: #000;
        }

        // 视频元素样式
        :deep(.xgplayer-video) {
            width: 100% !important;
            height: 100% !important;
            object-fit: contain; // 默认保持比例
        }

        // 控制栏样式
        :deep(.xgplayer-controls) {
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.8)) !important;
        }

        // 播放按钮样式
        :deep(.xgplayer-play) {
            color: #1890ff !important;
        }

        // 进度条样式
        :deep(.xgplayer-progress-outer) {
            background: rgba(255, 255, 255, 0.3) !important;
        }

        :deep(.xgplayer-progress-cache),
        :deep(.xgplayer-progress-played) {
            background: #1890ff !important;
        }

        // 音量控制样式
        :deep(.xgplayer-volume-progress) {
            background: #1890ff !important;
        }

        // 时间显示样式
        :deep(.xgplayer-time) {
            color: white !important;
        }

        // 全屏按钮样式
        :deep(.xgplayer-fullscreen) {
            color: white !important;
        }

        // 加载状态样式
        :deep(.xgplayer-loading) {
            background: rgba(0, 0, 0, 0.8) !important;
        }

        // 错误状态样式
        :deep(.xgplayer-error) {
            background: rgba(0, 0, 0, 0.8) !important;
            color: white !important;
        }
    }
}

// 响应式适配
@media (max-width: 768px) {
    .video-player-container {
        min-height: 150px;

        .xgplayer-container {
            :deep(.xgplayer-controls) {
                height: 40px !important; // 移动端控制栏高度
            }

            :deep(.xgplayer-time) {
                font-size: 12px !important; // 移动端字体大小
            }
        }
    }
}
</style>
