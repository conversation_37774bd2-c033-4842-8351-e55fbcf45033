/**
 * 测试数据 - 用于验证表格comment和Paths列的功能
 */

export const testTableData = [
    {
        id: 1,
        firstIndicatorName: '学习态度',
        secondIndicatorName: '课堂参与度',
        content: '积极参与课堂讨论，主动回答问题',
        minScore: 0,
        maxScore: 10,
        scoreCardName: '课堂表现卡',
        evalScoreTypeList: ['text'], // 只能文字评语
        othersIndicatorScore: 8.5,
        thisIndicatorScore: null,
        totalIndicatorScore: null,
        comment: '', // 评语字段
        imgPaths: '', // 图片路径
        videoPaths: '', // 视频路径
    },
    {
        id: 2,
        firstIndicatorName: '学习态度',
        secondIndicatorName: '作业完成质量',
        content: '按时完成作业，质量优秀',
        minScore: 0,
        maxScore: 10,
        scoreCardName: '作业表现卡',
        evalScoreTypeList: ['image', 'text'], // 图片和文字评语
        othersIndicatorScore: 9.0,
        thisIndicatorScore: null,
        totalIndicatorScore: null,
        comment: '',
        imgPaths: '',
        videoPaths: '',
    },
    {
        id: 3,
        firstIndicatorName: '实践能力',
        secondIndicatorName: '实验操作',
        content: '实验操作规范，结果准确',
        minScore: 0,
        maxScore: 15,
        scoreCardName: '实验技能卡',
        evalScoreTypeList: ['image', 'video', 'text'], // 全部类型
        othersIndicatorScore: 12.0,
        thisIndicatorScore: null,
        totalIndicatorScore: null,
        comment: '',
        imgPaths: '',
        videoPaths: '',
    },
    {
        id: 4,
        firstIndicatorName: '实践能力',
        secondIndicatorName: '项目展示',
        content: '项目展示清晰，表达流畅',
        minScore: 0,
        maxScore: 20,
        scoreCardName: '展示技能卡',
        evalScoreTypeList: ['video'], // 只能视频
        othersIndicatorScore: 16.0,
        thisIndicatorScore: null,
        totalIndicatorScore: null,
        comment: '',
        imgPaths: '',
        videoPaths: '',
    },
    {
        id: 5,
        firstIndicatorName: '团队协作',
        secondIndicatorName: '沟通能力',
        content: '与团队成员沟通良好，协作有效',
        minScore: 0,
        maxScore: 10,
        scoreCardName: '协作能力卡',
        evalScoreTypeList: ['image'], // 只能图片
        othersIndicatorScore: 8.0,
        thisIndicatorScore: null,
        totalIndicatorScore: null,
        comment: '',
        imgPaths: '',
        videoPaths: '',
    }
]

/**
 * 模拟evalScoreTypeList的不同组合情况
 */
export const evalScoreTypeExamples = {
    textOnly: ['text'], // 只能文字评语
    imageOnly: ['image'], // 只能图片
    videoOnly: ['video'], // 只能视频
    imageAndText: ['image', 'text'], // 图片和文字
    videoAndText: ['video', 'text'], // 视频和文字
    imageAndVideo: ['image', 'video'], // 图片和视频
    all: ['image', 'video', 'text'], // 全部类型
}

/**
 * 测试用的媒体文件URL（示例）
 */
export const testMediaUrls = {
    images: [
        'https://via.placeholder.com/300x200/4CAF50/white?text=Image+1',
        'https://via.placeholder.com/300x200/2196F3/white?text=Image+2',
        'https://via.placeholder.com/300x200/FF9800/white?text=Image+3',
    ],
    videos: [
        'https://www.w3schools.com/html/mov_bbb.mp4',
        'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
    ]
}

/**
 * 修复后的功能测试用例
 */
export const testCases = [
    {
        name: '测试文字评语功能',
        description: '验证当evalScoreTypeList包含text时，显示评语输入框',
        data: {
            evalScoreTypeList: ['text'],
            comment: '这是一个测试评语'
        },
        expected: '应该显示评语输入框，可以输入和编辑文字，最大200字符'
    },
    {
        name: '测试统一上传按钮 - 仅图片',
        description: '验证当evalScoreTypeList只包含image时，上传按钮配置',
        data: {
            evalScoreTypeList: ['image'],
            imgPaths: testMediaUrls.images[0]
        },
        expected: '应该显示"上传图片"按钮，accept="image/*"，上传后显示第一张图片缩略图'
    },
    {
        name: '测试统一上传按钮 - 仅视频',
        description: '验证当evalScoreTypeList只包含video时，上传按钮配置',
        data: {
            evalScoreTypeList: ['video'],
            videoPaths: testMediaUrls.videos[0]
        },
        expected: '应该显示"上传视频"按钮，accept="video/*"，上传后显示第一个视频缩略图'
    },
    {
        name: '测试统一上传按钮 - 图片和视频',
        description: '验证当evalScoreTypeList包含image和video时，上传按钮配置',
        data: {
            evalScoreTypeList: ['image', 'video'],
            imgPaths: testMediaUrls.images.join(','),
            videoPaths: testMediaUrls.videos.join(',')
        },
        expected: '应该显示"上传文件"按钮，accept="image/*,video/*"，优先显示图片缩略图，有多文件指示器'
    },
    {
        name: '测试多媒体组合',
        description: '验证同时支持多种评分方式时的界面显示',
        data: {
            evalScoreTypeList: ['image', 'video', 'text'],
            comment: '综合评价测试',
            imgPaths: testMediaUrls.images.join(','),
            videoPaths: testMediaUrls.videos.join(',')
        },
        expected: '应该同时显示文字输入框和统一上传按钮，只显示一个缩略图'
    },
    {
        name: '测试新预览组件',
        description: '验证点击缩略图时打开MediaPreviewModal组件',
        data: {
            evalScoreTypeList: ['image', 'video'],
            imgPaths: testMediaUrls.images.join(','),
            videoPaths: testMediaUrls.videos.join(',')
        },
        expected: '点击缩略图应该打开MediaPreviewModal，使用xgplayer播放视频'
    },
    {
        name: '测试文件类型验证',
        description: '验证上传文件时的类型检查',
        data: {
            evalScoreTypeList: ['image'],
            testFile: { type: 'video/mp4' }
        },
        expected: '上传视频文件时应该提示"当前评分方式不支持上传视频"'
    },
    {
        name: '测试上传接口调用',
        description: '验证上传接口调用格式是否正确',
        data: {
            evalScoreTypeList: ['image'],
            testFile: { type: 'image/jpeg' }
        },
        expected: '应该调用http.form("/file/common/upload", { file, folderType: "evalActivity" }, {})，返回res.data[0].url'
    }
]

/**
 * 修复验证清单
 */
export const fixVerificationList = [
    {
        item: '上传接口调用方式',
        description: '使用正确的http.form调用格式',
        status: '✅ 已修复',
        details: 'http.form("/file/common/upload", { file, folderType: "evalActivity" }, {})'
    },
    {
        item: '统一上传按钮',
        description: '图片和视频合并为一个上传按钮',
        status: '✅ 已修复',
        details: '根据evalScoreTypeList动态配置accept属性和按钮文字'
    },
    {
        item: '缩略图显示逻辑',
        description: '只显示第一个文件的缩略图',
        status: '✅ 已修复',
        details: '优先显示图片，有多文件时显示+N指示器'
    },
    {
        item: '通用预览组件',
        description: '封装MediaPreviewModal组件',
        status: '✅ 已修复',
        details: '接收imgPaths和videoPaths，支持轮播预览'
    },
    {
        item: 'xgplayer视频播放器',
        description: '直接使用xgplayer而不是原生video',
        status: '✅ 已修复',
        details: '使用项目中已有的xgplayer 3.0.22依赖'
    },
    {
        item: '文件类型验证',
        description: '在before-upload中进行文件类型检查',
        status: '✅ 已修复',
        details: '根据evalScoreTypeList验证允许的文件类型'
    }
]
