<!-- 评价审核评价审核评价审核评价审核评价审核 -->
<template>
    <YDrawer class="drawer_box" v-model:open="state.open" title="评价审核" @closebtn="handleClose">
        <a-spin :spinning="spinning">
            <div class="evaluationDetailedPage" h-full flex>
                <div class="treeListBox" h-full flex flex-col>
                    <div p-b-16>
                        <a-input-search placeholder="请输入姓名" v-model:value="state.toPersonName" @search="searchDevice" />
                    </div>
                    <div flex-auto overflow-y-auto class="tree_box" v-if="state.treeData.length">
                        <a-tree
                            block-node
                            :virtual="true"
                            :defaultExpandAll="true"
                            :autoExpandParent="true"
                            v-model:selectedKeys="selectedKeysArr"
                            :fieldNames="{
                                children: 'children',
                                title: 'name',
                                key: 'id',
                            }"
                            @select="handleSelect"
                            :tree-data="state.treeData"
                        >
                            <template #title="{ name, isApprove, levelType }">
                                <span :style="{ color: color[isApprove] }">
                                    <span
                                        v-if="levelType === 2"
                                        class="status_box"
                                        :style="{
                                            color: color[isApprove],
                                            borderColor: color[isApprove],
                                        }"
                                    >
                                        {{ isApprove ? '已审核' : '未审核' }}
                                    </span>
                                    {{ name }}
                                </span>
                            </template>
                        </a-tree>
                    </div>
                </div>

                <div class="appraiseTable">
                    <div class="infoBox">
                        <a-row :gutter="[16, 16]">
                            <a-col :span="12">
                                <div>
                                    评价类型：
                                    <span>{{ state.rulePersonDetails.evalTypeName || '-' }}</span>
                                </div>
                            </a-col>
                            <a-col :span="12">
                                <div>
                                    活动名称：
                                    <span>{{ state.rulePersonDetails.title || '-' }}</span>
                                </div>
                            </a-col>
                            <a-col :span="12">
                                <div>
                                    姓名：
                                    <span>{{ state.rulePersonDetails.toPersonName || '-' }}</span>
                                </div>
                            </a-col>
                            <a-col :span="12">
                                <div>
                                    班级：
                                    <span>{{ state.rulePersonDetails.orgName || '-' }}</span>
                                </div>
                            </a-col>
                        </a-row>
                    </div>

                    <div class="listBox">
                        <div class="list_arr">
                            <div class="list_item" v-for="(item, index) in state.tableList">
                                <a-row justify="space-between">
                                    <a-col :span="6">
                                        <div class="item_titleBox">
                                            <div class="item_line"></div>
                                            <div class="item_title">第{{ item.thisCount }}次评价</div>
                                        </div>
                                    </a-col>
                                    <a-col :span="6">
                                        <div class="list_item_sub">
                                            最后得分：
                                            <span>{{ item.score }}</span>
                                        </div>
                                    </a-col>
                                    <a-col :span="6">
                                        <div class="list_item_sub">
                                            得分时间：
                                            <span>{{ item.scoreTime }}</span>
                                        </div>
                                    </a-col>
                                    <a-col :span="2">
                                        <div class="list_item_sub spread" @click="showDetails(item)">
                                            评价详情
                                            <RightOutlined v-if="!item.showDetails" />
                                            <DownOutlined v-if="item.showDetails" />
                                        </div>
                                    </a-col>
                                </a-row>
                                <div class="onlyShowOne" v-show="item.showDetails">
                                    <div class="detailsTableBox_table">
                                        <a-table
                                            :scroll="{ x: 1500 }"
                                            :dataSource="state.dataSource"
                                            :columns="columns"
                                            bordered
                                            :pagination="false"
                                        >
                                            <template #headerCell="{ column }">
                                                <template v-if="column.key === 'othersIndicatorScore'">
                                                    <a-tooltip :open="tooltipOpen" color="red">
                                                        <template #title>还有{{ state.notEvalScoreNum }}人未评价</template>
                                                        他人评分
                                                    </a-tooltip>
                                                </template>
                                            </template>
                                            <template #bodyCell="{ column, record }">
                                                <template v-if="column.key === 'minScore'">
                                                    {{ record.minScore }}-{{ record.maxScore }}
                                                </template>
                                                <template v-if="column.key === 'approveScore'">
                                                    <a-input placeholder="请评分" v-model:value="record.approveScore"></a-input>
                                                </template>
                                                <template v-if="column.key === 'remark'">
                                                    <a-input placeholder="请输入" v-model:value="record.remark"></a-input>
                                                </template>
                                                <template v-if="column.key === 'comment'">
                                                    <div>
                                                        {{ record.comment }}
                                                    </div>
                                                    <div class="paths">
                                                        <div v-if="getFirstMediaFile(record)" class="thumbnail-item">
                                                            <!-- 图片缩略图 -->
                                                            <img
                                                                v-if="getFirstMediaFile(record).type === 'image'"
                                                                :src="getFirstMediaFile(record).url"
                                                                alt="缩略图"
                                                            />
                                                            <!-- 视频缩略图 -->
                                                            <video
                                                                v-else-if="getFirstMediaFile(record).type === 'video'"
                                                                :src="getFirstMediaFile(record).url"
                                                                muted
                                                            ></video>
                                                            <div class="thumbnail-overlay">
                                                                <PlayCircleOutlined v-if="getFirstMediaFile(record).type === 'video'" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="moreBtn" @click="openPreviewModal(record)">
                                                        更多
                                                        <RightOutlined />
                                                    </div>
                                                </template>
                                            </template>
                                        </a-table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="footer_box">
                            <a-pagination
                                v-model:current="state.query.pageNo"
                                v-model:page-size="state.query.pageSize"
                                :hideOnSinglePage="true"
                                :showLessItems="true"
                                :showQuickJumper="true"
                                :showSizeChanger="true"
                                :pageSizeOptions="['10', '20', '50', '100']"
                                :total="state.query.total"
                                :show-total="total => `共 ${total} 条`"
                                @change="onPaginationChange"
                            />
                        </div>
                    </div>

                    <div class="comment">
                        <div class="totalBox">
                            <span class="redBox">*</span>
                            最终得分：
                            <span class="fenBox">{{ state.rulePersonDetails.totalScore }}</span>
                            分
                        </div>

                        <div class="commentBox">
                            <div class="commentBox_left">学生评语：</div>
                            <div class="commentBox_right">
                                <a-textarea placeholder="请输入" v-model:value="state.comment" show-count :maxlength="2000" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </a-spin>
        <template #footer>
            <a-button mr-5 @click="handleClose">取消</a-button>
            <a-button type="primary" @click="handleConfirm">确定</a-button>
        </template>
    </YDrawer>
    <PreviewModal ref="previewModalRef" />
</template>
<script setup name="evaluationAudit">
import PreviewModal from './PreviewModal.vue'

// 定义一个函数，判断数组是否为非空数组
function isNonEmptyArray(arr) {
    return Array.isArray(arr) && arr.length > 0
}

const tooltipOpen = ref(true)
const previewModalRef = ref(null)
const state = reactive({
    open: false,
    toPersonName: '',
    treeData: [],
    query: {
        queryThisFrom: false,
        pageNo: 1,
        pageSize: 10,
    },
    tableList: [],
    dataSource: [],
    rulePersonDetails: {},
    comment: '',
    notEvalScoreNum: '', // 未评分人数
})

const color = {
    null: '',
    0: '',
    false: '#c5c5c5',
    true: '#00B781',
}

// 获取图片列表
const getImageList = imgPaths => {
    if (!imgPaths) return []
    return imgPaths.split(',').filter(path => path.trim())
}

// 获取视频列表
const getVideoList = videoPaths => {
    if (!videoPaths) return []
    return videoPaths.split(',').filter(path => path.trim())
}

// 获取第一个媒体文件
const getFirstMediaFile = record => {
    // 优先显示图片
    if (record.imgPaths) {
        const imageList = getImageList(record.imgPaths)
        if (imageList.length > 0) {
            return { type: 'image', url: imageList[0] }
        }
    }

    // 然后显示视频
    if (record.videoPaths) {
        const videoList = getVideoList(record.videoPaths)
        if (videoList.length > 0) {
            return { type: 'video', url: videoList[0] }
        }
    }

    return null
}

/**
 * 数据转换函数 - 将嵌套数据转换为扁平化表格数据
 */
const transformTableData = originalData => {
    const flatData = []

    originalData.forEach(firstLevel => {
        // 如果一级指标下有二级指标
        if (firstLevel.secondIndicators && firstLevel.secondIndicators.length > 0) {
            firstLevel.secondIndicators.forEach(secondLevel => {
                const indicatorScore = secondLevel.indicatorScore || {}

                flatData.push({
                    // 一级指标信息
                    firstIndicatorId: firstLevel.id,
                    firstIndicatorName: firstLevel.name,

                    // 二级指标信息
                    secondIndicatorId: secondLevel.id,
                    secondIndicatorName: secondLevel.name,

                    // 评分相关信息
                    content: indicatorScore.content || '',
                    minScore: indicatorScore.minScore || 0,
                    maxScore: indicatorScore.maxScore || 100,
                    scoreCardName: indicatorScore.scoreCardName || '',
                    evalScoreTypeList: indicatorScore.evalScoreTypeList || [],

                    // 评分数据
                    othersIndicatorScore: indicatorScore.othersIndicatorScore,
                    thisIndicatorScore: indicatorScore.thisIndicatorScore,
                    totalIndicatorScore: indicatorScore.totalIndicatorScore,

                    // 审核后得分
                    approveScore: indicatorScore.approveScore,

                    // 备注
                    remark: indicatorScore.remark,

                    // 其他信息
                    comment: indicatorScore.comment || '',
                    imgPaths: indicatorScore.imgPaths || '',
                    videoPaths: indicatorScore.videoPaths || '',
                    rulePersonId: indicatorScore.rulePersonId,
                    indicatorId: indicatorScore.id,

                    // 原始数据引用（用于数据回写）
                    _originalFirst: firstLevel,
                    _originalSecond: secondLevel,
                    _originalScore: indicatorScore,
                })
            })
        } else {
            // 如果一级指标下没有二级指标，直接显示一级指标
            flatData.push({
                firstIndicatorId: firstLevel.id,
                firstIndicatorName: firstLevel.name,
                secondIndicatorId: null,
                secondIndicatorName: '',
                content: '',
                minScore: null,
                maxScore: null,
                scoreCardName: '',
                evalScoreTypeList: [],
                othersIndicatorScore: null,
                thisIndicatorScore: null,
                totalIndicatorScore: null,
                comment: '',
                imgPaths: '',
                videoPaths: '',
                _originalFirst: firstLevel,
                _originalSecond: null,
                _originalScore: null,
            })
        }
    })

    return flatData
}

const columns = ref([
    {
        title: '一级指标',
        dataIndex: 'firstIndicatorName',
        key: 'firstIndicatorName',
        width: 150,
        // 核心合并逻辑：根据一级指标ID进行行合并
        customCell: (record, rowIndex) => {
            // 计算当前一级指标需要合并的行数
            let rowSpan = 1

            // 向下查找相同的一级指标
            for (let i = rowIndex + 1; i < state.dataSource.length; i++) {
                if (state.dataSource[i].firstIndicatorId === record.firstIndicatorId) {
                    rowSpan++
                } else {
                    break
                }
            }

            // 如果当前行不是该一级指标的第一行，则隐藏
            if (rowIndex > 0 && state.dataSource[rowIndex - 1].firstIndicatorId === record.firstIndicatorId) {
                return { rowSpan: 0 }
            }

            return { rowSpan }
        },
    },
    {
        title: '二级指标',
        dataIndex: 'secondIndicatorName',
        key: 'secondIndicatorName',
        width: 150,
    },
    {
        title: '评分标准',
        dataIndex: 'content',
        key: 'content',
        width: 150,
    },
    {
        title: '评分范围',
        dataIndex: 'minScore',
        key: 'minScore',
        width: 100,
    },
    {
        title: '积分卡',
        dataIndex: 'scoreCardName',
        key: 'scoreCardName',
        width: 100,
    },
    {
        title: '他人评分',
        dataIndex: 'othersIndicatorScore',
        key: 'othersIndicatorScore',
        width: 100,
    },
    {
        title: '本次评分',
        dataIndex: 'thisIndicatorScore',
        key: 'thisIndicatorScore',
        width: 150,
    },
    {
        title: '最后得分',
        dataIndex: 'totalIndicatorScore',
        key: 'totalIndicatorScore',
        width: 100,
        customHeaderCell: () => ({
            style: {
                background: '#FCF1D2',
            },
        }),
        customCell: () => ({
            style: {
                background: '#FCF1D2',
            },
        }),
    },
    {
        title: '评语/图片/视频',
        dataIndex: 'comment',
        key: 'comment',
    },
    {
        title: '审核后得分',
        dataIndex: 'approveScore',
        key: 'approveScore',
        width: 150,
    },
    {
        title: '备注',
        dataIndex: 'remark',
        key: 'remark',
        width: 150,
    },
])
const selectedKeysArr = ref([])
const spinning = ref(false)

function findNameById(tree, id) {
    // 遍历树形结构数组
    for (let node of tree) {
        // 如果当前节点的id等于要查找的id，则返回当前节点的name
        if (node.toPersonId === id) {
            return node.id
        }
        // 如果当前节点有子节点，则递归调用函数在子节点中查找
        if (node.children) {
            let result = findNameById(node.children, id)
            // 如果在子节点中找到了对应id的name，则直接返回结果
            if (result) {
                return result
            }
        }
    }
    // 如果遍历完整个树仍未找到对应id的name，则返回null或者其他自定义的值
    return null
}

// 树查询
const searchDevice = select => {
    spinning.value = true
    const params = {
        activityId: state.query.activityId,
        toPersonName: state.toPersonName,
        queryThisFrom: state.query.queryThisFrom,
        isApprove: true,
    }
    http.post('/cloud/evalDayRulePerson/getDayRulePersonTerr', params).then(res => {
        state.treeData = res.data
        spinning.value = false
        if (!!select) {
            const selectId = findNameById(state.treeData, select)
            selectedKeysArr.value = [selectId]
            nextTick(() => {
                if (document.getElementsByClassName('ant-tree-treenode-selected').length > 0) {
                    document.getElementsByClassName('ant-tree-treenode-selected')[0].scrollIntoView()
                }
            })
        }
    })
}

// 树点击
const handleSelect = async (selectedKeys, e) => {
    selectedKeysArr.value = selectedKeys
    if (e.node.levelType !== 2) return
    state.query.toPersonId = e.node.toPersonId
    getPageDayPersonScore()
    getEvalRulePersonDetails(e.node.rulePersonId)
}

// 取消
const handleClose = () => {
    // 清除掉所有的数据
    state.treeData = []
    state.tableList = []
    state.dataSource = []
    state.rulePersonDetails = {}
    state.comment = ''
    selectedKeysArr.value = []
    state.open = false
}

// 打开查看评价审核的弹窗
const showModel = async data => {
    state.query.activityId = data.id
    // 查询树列表
    searchDevice()
    state.open = true
}

// 获取参与人评分历史分页
const getPageDayPersonScore = () => {
    http.post('/cloud/evalDayRulePerson/pageDayPersonScore', {
        activityId: state.query.activityId, // 活动id
        toPersonId: state.query.toPersonId, // 参与人Id
        pageNo: state.query.pageNo,
        pageSize: state.query.pageSize,
    }).then(res => {
        state.tableList = res.data.list
        state.query.total = res.data.total
    })
}

// 获取活动评分规则数据
const getEvalRulePersonDetails = rulePersonId => {
    http.post('/cloud/evalDayRulePerson/getEvalRulePersonDetails', {
        rulePersonId: rulePersonId, // 规则人员id
    }).then(res => {
        console.log(res, 11)
        state.rulePersonDetails = res.data
        state.notEvalScoreNum = res.data.notEvalScoreNum
        state.comment = res.data.comment
    })
}

const showDetails = item => {
    state.tableList.forEach(i => {
        if (i.id === item.id) {
            i.showDetails = !item.showDetails
        } else {
            i.showDetails = false
        }
    })
    getEvalRulePersonDetails(item.rulePersonId)

    http.post('/cloud/evalDayRulePerson/getRulePersonScoreList', {
        activityId: item.activityId, // 活动id
        toPersonId: item.toPersonId, // 参与人Id
        rulePersonId: item.rulePersonId, // 参与规则Id
        queryThisFrom: false, // 是否只查询自己评价的数据
        isApprove: true,
    }).then(res => {
        // 转数据结构
        state.dataSource = transformTableData(res.data || [])
    })
}

// 审核的确认按钮
const handleConfirm = () => {
    if (isNonEmptyArray(selectedKeysArr.value)) {
        const scoringList = state.dataSource.map(item => ({
            indicatorId: item.indicatorId,
            rulePersonId: item.rulePersonId,
            approveScore: item.approveScore,
            remark: item.remark || '', // 备注
        }))

        if (isNonEmptyArray(scoringList)) {
            http.post('/cloud/evalDayRulePerson/approvePersonScore', {
                scoringList: scoringList,
                comment: state.comment,
            }).then(res => {
                YMessage.success('本次评价审核成功,请继续完成其他评价审核')
            })
        } else {
            YMessage.warning('请填写审核后得分')
        }
    } else {
        YMessage.warning('请选择被审核人员')
    }
}

const openPreviewModal = record => {
    console.log(record, 'record')
    const scoreRecordList = record._originalScore.scoreRecordList || []

    previewModalRef.value.showModel(scoreRecordList)
}

// 点击页码
const onPaginationChange = (page, pageSize) => {
    state.query.pageNo = page
    state.query.pageSize = pageSize
    getPageDayPersonScore()
}

defineExpose({
    state,
    showModel,
})
</script>

<style lang="less" scoped>
.drawer_box {
    .evaluationDetailedPage {
        .treeListBox {
            position: fixed;
            height: calc(100% - 250px);
            flex: 0 0 200px;
            width: 200px;
            overflow: hidden;
            // border-right: 1px solid #d9d9d9;
            padding-top: 16px;
            padding-right: 16px;
            // height: 100%;
            .tree_box {
                // height: calc(100% - 48px);
                // overflow-y: hidden;
            }
            .status_box {
                border: 1px solid;
                border-radius: 4px;
                padding: 1px 3px;
            }
        }

        .empty-data {
            position: fixed;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 200px;
            color: rgba(0, 0, 0, 0.25);
            height: 70%;
        }

        .appraiseTable {
            margin-left: 200px;
            flex: auto;
            padding: 16px 0px 0px 16px;
            overflow: hidden;
        }
    }
}

.infoBox {
    padding: 24px;
    background: #f0f2f5;
    border-radius: 10px;
}

.list_item {
    padding-top: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #d9d9d9;
    .item_titleBox {
        display: flex;
        align-items: center;
        .item_line {
            width: 2px;
            height: 16px;
            background: #00b781;
            margin-right: 10px;
        }
        .item_title {
            font-weight: 400;
            font-size: 16px;
            color: #333333;
        }
    }
}

.spread {
    cursor: pointer;
    color: #00b781;
}

.totalBox {
    font-weight: 500;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    padding-top: 24px;
    padding-bottom: 24px;
    .redBox {
        font-weight: 400;
        font-size: 14px;
        color: #f5222d;
    }
    .fenBox {
        font-weight: 400;
        font-size: 32px;
        color: #333333;
    }
}

.onlyShowOne {
    margin-top: 16px;
    background: #eaf9f5;
    padding: 16px;
}
.paths {
}

.moreBtn {
    cursor: pointer;
    font-weight: 400;
    font-size: 14px;
    color: #00b781;
}

.thumbnail-item {
    position: relative;
    width: 100px;
    height: 100px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s;

    img,
    video {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .thumbnail-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 20px;
        opacity: 1;
        transition: opacity 0.3s;
    }

    &.video-thumbnail {
        .thumbnail-overlay {
            opacity: 0.8;
        }
    }
}

.commentBox {
    width: 100%;
    display: flex;
    .commentBox_left {
        font-weight: 500;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        flex-shrink: 0;
    }
    .commentBox_right {
        flex: 1;
    }
}

.footer_box {
    text-align: right;
    padding-top: 24px;
}

:deep(.ant-tree .ant-tree-treenode) {
    padding: 0 0 12px 0;
}
</style>
