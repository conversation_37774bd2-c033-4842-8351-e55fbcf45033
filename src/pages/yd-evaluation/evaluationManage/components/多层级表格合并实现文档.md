# 多层级表格合并实现文档

## 📋 概述

本文档详细说明了如何实现三层数据结构的表格合并功能：**一级指标 → 二级指标 → 多个评分标准**。这种复杂的层级结构在教育评价、绩效考核等场景中非常常见。

## 🏗️ 数据结构设计

### 原始数据结构

```javascript
// 三层嵌套数据结构
[
  {
    id: '一级指标ID',
    name: '一级指标名称',
    secondIndicators: [
      {
        id: '二级指标ID',
        name: '二级指标名称',
        indicatorScore: [  // 🔥 关键变化：从对象变为数组
          {
            id: '评分标准ID',
            content: '评分标准描述',
            minScore: 0,
            maxScore: 100,
            scoreCardName: '积分卡名称',
            evalScoreTypeList: ['text', 'image', 'video'],
            othersIndicatorScore: 85,
            thisIndicatorScore: null,
            totalIndicatorScore: null,
            comment: ''
          },
          // ... 更多评分标准
        ]
      }
    ]
  }
]
```

### 转换后的扁平化结构

```javascript
// 扁平化表格数据
[
  {
    firstIndicatorId: '一级指标ID',
    firstIndicatorName: '一级指标名称',
    secondIndicatorId: '二级指标ID', 
    secondIndicatorName: '二级指标名称',
    scoreStandardId: '评分标准ID',
    content: '评分标准描述',
    minScore: 0,
    maxScore: 100,
    // ... 其他字段
  }
]
```

## 🔧 核心实现原理

### 1. 多层级数据转换

```javascript
const transformMultipleTableData = (originalData) => {
  const flatData = []
  
  originalData.forEach((firstLevel) => {
    // 遍历一级指标下的二级指标
    if (firstLevel.secondIndicators && firstLevel.secondIndicators.length > 0) {
      firstLevel.secondIndicators.forEach((secondLevel) => {
        // 🔥 关键：遍历二级指标下的评分标准数组
        if (secondLevel.indicatorScore && Array.isArray(secondLevel.indicatorScore)) {
          secondLevel.indicatorScore.forEach((scoreStandard) => {
            flatData.push({
              // 一级指标信息
              firstIndicatorId: firstLevel.id,
              firstIndicatorName: firstLevel.name,
              
              // 二级指标信息
              secondIndicatorId: secondLevel.id,
              secondIndicatorName: secondLevel.name,
              
              // 评分标准信息
              scoreStandardId: scoreStandard.id,
              content: scoreStandard.content || '',
              minScore: scoreStandard.minScore || 0,
              maxScore: scoreStandard.maxScore || 100,
              // ... 其他字段映射
            })
          })
        }
      })
    }
  })
  
  return flatData
}
```

### 2. 双重合并逻辑

#### 一级指标合并

```javascript
{
  title: '一级指标',
  dataIndex: 'firstIndicatorName',
  customCell: (record, rowIndex) => {
    let rowSpan = 1
    
    // 向下查找相同的一级指标
    for (let i = rowIndex + 1; i < dataSource.length; i++) {
      if (dataSource[i].firstIndicatorId === record.firstIndicatorId) {
        rowSpan++
      } else {
        break
      }
    }
    
    // 如果不是该一级指标的第一行，则隐藏
    if (rowIndex > 0 && 
        dataSource[rowIndex - 1].firstIndicatorId === record.firstIndicatorId) {
      return { rowSpan: 0 }
    }
    
    return { rowSpan }
  }
}
```

#### 二级指标合并

```javascript
{
  title: '二级指标',
  dataIndex: 'secondIndicatorName',
  customCell: (record, rowIndex) => {
    let rowSpan = 1
    
    // 向下查找相同的二级指标
    for (let i = rowIndex + 1; i < dataSource.length; i++) {
      if (dataSource[i].secondIndicatorId === record.secondIndicatorId) {
        rowSpan++
      } else {
        break
      }
    }
    
    // 如果不是该二级指标的第一行，则隐藏
    if (rowIndex > 0 && 
        dataSource[rowIndex - 1].secondIndicatorId === record.secondIndicatorId) {
      return { rowSpan: 0 }
    }
    
    return { rowSpan }
  }
}
```

## 📊 数据流程图

```
原始三层嵌套数据
    ↓
遍历一级指标
    ↓
遍历二级指标
    ↓
遍历评分标准数组 (🔥 新增层级)
    ↓
生成扁平化数据
    ↓
应用双重合并逻辑
    ↓
渲染多层级合并表格
```

## 🎯 关键技术点

### 1. 数组处理

```javascript
// 检查 indicatorScore 是否为数组
if (secondLevel.indicatorScore && Array.isArray(secondLevel.indicatorScore)) {
  // 遍历数组中的每个评分标准
  secondLevel.indicatorScore.forEach((scoreStandard) => {
    // 处理每个评分标准
  })
}
```

### 2. 层级关系维护

```javascript
// 在扁平化数据中保持层级关系
{
  firstIndicatorId: firstLevel.id,      // 一级指标ID
  secondIndicatorId: secondLevel.id,    // 二级指标ID  
  scoreStandardId: scoreStandard.id,    // 评分标准ID
  
  // 原始数据引用，便于数据回写
  _originalFirst: firstLevel,
  _originalSecond: secondLevel,
  _originalScore: scoreStandard
}
```

### 3. 合并算法优化

```javascript
// 优化的合并算法，支持多层级
const calculateRowSpan = (dataSource, currentIndex, compareField) => {
  let rowSpan = 1
  const currentValue = dataSource[currentIndex][compareField]
  
  // 向下查找相同值
  for (let i = currentIndex + 1; i < dataSource.length; i++) {
    if (dataSource[i][compareField] === currentValue) {
      rowSpan++
    } else {
      break
    }
  }
  
  // 检查是否为该组的第一行
  const isFirstRow = currentIndex === 0 || 
    dataSource[currentIndex - 1][compareField] !== currentValue
  
  return isFirstRow ? rowSpan : 0
}
```

## 🔍 使用示例

### 基础使用

```vue
<template>
  <TableMergeMultipleDemo />
</template>

<script setup>
import TableMergeMultipleDemo from './TableMergeMultipleDemo.vue'
</script>
```

### 完整功能

```vue
<template>
  <TableMergeMultiplePage />
</template>

<script setup>
import TableMergeMultiplePage from './TableMergeMultiplePage.vue'
</script>
```

### 自定义配置

```javascript
// 自定义数据转换
const customTransform = (data) => {
  return transformMultipleTableData(data).map(item => ({
    ...item,
    // 添加自定义字段
    customField: calculateCustomValue(item)
  }))
}

// 自定义合并逻辑
const customMergeColumns = [
  {
    title: '一级指标',
    dataIndex: 'firstIndicatorName',
    customCell: (record, rowIndex) => {
      // 自定义合并逻辑
      return calculateCustomRowSpan(record, rowIndex, 'firstIndicatorId')
    }
  }
]
```

## 📈 性能优化

### 1. 数据处理优化

```javascript
// 使用 Map 优化查找性能
const createIndexMap = (dataSource) => {
  const map = new Map()
  dataSource.forEach((item, index) => {
    const key = `${item.firstIndicatorId}-${item.secondIndicatorId}`
    if (!map.has(key)) {
      map.set(key, [])
    }
    map.get(key).push(index)
  })
  return map
}

// 使用索引映射快速计算合并
const fastCalculateRowSpan = (record, rowIndex, indexMap) => {
  const key = `${record.firstIndicatorId}-${record.secondIndicatorId}`
  const indices = indexMap.get(key) || []
  
  if (indices[0] === rowIndex) {
    return indices.length  // 第一行，返回合并行数
  } else {
    return 0  // 非第一行，隐藏
  }
}
```

### 2. 渲染优化

```javascript
// 使用 computed 缓存计算结果
const mergedColumns = computed(() => {
  return baseColumns.map(col => {
    if (col.needMerge) {
      return {
        ...col,
        customCell: memoize((record, rowIndex) => {
          return calculateRowSpan(tableData.value, rowIndex, col.mergeField)
        })
      }
    }
    return col
  })
})
```

### 3. 内存优化

```javascript
// 避免深度拷贝，使用引用
const optimizedTransform = (originalData) => {
  const flatData = []
  
  for (const firstLevel of originalData) {
    for (const secondLevel of firstLevel.secondIndicators || []) {
      for (const scoreStandard of secondLevel.indicatorScore || []) {
        // 直接引用原始对象，避免拷贝
        flatData.push({
          ...scoreStandard,
          firstIndicatorId: firstLevel.id,
          firstIndicatorName: firstLevel.name,
          secondIndicatorId: secondLevel.id,
          secondIndicatorName: secondLevel.name
        })
      }
    }
  }
  
  return flatData
}
```

## 🚀 扩展功能

### 1. 动态层级

```javascript
// 支持动态层级数量
const dynamicTransform = (data, levelConfig) => {
  const traverse = (node, level, path) => {
    if (level >= levelConfig.length) {
      return [{ ...node, path }]
    }
    
    const children = node[levelConfig[level].childrenField] || []
    return children.flatMap(child => 
      traverse(child, level + 1, [...path, node])
    )
  }
  
  return data.flatMap(item => traverse(item, 0, []))
}
```

### 2. 条件合并

```javascript
// 根据条件决定是否合并
const conditionalMerge = (record, rowIndex, condition) => {
  if (!condition(record)) {
    return { rowSpan: 1 }  // 不合并
  }
  
  return calculateRowSpan(record, rowIndex)  // 正常合并
}
```

### 3. 自定义合并样式

```css
/* 合并单元格的特殊样式 */
.merged-cell {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-left: 3px solid #1890ff;
  font-weight: 600;
}

.merged-cell-secondary {
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
  border-left: 3px solid #52c41a;
  font-weight: 500;
}
```

## ⚠️ 注意事项

### 1. 数据一致性

- 确保每个层级的ID字段唯一且一致
- 评分标准数组不能为空
- 数据类型必须正确（数组 vs 对象）

### 2. 性能考虑

- 大数据量时考虑虚拟滚动
- 避免在合并函数中进行复杂计算
- 使用适当的缓存策略

### 3. 用户体验

- 提供清晰的层级视觉指示
- 支持展开/折叠功能
- 添加加载状态和错误处理

## 🔧 故障排除

### 常见问题

1. **合并不生效**
   - 检查数据中的ID字段是否正确
   - 确认 `indicatorScore` 是数组格式
   - 验证 `customCell` 函数的返回值

2. **性能问题**
   - 减少合并计算的复杂度
   - 使用 `computed` 缓存结果
   - 考虑分页或虚拟滚动

3. **样式异常**
   - 检查CSS的 `:deep()` 选择器
   - 确认表格的 `bordered` 属性
   - 验证单元格的 `vertical-align` 设置

## 📚 相关资源

- [基础表格合并文档](./表格合并实现文档.md)
- [Ant Design Vue Table API](https://antdv.com/components/table-cn)
- [Vue 3 Composition API](https://v3.cn.vuejs.org/guide/composition-api-introduction.html)

## 🎯 总结

多层级表格合并是一个复杂但实用的功能，关键在于：

1. **正确的数据转换**：将三层嵌套结构转换为扁平化数据
2. **双重合并逻辑**：同时处理一级和二级指标的合并
3. **性能优化**：使用适当的算法和缓存策略
4. **用户体验**：提供清晰的视觉层级和交互反馈

通过本文档的指导，您可以实现功能完整、性能良好的多层级表格合并功能。
