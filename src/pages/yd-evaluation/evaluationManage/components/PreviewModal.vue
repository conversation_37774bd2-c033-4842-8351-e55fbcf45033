<template>
    <a-modal
        v-model:open="state.visible"
        width="800px"
        title="更多评价/图片/视频"
        :footer="null"
        :bodyStyle="{ padding: '16px', overflow: 'auto', height: '600px' }"
        @cancel="handleClose"
    >
        <a-table :columns="columns" :dataSource="state.dataSource" :pagination="false">
            <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'Paths'">
                    <div class="paths" v-if="record.imgPaths || record.videoPaths">
                        <div v-if="getFirstMediaFile(record)" class="thumbnail-item">
                            <!-- 图片缩略图 -->
                            <img v-if="getFirstMediaFile(record).type === 'image'" :src="getFirstMediaFile(record).url" alt="缩略图" />
                            <!-- 视频缩略图 -->
                            <video
                                v-else-if="getFirstMediaFile(record).type === 'video'"
                                :src="getFirstMediaFile(record).url"
                                muted
                            ></video>
                            <div class="thumbnail-overlay">
                                <PlayCircleOutlined v-if="getFirstMediaFile(record).type === 'video'" />
                            </div>
                        </div>
                    </div>
                    <div class="moreBtn" @click="openPreviewModal(record)" v-if="record.imgPaths || record.videoPaths">
                        更多
                        <RightOutlined />
                    </div>
                </template>
            </template>
        </a-table>
    </a-modal>
    <MediaPreviewModal v-model:open="previewModal.open" :img-paths="previewModal.imgPaths" :video-paths="previewModal.videoPaths" />
</template>

<script setup>
// 引入组件
import MediaPreviewModal from './MediaPreviewModal.vue'

// 预览Modal状态
const previewModal = reactive({
    open: false,
    imgPaths: '', // 图片路径字符串
    videoPaths: '', // 视频路径字符串
})

const state = reactive({
    visible: false,
    dataSource: [],
})

// 打开预览Modal
const openPreviewModal = record => {
    previewModal.imgPaths = record.imgPaths || ''
    previewModal.videoPaths = record.videoPaths || ''
    previewModal.open = true
}

// 获取图片列表
const getImageList = imgPaths => {
    if (!imgPaths) return []
    return imgPaths.split(',').filter(path => path.trim())
}

// 获取视频列表
const getVideoList = videoPaths => {
    if (!videoPaths) return []
    return videoPaths.split(',').filter(path => path.trim())
}

// 获取第一个媒体文件
const getFirstMediaFile = record => {
    // 优先显示图片
    if (record.imgPaths) {
        const imageList = getImageList(record.imgPaths)
        if (imageList.length > 0) {
            return { type: 'image', url: imageList[0] }
        }
    }

    // 然后显示视频
    if (record.videoPaths) {
        const videoList = getVideoList(record.videoPaths)
        if (videoList.length > 0) {
            return { type: 'video', url: videoList[0] }
        }
    }

    return null
}

const columns = [
    {
        title: '评价者',
        dataIndex: 'createBy',
        key: 'createBy',
    },
    {
        title: '评价时间',
        dataIndex: 'scoreTime',
        key: 'scoreTime',
    },
    {
        title: '图片/视频',
        dataIndex: 'Paths',
        key: 'Paths',
    },
    {
        title: '评语',
        dataIndex: 'comment',
        key: 'comment',
    },
]

const showModel = scoreRecordList => {
    state.dataSource = scoreRecordList
    state.visible = true
}

defineExpose({
    showModel,
})
</script>

<style scoped lang="less">
.thumbnail-item {
    position: relative;
    width: 100px;
    height: 100px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s;

    img,
    video {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .thumbnail-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0; 
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 20px;
        opacity: 1;
        transition: opacity 0.3s;
    }

    &.video-thumbnail {
        .thumbnail-overlay {
            opacity: 0.8;
        }
    }
}

.moreBtn {
    cursor: pointer;
    font-weight: 400;
    font-size: 14px;
    color: #00b781;
}
</style>
