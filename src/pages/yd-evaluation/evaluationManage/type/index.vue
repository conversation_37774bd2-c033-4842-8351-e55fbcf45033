<template>
    <cont-header />
    <div p-20>
        <div pb-20>
            <a-form :model="query" autocomplete="off">
                <a-row :gutter="[25, 16]">
                    <YCol>
                        <a-form-item label="类型名称：">
                            <a-input v-model:value.trim="query.name" placeholder="请输入" />
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-form-item label="类型状态：">
                            <a-select v-model:value="query.enable" placeholder="请选择">
                                <a-select-option :value="1">启用</a-select-option>
                                <a-select-option :value="0">禁用</a-select-option>
                            </a-select>
                        </a-form-item>
                    </YCol>
                    <YCol :span="5">
                        <a-form-item label="创建时间：">
                            <RangePicker v-model:startTime="query.startDate" v-model:endTime="query.endDate"></RangePicker>
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-button type="primary" @click="getList">
                            <SearchOutlined />
                            查 询
                        </a-button>
                        <a-button @click="reset">
                            <reload-outlined />
                            重 置
                        </a-button>
                    </YCol>
                </a-row>
            </a-form>
        </div>
        <div flex grid-justify-end mb-24 mt-20>
            <a-button type="primary" @click="AddedType">
                <template #icon>
                    <PlusOutlined />
                </template>
                新增
            </a-button>
            <a-button danger :disabled="!selected.length" @click="deleteData">删除</a-button>
        </div>
        <div min-h-520 mb-20>
            <ETable
                hash="evaluationTypeTable"
                :loading="page.loading"
                :dataSource="page.list"
                :total="page.total"
                @paginationChange="paginationChange"
                :current="query.pageNo"
                :row-selection="{
                    selectedRowKeys: selected,
                    onChange: onSelectChange,
                }"
            >
                <a-table-column title="类型名称" data-index="name" :width="100" />
                <a-table-column title="类型状态" data-index="enable" :width="150">
                    <template #default="{ record }">
                        <a-badge :color="getStatusObj(record.enable).color" :text="getStatusObj(record.enable).text" />
                    </template>
                </a-table-column>
                <a-table-column title="评价活动总数" data-index="activityCount" :width="200"></a-table-column>
                <a-table-column title="创建人" data-index="createBy" :width="100"></a-table-column>
                <a-table-column title="创建时间" data-index="createTime" :width="120"></a-table-column>
                <a-table-column title="操作" data-index="operate" :width="100">
                    <template #default="{ record }">
                        <a-button type="link" class="btn-link-color" @click="particularsType(record)">详情</a-button>
                        <a-button type="link" class="btn-link-color" @click="compileType(record)">编辑</a-button>
                    </template>
                </a-table-column>
            </ETable>
        </div>
    </div>
    <!-- 这里是新增评价类型的抽屉 -->
    <a-drawer
        v-model:open="state.addedTypeOpen"
        :width="500"
        :destroyOnClose="true"
        :keyboard="false"
        :maskClosable="false"
        :title="titleObj[state.typeValue]"
        placement="right"
        @close="closeThing"
    >
        <div>
            <a-form
                :model="addedFormState"
                ref="addedFormStateRef"
                name="addedFormState"
                autocomplete="off"
                layout="vertical"
                :disabled="state.typeValue === 'details'"
            >
                <a-form-item
                    p-b-16
                    :label-col="{ span: 24 }"
                    :wrapper-col="{ span: 24 }"
                    :rules="[{ required: true, message: '请上传应用图标!', trigger: 'change' }]"
                    name="logo"
                    label="应用图标："
                >
                    <div flex flex-items-center>
                        <div w-100 h-100>
                            <uploadImage v-model:url="addedFormState.logo" :limit="2" />
                        </div>
                    </div>
                </a-form-item>
                <div class="iconRoom" @click="selectIconStoreRoom">图标库</div>
                <a-form-item
                    p-b-16
                    :label-col="{ span: 24 }"
                    :wrapper-col="{ span: 24 }"
                    name="name"
                    :rules="[{ required: true, message: '请输入评价类型名称!' }]"
                    label="评价类型："
                    mb-6
                >
                    <a-input v-model:value.trim="addedFormState.name" show-count :maxlength="10" placeholder="请输入" />
                </a-form-item>
                <a-form-item
                    p-b-16
                    :label-col="{ span: 24 }"
                    :wrapper-col="{ span: 24 }"
                    name="appTypeId"
                    :rules="[{ required: true, message: '请选择应用分类!' }]"
                    label="应用分类："
                    mb-6
                >
                    <a-select
                        placeholder="请选择"
                        v-model:value="addedFormState.appTypeId"
                        :field-names="{ label: 'name', value: 'id' }"
                        :options="state.typeOptions"
                    ></a-select>
                </a-form-item>

                <a-form-item :rules="[{ required: true, message: '请选择类型状态!' }]" name="enable" label="类型状态：" mb-6>
                    <a-radio-group v-model:value="addedFormState.enable" name="radioGroup">
                        <a-radio :value="true">启用</a-radio>
                        <a-radio :value="false">禁用</a-radio>
                    </a-radio-group>
                </a-form-item>
            </a-form>
        </div>
        <template #footer>
            <div class="footer">
                <template v-if="state.typeValue === 'details'">
                    <a-button @click="closeThing">确定</a-button>
                </template>
                <template v-else>
                    <a-button @click="closeThing">取消</a-button>
                    <a-button type="primary" @click="submitThing">确认</a-button>
                </template>
            </div>
        </template>
    </a-drawer>
    <!-- 整一个勋章图标库的弹窗modal -->
    <a-modal
        v-model:open="state.iconLibraryOpen"
        width="500px"
        title="应用图标库"
        :bodyStyle="{ padding: '16px', overflow: 'auto', maxHeight: '550px' }"
        @ok="handleConfirm"
    >
        <div class="iconLibraryBox">
            <a-row :gutter="[25, 16]">
                <a-col :span="8" v-for="(item, index) in state.medalList" :key="item.id">
                    <div class="medalbox" flex justify-center @click="selectiveMedal(item)">
                        <div class="medalbox_radio">
                            <img v-if="checkImg === item.id" src="@/assets/images/admin/medal_yes.png" />
                            <img v-else src="@/assets/images/admin/medal_no.png" />
                        </div>
                        <div class="medalbox_img">
                            <img :src="item.img" alt="" />
                            <span class="text" :title="item.name">{{ item.name }}</span>
                        </div>
                    </div>
                </a-col>
            </a-row>
        </div>
    </a-modal>
</template>
<script setup name="medalRecords">
import uploadImage from './uploadImage.vue'

// title
const titleObj = {
    add: '新增评价类型',
    edit: '编辑评价类型',
    details: '评价类型详情',
}

// 表单实例
const addedFormStateRef = ref()

// 表单对象
const addedFormState = ref({})

const selected = ref([])

// 响应式数据
const state = ref({
    iconLibraryOpen: false,
    logo:'',
    typeValue: 'add',
    formState: {
        libraryId: null,
        typeId: null,
    },
    addedTypeOpen: false,
    typeOptions: [],
    medalList: [
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/wuyu.png',
            name: '五育评价',
            id: 0,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/deyu.png',
            name: '德育评价',
            id: 1,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/zhiyu.png',
            name: '智育评价',
            id: 2,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/tiyu.png',
            name: '体育评价',
            id: 3,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/meiyu.png',
            name: '美育评价',
            id: 4,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/laodong.png',
            name: '劳动评价',
            id: 5,
        },
        {
            img: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/qita.png',
            name: '其他评价',
            id: 6,
        },
    ],
})

const statusObj = {
    true: {
        text: '启用',
        color: '#00B781',
    },
    false: {
        text: '禁用',
        color: '#F5222D',
    },
    default: {
        text: '-',
        color: '',
    }, // 默认值存储在 default 属性中
}

function getStatusObj(key) {
    return statusObj[key] || statusObj.default
}

// table 接口
let { query, page, getList, reset, paginationChange } = useList('/cloud/evalType/page')
getList()

// const orderType = {
//     ascend: 'asc',
//     descend: 'desc',
// }

// table排序切换
// const handleTableChange = (pag, filters, sorter) => {
//     query.order = orderType[sorter.order]
//     query.field = sorter.field
//     getList()
// }

// 打开新增评价类型的抽屉事件
const AddedType = () => {
    state.value.typeValue = 'add'
    state.value.addedTypeOpen = true
}

// 确定新增评价类型
const submitThing = () => {
    addedFormStateRef.value
        .validate()
        .then(() => {
            let typeApi = ''
            state.value.typeValue === 'add' ? (typeApi = '/cloud/evalType/create') : (typeApi = '/cloud/evalType/update')
            http.post(typeApi, addedFormState.value).then(res => {
                YMessage.success(res.message)
                closeThing()
                getList()
            })
        })
        .catch(error => {
            console.log('error: ', error)
        })
}

// 取消抽屉
const closeThing = () => {
    // 清除表单校验
    addedFormStateRef.value.resetFields()
    state.value.addedTypeOpen = false
    state.value.typeValue = 'add'
    addedFormState.value = {
        enable: false,
    }
}

// 批量选择
const onSelectChange = selectedRowKeys => {
    selected.value = selectedRowKeys
}

// 删除
const deleteData = () => {
    if (selected.value.length) {
        // console.log('selected.value', selected.value)
        // 删除数据
        http.post('/cloud/evalType/delete', {
            ids: selected.value,
        }).then(res => {
            YMessage.success(res.message)
            getList()
        })
        selected.value = []
    } else {
        YMessage.warning('未选中数据!')
    }
}

// 获取应用分类接口
const getTypeList = () => {
    http.post('/cloud/app/category/page', { pageNo: 1, pageSize: 100 }).then(({ data }) => {
        state.value.typeOptions = data.list
    })
}
// 详情
const particularsType = item => {
    state.value.addedTypeOpen = true
    state.value.typeValue = 'details'
    Object.assign(addedFormState.value, item)
}

// 编辑
const compileType = item => {
    state.value.addedTypeOpen = true
    state.value.typeValue = 'edit'
    Object.assign(addedFormState.value, item)
}

// 也可以选择图标库的url
const selectIconStoreRoom = () => {
    state.value.iconLibraryOpen = true
}

const checkImg = ref('')
const selectiveMedal = data => {
    checkImg.value = data.id
    state.value.logo = data.img
}

const handleConfirm = () => {
    addedFormState.value.logo = state.value.logo 
    state.value.iconLibraryOpen = false
}


watch(
    () => addedFormState.value.logo,
    val => {
        addedFormStateRef.value.validate('logo')
    },
)

onMounted(() => {
    getTypeList()
})
</script>
<style lang="less" scoped>
.iconRoom {
    font-weight: 400;
    font-size: 14px;
    color: #00b781;
    cursor: pointer;
    padding-bottom: 12px;
}
.medalbox {
    cursor: pointer;
    position: relative;
    .medalbox_img {
        width: 110px;
        // height: 110px;
        background: #ffffff;
        // border: 1px solid #f0f2f5;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        // border-radius: 50%;
        margin-bottom: 20px;
        img {
            width: 110px;
            height: 110px;
        }
        .text {
            width: 110px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-align: center;
        }
    }
    .medalbox_radio {
        right: 10px;
        width: 18px;
        height: 18px;
        position: absolute;
        img {
            width: 18px;
            height: 18px;
        }
    }
}
</style>
