<template>
    <cont-header />
    <div p-20>
        <div pb-20>
            <a-form :model="query" autocomplete="off">
                <a-row :gutter="[25, 16]">
                    <YCol>
                        <a-form-item label="规则名称：">
                            <a-input v-model:value.trim="query.name" placeholder="请输入" />
                        </a-form-item>
                    </YCol>

                    <YCol :span="5">
                        <a-form-item label="使用次数范围：">
                            <a-input
                                style="width: 100px"
                                @keyup="query.minCount = query.minCount.replace(/\D/g, '')"
                                v-model:value="query.minCount"
                                placeholder="请输入"
                            />
                            -
                            <a-input
                                style="width: 100px"
                                @keyup="query.maxCount = query.maxCount.replace(/\D/g, '')"
                                v-model:value="query.maxCount"
                                placeholder="请输入"
                            />
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-form-item label="规则状态：">
                            <a-select v-model:value="query.enable" placeholder="请选择">
                                <a-select-option :value="1">启用</a-select-option>
                                <a-select-option :value="0">禁用</a-select-option>
                            </a-select>
                        </a-form-item>
                    </YCol>
                    <YCol :span="5">
                        <a-form-item label="更新时间：">
                            <RangePicker v-model:startTime="query.updateStartTime" v-model:endTime="query.updateEndTime"></RangePicker>
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-button type="primary" @click="getList">
                            <SearchOutlined />
                            查 询
                        </a-button>
                        <a-button @click="reset">
                            <reload-outlined />
                            重 置
                        </a-button>
                    </YCol>
                </a-row>
            </a-form>
        </div>
        <div flex grid-justify-end mb-24 mt-20>
            <a-button type="primary" @click="handleEdit('add')">
                <template #icon>
                    <PlusOutlined />
                </template>
                新增规则库
            </a-button>
            <a-button danger :disabled="!selected.length" @click="deleteData">删除</a-button>
        </div>
        <div min-h-520 mb-20>
            <ETable
                hash="evaluationregulationTable"
                :loading="page.loading"
                :dataSource="page.list"
                :total="page.total"
                @paginationChange="paginationChange"
                :current="query.pageNo"
                @change="handleTableChange"
                :row-selection="{
                    selectedRowKeys: selected,
                    onChange: onSelectChange,
                }"
            >
                <a-table-column title="规则名称" data-index="name" :width="100" />
                <a-table-column title="使用次数" data-index="counts" :width="100" />
                <a-table-column title="规则状态" data-index="enable" :width="150">
                    <template #default="{ record }">
                        <a-badge :color="getStatusObj(record.enable).color" :text="getStatusObj(record.enable).text" />
                    </template>
                </a-table-column>
                <a-table-column title="创建人" data-index="createBy" :width="100"></a-table-column>
                <a-table-column title="更新时间" data-index="updateTime" :width="120" :sorter="true"></a-table-column>
                <a-table-column title="操作" data-index="operate" :width="100">
                    <template #default="{ record }">
                        <a-button type="link" class="btn-link-color" @click="handleEdit('info', record)">详情</a-button>
                        <a-button type="link" class="btn-link-color" @click="handleEdit('edit', record)">编辑</a-button>
                        <a-button danger type="link" class="btn-link-color" @click="disabledEnable(record)" v-if="record.enable">
                            禁用
                        </a-button>
                        <a-button danger type="link" class="btn-link-color" @click="putEnable(record)" v-if="!record.enable">启用</a-button>
                    </template>
                </a-table-column>
            </ETable>
        </div>
    </div>
    <!-- 编辑和新建评价规则 -->
    <NewRule :ruleId="state.ruleId" ref="newRuleRef" @close="close" />
</template>
<script setup name="medalRecords">
import NewRule from './newRule.vue'

let { query, page, getList, reset, paginationChange } = useList('/cloud/evalPublicRule/page')

getList()
const newRuleRef = ref()

const state = ref({})
const selected = ref([])
// 新增规则
const handleEdit = (isEditType, item) => {
    switch (isEditType) {
        case "edit":
        newRuleRef.value.showModel(isEditType, item)
            break;
        case "add":
        newRuleRef.value.showModel(isEditType)
            break;
        case "info":
        newRuleRef.value.showModel(isEditType, item)
            break;
        default:
            break;
    }
}


// 关闭抽屉
const close = () => {
    reset()
}

const statusObj = {
    1: {
        text: '启用',
        color: '#00B781',
    },
    0: {
        text: '禁用',
        color: '#F5222D',
    },
    default: {
        text: '-',
        color: '',
    }, // 默认值存储在 default 属性中
}

function getStatusObj(key) {
    return statusObj[key] || statusObj.default
}


const orderType = {
    ascend: false,
    descend: true,
}

// table排序切换
const handleTableChange = (pag, filters, sorter) => {
    query.isDesc = orderType[sorter.order]
    query.field = sorter.field
    getList()
}



// 批量选择
const onSelectChange = selectedRowKeys => {
    selected.value = selectedRowKeys
}

// 删除
const deleteData = () => {
    if (selected.value.length) {
        // 删除数据
        http.post('/cloud/evalPublicRule/delete', {
            ids: selected.value,
        }).then(res => {
            YMessage.success(res.message)
            getList()
        })
        selected.value = []
    } else {
        YMessage.warning('未选中数据!')
    }
}

// 禁用规则状态
const disabledEnable = async item => {
    const flag = await yConfirm('禁用', '确认要禁用吗？')
    if (flag) {
        http.post('/cloud/evalPublicRule/updateStatus', { id: item.id, enable: 0 }).then(res => {
            getList()
        })
    }
}
// 启用规则状态
const putEnable = async item => {
    const flag = await yConfirm('启用', '确认要启用吗？')
    if (flag) {
        http.post('/cloud/evalPublicRule/updateStatus', { id: item.id, enable: 1 }).then(res => {
            getList()
        })
    }
}
</script>
<style lang="less" scoped></style>
