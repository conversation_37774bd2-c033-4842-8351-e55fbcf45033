<template>
    <YDrawer v-model:open="state.open" :title="state.isEdit ? '编辑评比活动' : '新建评比活动'">
        <template #title>
            <div flex-1 flex justify-center>
                <a-tabs class="tab_box" v-model:activeKey="state.activeKey">
                    <a-tab-pane disabled :key="item.id" v-for="item in tabList">
                        <template #tab>
                            <span>
                                <span class="step" :style="{ borderColor: state.activeKey === item.id ? '#00b781' : '' }">
                                    {{ item.id }}
                                </span>
                                {{ item.label }}
                            </span>
                        </template>
                    </a-tab-pane>
                </a-tabs>
            </div>
        </template>
        <div class="container_box">
            <a-form :model="state.formModel" ref="formRef">
                <StepOne :data="state.formModel" v-if="state.activeKey === 1" />
                <StepTwo :data="state.formModel" :jifenOptions="state.scoreCardList" v-if="state.activeKey === 2" />
                <StepThree :data="state.formModel" v-if="state.activeKey === 3" />
            </a-form>
        </div>
        <template #footer>
            <div>
                <a-button mr-5 @click="close" v-if="stepClose">取消</a-button>
                <a-button mr-5 v-if="stepBack" @click="handleBlack">上一步</a-button>
                <a-button type="primary" v-if="stepNext" @click="handleNext">下一步</a-button>
                <a-button mr-5 v-if="step3" @click="handleNext">保存</a-button>
                <a-button type="primary" v-if="step3" @click="handleSubmit">发布</a-button>
            </div>
        </template>
    </YDrawer>
</template>
<script setup>
import StepOne from './stepOne.vue'
import StepTwo from './stepTwo.vue'
import StepThree from './stepThree.vue'
import { arrDispose } from '../configData.js'
const cloudState = reactive({ ...getUrlParams() })
const emit = defineEmits(['close'])

// 评价类型id
const props = defineProps({
    evalTypeId: {
        type: [Number, String],
        default: 0,
    },
})

const state = ref({
    scoreCardList: [],
    open: false,
    isEdit: false,
    activeKey: 1,
    formModel: {
        approvesStr: '',
        promotionalImage: '',
        title: '',
        description: '',
        result: '',
        curator: '',
        cycle: 0,
        dateSetting: 0,
        schoolSemesterId: null,
        customTime: [],
        approves: [],
        name: '',
        ruleType: 0,
        personType: 0,
        fileList: [],
        imgSrc: '',
        rulesData: [{ name: '', ruleName: '', ruleType: 0, personType: 0, evaluator: '', dataSource: [] }], // 前端收集的数据
        rules: [], // 转换后的接口数据
        evalMaxNum: null, // 评价次数
        isApprove: 1, // 评价审核
        minRatingStep: null, // 最小评分单位
        settlementType: null, // 积分规则
    },
})

const tabList = [
    { label: '评价信息', id: 1 },
    { label: '评价规则', id: 2 },
    { label: '评价设置', id: 3 },
]

const stepClose = computed(() => state.value.activeKey === 1 || state.value.activeKey === 3)
const stepNext = computed(() => state.value.activeKey === 1 || state.value.activeKey === 2)
const stepBack = computed(() => state.value.activeKey === 2 || state.value.activeKey === 3)
const step3 = computed(() => state.value.activeKey === 3)

const close = () => {
    state.value.open = false
    state.value.formModel = {}
    emit('close')
}

// 上一步
const handleBlack = () => {
    state.value.activeKey -= 1
}

const stepMethod = {
    1: stepOne,
    2: stepTwo,
    3: stepThree,
}

const formRef = ref(null)
// 下一步
const handleNext = () => {
    formRef.value.validate().then(async res => {
        try {
            await stepMethod[state.value.activeKey]()
            // console.log('1212')

            // 下一步
            if (state.value.activeKey === 3) {
                close()
            } else {
                state.value.activeKey++
            }
        } catch (error) {
            console.log('error :>> ', error)
        }
    })
}

// 发布
const handleSubmit = () => {
    formRef.value.validate().then(() => {
        const [startDate, endDate] = state.value.formModel.customTime
        const params = {
            id: state.value.saveActivityId,
            dateSetting: state.value.formModel.dateSetting,
            schoolSemesterId: state.value.formModel.schoolSemesterId,
            startDate: startDate,
            endDate: endDate,
            cycle: state.value.formModel.cycle,
            approves: state.value.formModel.approves,
            enable: 1, // 这个就是发布
            evalMaxNum: state.value.formModel.evalMaxNum,
            isApprove: +state.value.formModel.isApprove,
            minRatingStep: state.value.formModel.minRatingStep,
            settlementType: state.value.formModel.settlementType,
        }
        return new Promise((resolve, reject) => {
            http.post('/cloud/evalActivity/setting', params)
                .then(res => {
                    resolve(res)
                    YMessage.success('操作成功!')
                    close()
                })
                .catch(err => reject(err))
        })
    })
}

function stepOne() {
    if (state.value.isEdit || state.value.saveActivityId) {
        // 如果第一步是修改那就发编辑的接口啊
        const editParams = {
            id: state.value.saveActivityId, // 活动id
            evalTypeId: props.evalTypeId || cloudState.codeId, // 评价类型外面带进来的
            promotionalImage:
                state.value.formModel.promotionalImage || 'https://cloudcdn.yyide.com/evalActivity/eval/activity/eval_default.png',
            title: state.value.formModel.title,
            description: state.value.formModel.description,
        }
        return new Promise((resolve, reject) => {
            // resolve(true)
            http.post('/cloud/evalActivity/update', editParams)
                .then(res => {
                    resolve(res)
                })
                .catch(err => reject(err))
        })
    } else {
        // 如果第一步是新增那就发新增的接口
        const params = {
            evalTypeId: props.evalTypeId || cloudState.codeId, // 评价类型外面带进来的
            promotionalImage:
                state.value.formModel.promotionalImage || 'https://cloudcdn.yyide.com/evalActivity/eval/activity/eval_default.png',
            title: state.value.formModel.title,
            description: state.value.formModel.description,
        }
        return new Promise((resolve, reject) => {
            // resolve(true)
            http.post('/cloud/evalActivity/create', params)
                .then(res => {
                    state.value.saveActivityId = res.data // 存一个活动的id 后面只要走接口就用他
                    resolve(res)
                })
                .catch(err => reject(err))
        })
    }
}

function stepTwo() {
    return new Promise((resolve, reject) => {
        if (state.value.formModel?.status === 1) {
            resolve(true)
        } else {
            try {
                const requiredFields = ['firstIndicatorName', 'secondIndicatorName', 'evalScoreTypeList', 'showLineName', 'standardName'] // 需要校验的字段名
                state.value.formModel.rulesData.forEach(item => {
                    item.dataSource.forEach(i => {
                        requiredFields.forEach(field => {
                            if (!i.hasOwnProperty(field) || !i[field]) {
                                throw new Error('请补全评价规则')
                            }
                        })
                    })
                })

                state.value.formModel.rulesData.forEach(item => {
                    item.dataSource.forEach(i => {
                        if (Object.values(i).includes('')) {
                            throw new Error('请补全评价规则')
                        }
                    })
                })

                state.value.formModel.rules = arrDispose(state.value.formModel.rulesData)
                const params = {
                    activityId: state.value.saveActivityId,
                    rules: state.value.formModel.rules,
                }

                http.post('/cloud/evalActivity/rule/create', params).then(res => {
                    resolve(res)
                })
            } catch (error) {
                YMessage.warning('请补全评价规则')
                reject(false)
            }
        }
    })
    // state.value.formModel.rules = arrDispose(state.value.formModel.rulesData)
    // const params = {
    //     activityId: state.value.saveActivityId,
    //     rules: state.value.formModel.rules,
    // }
    // return new Promise((resolve, reject) => {
    //     http.post('/cloud/evalActivity/rule/create', params)
    //         .then(res => {
    //             resolve(res)
    //         })
    //         .catch(err => reject(err))
    // })
}

function stepThree() {
    const [startDate, endDate] = state.value.formModel.customTime
    const params = {
        id: state.value.saveActivityId,
        dateSetting: state.value.formModel.dateSetting,
        schoolSemesterId: state.value.formModel.schoolSemesterId,
        startDate: startDate,
        endDate: endDate,
        cycle: state.value.formModel.cycle,
        approves: state.value.formModel.approves,
        enable: 0,
        evalMaxNum: state.value.formModel.evalMaxNum,
        isApprove: +state.value.formModel.isApprove,
        minRatingStep: state.value.formModel.minRatingStep,
        settlementType: state.value.formModel.settlementType,
    }
    return new Promise((resolve, reject) => {
        http.post('/cloud/evalActivity/setting', params)
            .then(res => {
                resolve(res)
            })
            .catch(err => reject(err))
    })
}

// 获取系统所有的积分卡接口
const getScoreCardList = () => {
    http.post('/cloud/evalScoreCard/listEvalScoreCard', {
        evalTypeId: props.evalTypeId || cloudState.codeId,
    }).then(res => {
        state.value.scoreCardList = res.data
        console.log(state.value.scoreCardList, '积分卡数据')
    })
}

const showModel = (isEdit, item) => {
    state.value.isEdit = isEdit
    state.value.activeKey = 1
    if (state.value.isEdit) {
        // 如果进来了是编辑模式的话 就要在这里调用查详情的请求了
        // 已经带了活动id进来的啊
        state.value.saveActivityId = item.id
        http.get('/cloud/evalActivity/get', {
            id: item.id,
        }).then(res => {
            // console.log('详情的请求详情的请求详情的请求', res)
            Object.assign(state.value.formModel, res.data)
            // Object.assign(state.formModel, res.data.rule)
            // console.log('   state.formModel', state.value.formModel)

            // 自定义时间 回显
            if (res.data.startDate) {
                state.value.formModel.customTime = [res.data.startDate, res.data.endDate]
            } else {
                state.value.formModel.customTime = []
            }

            // 活动负责人回显
            state.value.formModel.approvesStr = Array.isArray(res.data.approves) ? res.data.approves.map(item => item.name).join('、') : ''

            // 将布尔值 true 和 false 分别转换为数字 1 和 0
            state.value.formModel.isApprove = +res.data.isApprove

            // 第二步的数据稍稍处理一下 做一下回显 res.data.rule
            // 回显前端数据 等会也可以写优雅点
            if (res.data.rules.length > 0) {
                state.value.formModel.rulesData = res.data.rules.flatMap(item => {
                    let data = {
                        name: item.name, // 规则名称
                        participants: item.participants, // 人的数组
                        attendPeople: item.participants.map(item => item.name).join('、'), // 弄一个字符串来显示一下啊
                        personType: item.personType,
                        ruleType: 0,
                        dataSource: [], //table数据
                    }
                    item.firstIndicators.forEach(indicator => {
                        indicator.secondIndicators.forEach(secondIndicator => {
                            data.dataSource.push({
                                firstIndicatorId: indicator.id,
                                firstIndicatorName: indicator.name,
                                secondIndicatorId: secondIndicator.id,
                                secondIndicatorName: secondIndicator.name,
                                standardName: secondIndicator.indicatorScore.content,
                                startScore: secondIndicator.indicatorScore.minScore,
                                endScore: secondIndicator.indicatorScore.maxScore,
                                scoreCardId: secondIndicator.indicatorScore.scoreCardId,
                                scoreCardName: secondIndicator.indicatorScore.scoreCardName,
                                evalScoreTypeList: secondIndicator.indicatorScore.evalScoreTypeList,
                                valuers: secondIndicator.valuers,
                                showLineName: secondIndicator.valuers.map(item => item.name).join('、'), // 转个字符串
                            })
                        })
                    })

                    return data
                })
            } else {
                state.value.formModel.rulesData = [{ name: '', ruleName: '', ruleType: 0, personType: 0, evaluator: '', dataSource: [] }]
            }
            state.value.open = true
        })
    } else {
        // 进来是新增的话 就进行数据的初始化啊
        state.value.saveActivityId = ''
        state.value.formModel.status = null

        state.value.open = true
        const initformModel = {
            promotionalImage: '',
            title: '',
            description: '',
            result: '',
            curator: '',
            cycle: 0,
            dateSetting: 0,
            schoolSemesterId: null,
            customTime: [],
            approves: [],
            approvesStr: '',
            evalMaxNum: 1,
            ruleName: '',
            ruleType: 0,
            personType: 0,
            fileList: [],
            imgSrc: '',
            rulesData: [{ name: '', ruleName: '', ruleType: 0, personType: 0, evaluator: '', dataSource: [] }], // 前端收集的数据
            rules: [], // 转换后的接口数据
            isApprove: 1, // 评价审核
            minRatingStep: null, // 最小评分单位
            settlementType: null, // 积分规则
        }

        Object.assign(state.value.formModel, initformModel)
    }
    getScoreCardList()
}

defineExpose({
    showModel,
})
provide('formRef', formRef) // 提供方法
</script>
<style scoped lang="less">
.tab_box {
    :deep(.ant-tabs-nav) {
        margin-bottom: 0px;
        .ant-tabs-tab {
            padding: 18px 0;
        }
    }
    .step {
        display: inline-block;
        width: 22px;
        text-align: center;
        border: 1px solid #bfbfbf;
        border-radius: 50%;
    }
}

.container_box {
    margin: 0 auto;
    width: 85%;
}
</style>
