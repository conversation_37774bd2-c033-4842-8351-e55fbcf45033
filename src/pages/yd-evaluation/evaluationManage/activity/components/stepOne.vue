<template>
    <a-form-item label="活动封面" :labelCol="{ span: 24 }" mb-10>
        <div v-if="data.promotionalImage" class="upload_img">
            <img :src="data.promotionalImage" />
            <CloseCircleFilled class="close_btn" @click="deleteAvatar" />
        </div>
        <div v-else w-250 h-160>
            <a-upload
                class="avatar-uploaderImg"
                :customRequest="customRequest"
                list-type="picture-card"
                :before-upload="beforeUpload"
                :showUploadList="false"
                :multiple="false"
            >
                <div>
                    <PlusOutlined />
                    <div style="margin-top: 8px">点击上传</div>
                </div>
            </a-upload>
        </div>
        <div pb-8 class="defaultImg" @click="useDefaultImg">
            <ExclamationCircleOutlined />
            使用默认活动封面
        </div>
        <div class="color-#bfbfbf">建议图片尺寸为750×480，支持图片格式为png/jpg/bmp/webp，大小不超过2M</div>
    </a-form-item>
    <a-form-item
        label="评价活动标题"
        name="title"
        :labelCol="{ span: 24 }"
        :rules="[{ required: true, message: '请输入标题', trigger: 'blur' }]"
        mb-10
    >
        <a-input v-model:value="data.title" show-count :maxlength="30" placeholder="请输入" />
    </a-form-item>
    <a-form-item
        label="评比活动介绍"
        :labelCol="{ span: 24 }"
        name="description"
        :rules="[{ required: true, message: '请输入评价活动介绍', trigger: 'blur' }]"
    >
        <Editor v-model="data.description" />
    </a-form-item>
</template>
<script setup>
// import imgSrc from '@/assets/images/select-empty-data.png'
import Editor from './Editor.vue'

const props = defineProps({
    data: {
        type: Object,
        default: {},
    },
    validateInfos: {
        type: Object,
        default: {},
    },
})

const fileType = ['png', 'jpg', 'bmp', 'webp', 'jpeg']
const beforeUpload = file => {
    const type = file.type.split('/')[1]
    if (!fileType.includes(type)) {
        YMessage.warning('仅支持png/jpg/bmp/webp类型的文件')
        return false
    }
    if (file.size > 1024 * 1024 * 2) {
        YMessage.warning('文件大小不能超过2M')
        return false
    }
    return true
}

const customRequest = e => {
    const file = e.file
    http.form('/file/common/upload', { file, folderType: 'evalActivity' }, {})
        .then(res => {
            const { data } = res
            props.data.promotionalImage = data[0].url
        })
        .catch(() => {
            YMessage.error('图片上传失败!')
        })
}

const deleteAvatar = () => {
    props.data.promotionalImage = ''
}

// 使用默认封面
const useDefaultImg = () => {
    props.data.promotionalImage = 'https://cloudcdn.yyide.com/evalActivity/eval/activity/eval_default.png'
}
</script>
<style scoped lang="less">
.upload_img {
    position: relative;
    img {
        width: 250px;
        height: 160px;
        object-fit: cover;
    }
    .close_btn {
        position: absolute;
        left: 235px;
        cursor: pointer;
        color: #c4c4c4;
    }
}

.defaultImg {
    color: #00b781;
    cursor: pointer;
}

.avatar-uploaderImg {
    width: 100%;
    height: 100%;
}

.avatar-uploaderImg {
    :deep(.ant-upload-select-picture-card) {
        width: 100% !important;
        height: 100% !important;
        margin: unset !important;
    }
}
</style>
