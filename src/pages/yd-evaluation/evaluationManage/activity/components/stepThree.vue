<template>
    <a-form-item
        label="活动时间"
        :labelCol="{ span: 24 }"
        mb-10
        name="dateSetting"
        :rules="[
            { required: true, message: '请选择活动时间' },
            { required: true, validator: dateSettingRules, trigger: ['change', 'blur'] },
        ]"
    >
        <a-radio-group v-model:value="data.dateSetting" w-full @change="changeDateSetting" :disabled="data.status">
            <a-radio :value="0" mb-10 w-full>按学年学期</a-radio>
            <div v-if="data.dateSetting === 0" mb-10>
                <a-select
                    :disabled="data.status"
                    @blur="changeDateblur"
                    v-model:value="data.schoolSemesterId"
                    placeholder="请选择学年学期"
                    :options="semesterOptions"
                    :field-names="{
                        label: 'name',
                        value: 'id',
                    }"
                ></a-select>
            </div>
            <div class="addSemester">新增学年学期</div>

            <a-radio :value="1" mb-10>自定义时间</a-radio>
            <a-range-picker
                @blur="changeDateblur"
                :disabled="data.status"
                :disabled-date="disabledDate"
                valueFormat="YYYY-MM-DD"
                format="YYYY-MM-DD"
                v-if="data.dateSetting === 1"
                v-model:value="data.customTime"
            />
        </a-radio-group>
    </a-form-item>
    <a-form-item
        label="评分周期"
        :labelCol="{ span: 2 }"
        mb-10
        labelAlign="left"
        name="cycle"
        :rules="[{ required: true, message: '请选择评分周期', trigger: 'change' }]"
    >
        <a-radio-group v-model:value="data.cycle" :disabled="data.status">
            <a-radio :value="0">每天</a-radio>
            <a-radio :value="1">每周</a-radio>
            <a-radio :value="2">每月</a-radio>
        </a-radio-group>
    </a-form-item>
    <a-form-item
        label="评价次数："
        :labelCol="{ span: 24 }"
        mb-10
        name="evalMaxNum"
        :rules="[{ required: true, message: '请输入评价次数', trigger: 'change' }]"
    >
        <a-input-number :disabled="data.status" v-model:value="data.evalMaxNum" :min="1" :max="100" placeholder="请输入评价次数" />
    </a-form-item>
    <a-form-item
        label="评价审核："
        :labelCol="{ span: 2 }"
        mb-10
        labelAlign="left"
        name="isApprove"
        :rules="[{ required: true, message: '请选择评价审核', trigger: 'change' }]"
    >
        <a-radio-group @change="changeIsApprove" v-model:value="data.isApprove" :disabled="data.status">
            <a-radio :value="1">是</a-radio>
            <a-radio :value="0">否</a-radio>
        </a-radio-group>
    </a-form-item>

    <a-form-item
        label="选择审核人："
        :labelCol="{ span: 24 }"
        mb-10
        name="approvesStr"
        :rules="[{ required: data.isApprove === 1, message: '请选择审核人', trigger: 'change' }]"
    >
        <a-input
            :disabled="data.status"
            v-model:value="data.approvesStr"
            placeholder="请选择"
            @click="openSelectModal"
            @blur="changeDateblur"
            readOnly
        />
    </a-form-item>

    <a-form-item label="积分规则：" :labelCol="{ span: 24 }" mb-10 name="settlementType">
        <a-select
            :disabled="data.status"
            v-model:value="data.settlementType"
            :options="jifenRuleOptions"
            placeholder="请选择"
            :field-names="{
                label: 'name',
                value: 'id',
            }"
        ></a-select>
    </a-form-item>

    <a-form-item label="最小评分单位：" :labelCol="{ span: 24 }" mb-10 name="minRatingStep">
        <a-input-number
            style="width: 100%"
            addon-after="分"
            v-model:value="data.minRatingStep"
            placeholder="请输入最小评分单位"
            :disabled="data.status"
        ></a-input-number>
    </a-form-item>

    <!-- 选人组件 -->
    <ModelSelect v-model:openVisible="modelState.openVisible" :tabs="state.peopleTabs" :selected="state.selectEcho" />
</template>
<script setup>
import dayjs from 'dayjs'
const formRef = inject('formRef')
const props = defineProps({
    data: {
        type: Object,
        default: {},
    },
    validateInfos: {
        type: Object,
        default: {},
    },
})

const jifenRuleOptions = ref([
    { name: '立即到账：每评价一次获得1分加1分', id: 1 },
    {
        name: '活动结束后到账：按平均分，计算方式为：每次评分总和÷总参与次数=最终得分',
        id: 2,
    },
])
const dateSettingRules = async (_rule, value) => {
    if (value) {
        if (!props.data.customTime.length) {
            return Promise.reject('请选择自定义时间!')
        } else {
            return Promise.resolve()
        }
    } else {
        if (!props.data.schoolSemesterId) {
            return Promise.reject('请选择学年学期!')
        } else {
            return Promise.resolve()
        }
    }
}

const semesterOptions = ref([])

const state = reactive({
    peopleTabs: [
        {
            tab: '教职工',
            checked: true,
            checkVisible: 'all',
            personField: { key: 'typeValue', value: ['people_dept'] },
            id: 1,
            single: false,
            searchOption: {
                show: true,
                displayMode: 'old',
            },
        },
    ],
})
const disabledDate = current => {
    return current && current < dayjs().subtract(1, 'day')
}

// 获取学年学期的列表数据 直接查100条吧多了不管
const getSemesterList = () => {
    let parame = {
        pageNo: 1,
        pageSize: 100,
        schoolYearId: '',
    }
    http.post('/cloud/semester/page', parame).then(res => {
        // console.log('获取学年学期的列表数据', res)
        semesterOptions.value = res.data.list
    })
}

getSemesterList()

// 切换学年学期数据
const changeDateSetting = () => {
    props.data.schoolSemesterId = null
    props.data.customTime = []
}

// 关联弹窗
const modelState = reactive({
    openVisible: false, // 显示弹框
    dataSource: [], // 左侧数据源
    checkVisible: 'all', // 能选什么就选什么啊
    spinning: false, // loading
    disableSelect: [], // 禁止选择的ids
    searchTable: [], // 选人搜索 table 中显示
    globalID: '', // 最顶成id
})
provide('modelState', () => modelState)
provide('callbackFunction', () => ({
    search: searchSelect,
    toggleLevel,
    cancel: closeSelect,
    submit,
}))

// 获取到选人组件数据
function getSelectTree(options = {}) {
    /**
     * 1. 教职工 treeType：2 ， businessType：21
     * 2. 学生  treeType：1 ， businessType：11
     * 3. 家长  treeType：1 ， businessType：12
     * 4. pid第一次为0.之后根据层级id请求
     */
    modelState.spinning = true
    const params = {
        treeType: 2,
        pid: 0,
        typeValue: null,
        businessType: 21,
        code: null,
        isRule: true,
        ...options,
    }

    http.post('/cloud/v3/tree/selectTree', params)
        .then(({ data }) => {
            setDataSource(data)
        })
        .finally(() => {
            modelState.spinning = false
        })
}

// 设置选人数据源
function setDataSource(data) {
    modelState.dataSource = data || []
}

// 请求下一级数据
function toggleLevel(tabId, item = {}, options) {
    const firstLevel = !options.index
    let params = {
        treeType: item.treeType,
        businessType: item.businessType,
        typeValue: item.typeValue,
        pid: item.id,
    }
    // 首层数据
    if (firstLevel) {
        params = {
            treeType: 2,
            businessType: 21,
        }
    }
    getSelectTree(params)
}

// 查找教职工 注意请求参数啊! 还没开始写
function searchSelect(tabId, name) {
    modelState.spinning = true
    const options = {
        treeType: 2,
        businessType: 21,
        code: null,
        isRule: true,
        searchKey: name,
        pageNo: 1,
        // 目前没有分页，最大条数
        pageSize: 100,
    }
    http.post('/cloud/v3/tree/selectTree/search', options)
        .then(({ data }) => {
            setDataSource(data.list || [])
        })
        .finally(() => {
            modelState.spinning = false
        })
}

const submit = checked => {
    props.data.approves = checked.map(item => {
        return {
            name: item.name,
            id: item.id,
            identity: item._type,
            _type: item._type,
            treeType: item.treeType,
            typeValue: item.typeValue,
            businessType: item.businessType,
        }
    })
    props.data.approvesStr = checked.map(item => item.name).join('、')
}

const closeSelect = () => {}

const openSelectModal = () => {
    getSelectTree()
    state.selectEcho = props.data.approves || []
    modelState.openVisible = true
}

const changeDateblur = () => {
    formRef.value.validate()
}

const changeIsApprove = e => {
    if (e.target.value) {
        formRef.value.validateFields(['approvesStr'])
    } else {
        formRef.value.clearValidate(['approvesStr'])
    }
}
</script>
<style scoped lang="less">
.addSemester {
    font-weight: 400;
    font-size: 14px;
    color: #00b781;
    cursor: pointer;
    padding-bottom: 16px;
}
</style>
