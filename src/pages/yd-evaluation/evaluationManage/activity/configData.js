export const formList = ref([
    {
        type: 'input',
        value: 'title',
        label: '活动名称',
        span: 6,
    },
    {
        type: 'input',
        value: 'partName',
        label: '参与人员',
        span: 6,
    },
    {
        type: 'select',
        value: 'status',
        label: '评比状态',
        list: [
            { label: '全部', value: null },
            { label: '未开始', value: 0 },
            { label: '进行中', value: 1 },
            { label: '已结束', value: 2 },
        ],
        span: 6,
    },
    {
        type: 'select',
        value: 'enable',
        label: '活动状态',
        list: [
            { label: '全部', value: null },
            { label: '启用', value: 1 },
            { label: '禁用', value: 0 },
        ],
        span: 6,
    },
    {
        type: 'rangePicker',
        value: ['startDate', 'endDate'],
        label: '活动时间',
        attrs: {
            valueFormat: 'YYYY-MM-DD',
        },
        span: 8,
    },
    {
        type: 'rangePicker',
        value: ['updateStartDate', 'updateEndDate'],
        label: '更新时间',
        attrs: {
            valueFormat: 'YYYY-MM-DD',
        },
        span: 8,
    },
])

export const columns = [
    { title: '活动名称', dataIndex: 'title' },
    { title: '活动时间', dataIndex: 'activityTime' },
    { title: '评比状态', dataIndex: 'status' },
    { title: '评价状态', dataIndex: 'operationFlag' },
    { title: '参与人员', dataIndex: 'names' },
    { title: '活动状态', dataIndex: 'enable' },
    { title: '创建人 ', dataIndex: 'createBy' },
    {
        title: '更新时间',
        dataIndex: 'updateTime',
        // sorter: true,
        width: 200,
    },
    { title: '操作', dataIndex: 'operate', fixed: 'right', width: 250 },
]

export const ruleColumns = [
    {
        title: '序号',
        width: 80,
        customRender: ({ text, record, index }) => `${index + 1}`, // 显示每一行的序号
    },
    { title: '规则名称', dataIndex: 'name', width: 300 },
    { title: '使用次数(次)', dataIndex: 'counts' },
    { title: '创建人', dataIndex: 'createBy' },
    {
        title: '更新时间',
        dataIndex: 'updateTime',
        sorter: {
            multiple: 1,
        },
    },
    { title: '操作', dataIndex: 'operate', fixed: 'right' },
]

export function arrDispose(arr) {
    let arr1 = []
    arr1 = arr.map(item => {
        let firstIndicators = []
        item.dataSource.forEach(data => {
            let existingFirstIndicator = firstIndicators.find(indicator => indicator.firstIndicatorId === data.firstIndicatorId)
            if (existingFirstIndicator) {
                let existingSecondIndicator = {
                    name: data.secondIndicatorName,
                    secondIndicatorId: data.secondIndicatorId,
                    valuers: data.valuers,
                    indicatorScore: {
                        content: data.standardName,
                        minScore: data.startScore,
                        maxScore: data.endScore,
                        scoreCardId: data.scoreCardId,
                        scoreCardName: data.scoreCardName,
                        evalScoreTypeList: data.evalScoreTypeList,
                    },
                }
                existingFirstIndicator.secondIndicators.push(existingSecondIndicator)
            } else {
                let newFirstIndicator = {
                    firstIndicatorId: data.firstIndicatorId,
                    publicRuleId: data.publicRuleId,
                    name: data.firstIndicatorName,
                    secondIndicators: [
                        {
                            name: data.secondIndicatorName,
                            secondIndicatorId: data.secondIndicatorId,
                            valuers: data.valuers,
                            indicatorScore: {
                                content: data.standardName,
                                minScore: data.startScore,
                                maxScore: data.endScore,
                                scoreCardId: data.scoreCardId,
                                scoreCardName: data.scoreCardName,
                                evalScoreTypeList: data.evalScoreTypeList,
                            },
                        },
                    ],
                }
                firstIndicators.push(newFirstIndicator)
            }
        })
        return {
            name: item.name,
            publicRuleId: item.publicRuleId,
            personType: item.personType,
            participants: item.participants,
            firstIndicators: firstIndicators,
        }
    })
    return arr1
}

export function dataSourceDispose(arr) {
    let firstIndicators = []
    arr.forEach(data => {
        let existingFirstIndicator = firstIndicators.find(indicator => indicator.firstIndicatorId === data.firstIndicatorId)
        if (existingFirstIndicator) {
            let existingSecondIndicator = {
                name: data.secondIndicatorName,
                id: data.secondIndicatorId.length > 8 ? data.secondIndicatorId : '',
                secondIndicatorId: data.secondIndicatorId,
                indicatorScore: {
                    id: data.indicatorScoreId,
                    content: data.content,
                    minScore: data.minScore,
                    maxScore: data.maxScore,
                    evalScoreTypeList: data.evalScoreTypeList,
                },
            }
            existingFirstIndicator.secondIndicators.push(existingSecondIndicator)
        } else {
            let newFirstIndicator = {
                firstIndicatorId: data.firstIndicatorId,
                id: data.firstIndicatorId.length > 8 ? data.firstIndicatorId : '',
                name: data.firstIndicatorName,
                secondIndicators: [
                    {
                        name: data.secondIndicatorName,
                        id: data.secondIndicatorId.length > 8 ? data.secondIndicatorId : '',
                        secondIndicatorId: data.secondIndicatorId,
                        indicatorScore: {
                            id: data.indicatorScoreId,
                            content: data.content,
                            minScore: data.minScore,
                            maxScore: data.maxScore,
                            evalScoreTypeList: data.evalScoreTypeList,
                        },
                    },
                ],
            }
            firstIndicators.push(newFirstIndicator)
        }
    })
    return firstIndicators
}
