<template>
    <cont-header />
    <div p-20>
        <div pb-20>
            <a-form :model="query" autocomplete="off">
                <a-row :gutter="[25, 16]">
                    <YCol>
                        <a-form-item label="姓名：">
                            <a-input v-model:value="query.personName" placeholder="请输入" />
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-form-item label="设备名称：">
                            <a-input v-model:value="query.deviceName" placeholder="请输入" />
                        </a-form-item>
                    </YCol>
                    <YCol :span="6">
                        <a-form-item label="兑换时间：" name="time">
                            <RangePicker v-model:startTime="query.exchangeStartTime" v-model:endTime="query.exchangeEndTime"></RangePicker>
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-button type="primary" @click="getList">
                            <SearchOutlined />
                            查 询
                        </a-button>
                        <a-button @click="handleReset">
                            <reload-outlined />
                            重 置
                        </a-button>
                    </YCol>
                </a-row>
            </a-form>
        </div>

        <div min-h-520 mb-20>
            <ETable
                :loading="page.loading"
                :dataSource="page.list"
                :total="page.total"
                @paginationChange="paginationChange"
                :current="query.pageNo"
                colSetting
                hash="redemptionRecord"
                :columns="columns"
                :minH="360"
                @change="handleTableChange"
            >
                <template #operate="{ record }">
                    <a-button v-if="record.status !== 1" type="link" class="btn-link-color" @click="refundpoints(record)">
                        退还积分
                    </a-button>
                </template>

                <template #status="{ record }">
                    <span>{{ record.status === 1 ? '已退还' : '-' }}</span>
                </template>
                <template #groupNameList="{ record }">
                    <span v-if="Array.isArray(record.groupNameList)" :title="record.groupNameList.join('、') ">{{ record.groupNameList.join('、')}}</span>
                    <span v-else>-</span>
                </template>
            </ETable>
        </div>
    </div>
</template>
<script setup>
const { query, page, getList, reset, paginationChange } = useList('/cloud/evalExchange/pageEvalExchangeRecord')
query.identity = 0

getList()

const handleReset = () => {
    reset({ identity: 0 })
}

const orderType = {
    ascend: '1',
    descend: '2',
}

// table排序切换
const handleTableChange = (pag, filters, sorter) => {
    query.order = orderType[sorter.order]
    getList()
}

const columns = ref([
    { title: '姓名', dataIndex: 'personName' },
    { title: '班级', dataIndex: 'groupNameList' },
    { title: '设备名称', dataIndex: 'deviceName' },
    { title: '兑换商品', dataIndex: 'commodityName' },
    { title: '状态', dataIndex: 'status' },
    { title: '商品价值', dataIndex: 'commodityScore' },
    {
        title: '兑换时间',
        dataIndex: 'createTime',
        sorter: true,
        width: 200,
    },
    {
        title: '操作',
        dataIndex: 'operate',
        width: 80,
    },
])

// 退还积分
const refundpoints = async record => {
    let flag = await yConfirm('退还积分', '退还后不可撤销，确认退还积分？')
    if (flag) {
        http.post('/cloud/evalExchange/evalExchangeReturn', { id: record.id }).then(res => {
            YMessage.success('操作成功')
            getList()
        })
    }
}
</script>
<style lang="less" scoped>
.msg_box {
    display: inline-block;
    font-size: 16px;
    background-color: #e0faea;
    padding: 9px 18px 9px 10px;
    border: 1px solid #bcefd2;
    border-radius: 4px;
    margin-top: 24px;
    margin-bottom: 12px;
}
</style>
