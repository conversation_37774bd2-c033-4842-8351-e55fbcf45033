<!-- 写一个绑定设备的弹窗放这里就是  自研的设备绑定场地  非自研的绑定场地还加序列号 -->
<template>
    <div>
        <a-modal
            v-model:open="state.open"
            title="绑定设备"
            :confirmLoading="state.loading"
            :maskClosable="false"
            :keyboard="false"
            @cancel="cancelForm"
            @ok="handleOk"
            okText="确定"
            cancelText="取消"
        >
            <div style="padding: 20px">
                <a-form
                    ref="deviceRef"
                    layout="vertical"
                    :model="deviceObj"
                    name="deviceObj"
                    :label-col="{ span: 24 }"
                    :wrapper-col="{ span: 24 }"
                    autocomplete="off"
                >
                    <a-form-item
                        style="margin-bottom: 16px"
                        label="选择场地："
                        name="siteId"
                        :rules="[
                            {
                                required: true,
                                message: '请选择场地',
                            },
                        ]"
                    >
                        <a-tree-select
                            v-model:value="deviceObj.siteId"
                            show-search
                            placeholder="请选择场地"
                            allow-clear
                            :treeDefaultExpandAll="true"
                            :listHeight="320"
                            :tree-data="state.fieldList"
                            :field-names="{
                                children: 'children',
                                label: 'name',
                                key: 'id',
                                value: 'id',
                            }"
                            treeNodeFilterProp="name"
                        ></a-tree-select>
                    </a-form-item>
                    <a-form-item
                        v-if="deviceMode === '2'"
                        label="设备序列号："
                        name="deviceNo"
                        :rules="[
                            {
                                required: true,
                                message: '请输入设备序列号',
                            },
                        ]"
                    >
                        <a-input v-model:value="deviceObj.deviceNo" placeholder="请输入设备序列号" />
                    </a-form-item>
                </a-form>
            </div>
        </a-modal>
    </div>
</template>

<script setup>
import { reactive, onMounted, ref } from 'vue'

import { message, Modal } from 'ant-design-vue'

const props = defineProps({
    deviceId: {
        type: [String, Number],
        default: '',
    },
    deviceMode: {
        type: [String, Number],
        default: '',
    },
})

const emit = defineEmits(['confirmBtn'])
const state = ref({
    fieldList: [],
    open: false,
    loading: false,
})
const deviceRef = ref(null)
const deviceObj = ref({})

const cancelForm = () => {
    deviceObj.value = {}
    deviceRef.value.resetFields()
    state.value.open = false
}

const handleOk = () => {
    deviceRef.value.validateFields().then(() => {
        http.post('/cloud/v2/machine/bindDeviceById', {
            deviceId: props.deviceId,
            ...deviceObj.value,
        }).then(res => {
            cancelForm()
            message.success(res.message)
            emit('confirmBtn')
        })
    })
}

const openModal = () => {
    state.value.open = true
}

// 禁用选中父节点
const disabledP = tree => {
    tree.forEach(item => {
        if (item.children) {
            item.disabled = true
            disabledP(item.children)
        }
    })
}

// 场地接口
const getSiteList = () => {
    http.get('/cloud/v2/machine/site/list', {
        type: 2,
    }).then(res => {
        const { data } = res
        state.value.fieldList = data
        disabledP(data)
    })
}

getSiteList()

defineExpose({ cancelForm, openModal })
</script>

<style lang="less" scoped></style>
