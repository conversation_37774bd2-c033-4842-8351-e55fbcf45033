<!-- eslint-disable -->
<template>
    <div>
        <div class="titleBox">德育兑换机列表</div>
        <div class="searchBox">
            <a-input-search placeholder="请输入德育兑换机序列号/德育兑换机名称" style="width: 336px" allowClear @search="onSearch" />
            <a-button type="primary" @click="addequipment">
                <template #icon>
                    <plus-outlined />
                </template>
                新增德育兑换机
            </a-button>
        </div>
        <div min-h-520 mb-20>
            <ETable
                :loading="page.loading"
                :dataSource="page.list"
                :total="page.total"
                @paginationChange="paginationChange"
                :current="query.pageNo"
                @change="handleTableChange"
            >
                <template #headerCell-machineStatus>
                    <a-dropdown placement="bottomRight">
                        <a color-black @click.prevent>
                            状态
                            <caret-down-outlined style="color: var(--primary-color)" />
                        </a>
                        <template #overlay>
                            <a-menu>
                                <a-menu-item v-for="item in statusList" :key="item.status" @click="selectStatus(item)">
                                    <span
                                        :style="{
                                            color: item.status === state.selectedStatus ? 'var(--primary-color)' : 'inherit',
                                        }"
                                    >
                                        {{ item.label }}
                                    </span>
                                </a-menu-item>
                            </a-menu>
                        </template>
                    </a-dropdown>
                </template>
                <a-table-column title="场地" data-index="siteName">
                    <template #default="{ record }">
                        <span>{{ record.siteName || '-' }}</span>
                    </template>
                </a-table-column>
                <a-table-column title="德育兑换机名称" data-index="machineName" />
                <a-table-column title="状态" data-index="machineStatus" :width="150">
                    <template #default="{ record }">
                        <a-badge :color="getStatusObj(record.machineStatus).color" :text="getStatusObj(record.machineStatus).text" />
                    </template>
                </a-table-column>
                <a-table-column title="账号密码" data-index="deviceAccount" :width="250">
                    <template #default="{ record }">
                        <span v-if="record.deviceAccount">{{ record.deviceAccount }}/{{ record.devicePwd }}</span>
                        <span v-else>-</span>
                    </template>
                </a-table-column>

                <a-table-column title="德育兑换机序列号" data-index="no">
                    <template #default="{ record }">
                        <span>{{ record.no || '-' }}</span>
                    </template>
                </a-table-column>
                <a-table-column title="注册时间" data-index="updateTime" />
                <a-table-column title="操作" data-index="operate" :width="200">
                    <template #default="{ record }">
                        <a-button type="link" class="btn-link-color" @click="editequipment(record)">编辑</a-button>
                        <!-- <a-tooltip placement="top" v-if="record.isBinding == '1'">
                            <template #title>
                                <span>该设备还未激活</span>
                            </template>
                            <exclamation-circle-outlined style="color: #efa216f2; padding-right: 8px" />
                        </a-tooltip> -->

                        <a-button v-if="record.isBinding === '1'" type="link" class="btn-link-color" @click="onBindDevice(record)">
                            绑定设备
                        </a-button>
                        <a-button v-if="record.isBinding === '2'" type="link" class="btn-link-color" @click="onUnbindDevice(record)">
                            解绑设备
                        </a-button>
                        <a-button type="link" danger class="btn-link-color" @click="delequipment(record)">删除</a-button>
                    </template>
                </a-table-column>
            </ETable>
        </div>
        <a-drawer :width="500" @close="handleClose" v-model:open="state.medalOpen" :keyboard="false" :destroyOnClose="true" :title="title">
            <div class="addedOrder">
                <a-form
                    layout="vertical"
                    :model="addedFormState"
                    ref="addedFormStateRef"
                    autocomplete="off"
                    :label-col="{ span: 8 }"
                    :wrapper-col="{ span: 16 }"
                >
                    <!-- <a-form-item
                        :label-col="{ span: 24 }"
                        :wrapper-col="{ span: 24 }"
                        name="siteId"
                        :rules="[{ required: true, message: '请选择!' }]"
                        label="场地："
                        mb-6
                    >
                        <a-tree-select
                            v-model:value="addedFormState.siteId"
                            show-search
                            placeholder="请选择"
                            allow-clear
                            :treeDefaultExpandAll="true"
                            :listHeight="320"
                            :tree-data="state.fieldList"
                            :field-names="{
                                children: 'children',
                                label: 'name',
                                key: 'id',
                                value: 'id',
                            }"
                            :disabled="state.pattern === 'edit'"
                            :showSearch="false"
                        ></a-tree-select>
                    </a-form-item> -->
                    <a-form-item
                        label="设备名称:"
                        name="machineName"
                        :label-col="{ span: 24 }"
                        :wrapper-col="{ span: 24 }"
                        mt-12
                        :rules="[{ required: true, message: '请输入设备名称!' }]"
                    >
                        <a-input v-model:value.trim="addedFormState.machineName" show-count :maxlength="30" placeholder="请输入" />
                    </a-form-item>

                    <a-form-item
                        label="设备型号"
                        name="deviceManufacturer"
                        :label-col="{ span: 24 }"
                        :wrapper-col="{ span: 24 }"
                        mt-12
                        :rules="[{ required: true, message: '请选择设备型号!' }]"
                    >
                        <a-select
                            :field-names="{
                                label: 'deviceManufacturer',
                                value: 'deviceManufacturer',
                            }"
                            @change="deviceManufacturerChange"
                            :disabled="state.pattern === 'edit'"
                            v-model:value="addedFormState.deviceManufacturer"
                            placeholder="请选择设备型号"
                            :options="state.schoolEquipmentTypeList"
                        ></a-select>
                    </a-form-item>

                    <a-form-item
                        v-if="state.pattern === 'add'"
                        label="设备数量："
                        name="machineNumber"
                        :label-col="{ span: 24 }"
                        :wrapper-col="{ span: 24 }"
                        mt-12
                        :rules="[{ required: true, message: '请输入设备数量!' }]"
                    >
                        <a-input v-model:value.trim="addedFormState.machineNumber" placeholder="请输入1-20的数字" />
                    </a-form-item>
                    <a-form-item
                        label="管理员："
                        name="administratorName"
                        :label-col="{ span: 24 }"
                        :wrapper-col="{ span: 24 }"
                        mt-12
                    >
                        <a-input
                            readonly
                            v-model:value.trim="addedFormState.administratorName"
                            @click="handleSelectModel"
                            placeholder="请选择"
                        />
                        <!-- <div style="margin-top: 8px">
                            <exclamation-circle-filled />
                            <span style="color: #c0c4cc; font-size: 12px; margin-left: 6px">最多选择三个管理员</span>
                        </div> -->
                    </a-form-item>
                </a-form>
            </div>
            <template #footer>
                <div class="footer">
                    <a-button @click="handleClose">取消</a-button>
                    <a-button type="primary" @click="submitThing">确认</a-button>
                </div>
            </template>
        </a-drawer>
    </div>
    <!-- 绑定设备 -->
    <BindDevice ref="bindDeviceRefs" @confirmBtn="confirmBtn" :deviceId="state.deviceId" :deviceMode="state.deviceMode"></BindDevice>
    <!-- 选人组件 -->
    <ModelSelect v-model:openVisible="modelState.openVisible" :tabs="state.peopleTabs" :selected="state.selectEcho" />
</template>

<script setup name="educationExchange">
import { reactive, onMounted, ref } from 'vue'
import BindDevice from './bindDevice.vue'
const mainStore = useStore()
const bindDeviceRefs = ref(null)
const state = ref({
    selectedStatus: '',
    pattern: 'add',
    fieldList: [],
    selectEcho: [],
    medalOpen: false,
    formState: {
        deviceType: 25,
    },
    peopleTabs: [
        {
            tab: '教职工',
            checked: true,
            checkVisible: 'people',
            personField: { key: 'typeValue', value: ['people_dept'] },
            id: 1,
            single: false,
            searchOption: {
                show: true,
                displayMode: 'old',
            },
        },
    ],
})

const statusList = [
    { label: '全部', status: '' },
    { label: '在线', status: 1 },
    { label: '离线', status: 0 },
]

const title = computed(() => {
    return state.value.pattern === 'edit' ? '编辑德育兑换机' : '新增德育兑换机'
})

const addedFormState = ref({
    machineNumber: 1,
})
const addedFormStateRef = ref()

const { query, page, getList, reset, paginationChange } = useList('/cloud/v2/machine/page', {
    ...state.value.formState,
})
getList()

const statusObj = {
    1: {
        text: '在线',
        color: '#00B781',
    },
    0: {
        text: '离线',
        color: '#595959',
    },
    default: {
        text: '-',
        color: '',
    }, // 默认值存储在 default 属性中
}

function getStatusObj(key) {
    return statusObj[key] || statusObj.default
}

const onSearch = val => {
    state.value.formState.no = val
    getList({ ...state.value.formState })
}

const addequipment = () => {
    state.value.pattern = 'add'
    state.value.medalOpen = true
}

// 禁用选中父节点
const disabledP = tree => {
    tree.forEach(item => {
        if (item.children) {
            item.disabled = true
            disabledP(item.children)
        }
    })
}

// 查找
function getSiteList(tabId, name) {
    const options = {
        type: 2,
    }
    http.get('/cloud/v2/machine/site/list', options).then(({ data }) => {
        state.value.fieldList = data
        disabledP(state.value.fieldList)
    })
}
getSiteList()

// 关联弹窗
const modelState = reactive({
    openVisible: false, // 显示弹框
    dataSource: [], // 左侧数据源
    checkVisible: 'people', // 能选什么就选什么啊
    spinning: false, // loading
    disableSelect: [], // 禁止选择的ids
    searchTable: [], // 选人搜索 table 中显示
    globalID: '', // 最顶成id
})
provide('modelState', () => modelState)
provide('callbackFunction', () => ({
    search: searchSelect,
    toggleLevel,
    cancel: closeSelect,
    submit,
}))

// 获取到选人组件数据
function getSelectTree(options) {
    /**
     * 1. 教职工 treeType：2 ， businessType：21
     * 2. 学生  treeType：1 ， businessType：11
     * 3. 家长  treeType：1 ， businessType：12
     * 4. pid第一次为0.之后根据层级id请求
     */
    modelState.spinning = true
    const params = {
        treeType: 2,
        pid: 0,
        typeValue: null,
        businessType: 21,
        code: null,
        isRule: true,
        ...options,
    }

    http.post('/cloud/v3/tree/selectTree', params)
        .then(({ data }) => {
            setDataSource(data)
        })
        .finally(() => {
            modelState.spinning = false
        })
}
getSelectTree({ treeType: 2, businessType: 21 })

// 设置选人数据源
function setDataSource(data) {
    modelState.dataSource = data || []
}

// 请求下一级数据
function toggleLevel(tabId, item = {}, options) {
    const firstLevel = !options.index
    let params = {
        treeType: item.treeType,
        businessType: item.businessType,
        typeValue: item.typeValue,
        pid: item.id,
    }
    // 首层数据
    if (firstLevel) {
        params = {
            treeType: 2,
            businessType: 21,
        }
    }
    getSelectTree(params)
}

// 查找
function searchSelect(tabId, name) {
    modelState.spinning = true
    const options = {
        treeType: 2,
        businessType: 21,
        code: null,
        isRule: true,
        searchKey: name,
        pageNo: 1,
        // 目前没有分页，最大条数
        pageSize: 100,
    }
    http.post('/cloud/v3/tree/selectTree/search', options)
        .then(({ data }) => {
            setDataSource(data.list || [])
        })
        .finally(() => {
            modelState.spinning = false
        })
}

const closeSelect = () => {}

// 点击确定的人员
const submit = checked => {
    if (checked.length > 20) {
        YMessage.warning('管理员最多选择20个')
        state.value.selectEcho = checked.slice(0, 20)
        addedFormState.value.administratorName = state.value.selectEcho.map(i => i.name).join('、')
    } else {
        state.value.selectEcho = checked
        addedFormState.value.administratorName = state.value.selectEcho.map(i => i.name).join('、')
    }
    state.value.selectEcho = checked
    addedFormState.value.administratorName = state.value.selectEcho.map(i => i.name).join('、')
}

const handleSelectModel = () => {
    getSelectTree({ treeType: 2, businessType: 21 })
    modelState.openVisible = true
}

const submitThing = () => {
    addedFormStateRef.value.validate().then(() => {
        const params = {
            deviceType: state.value.formState.deviceType,
            ...addedFormState.value,
            teacherId: state.value.selectEcho.map(item => item.id),
        }
        if (state.value.pattern === 'add') {
            http.post('/cloud/v2/machine/create', params).then(res => {
                YMessage.success('操作成功')
                handleClose()
                getList({ ...state.value.formState })
            })
        } else {
            params.id = state.value.editId
            http.post('/cloud/v2/machine/update', params).then(res => {
                YMessage.success('操作成功')
                handleClose()
                getList({ ...state.value.formState })
            })
        }
    })
}

// 关闭弹窗
const handleClose = () => {
    addedFormState.value = {}
    state.value.medalOpen = false
    state.value.selectEcho = []
}

//删除设备
const delequipment = async data => {
    const flag = await yConfirm(
        '删除提示',
        '删除后不可恢复，确认删除已选设备？',
    )
    if (flag) {
        http.get('/cloud/v2/machine/delete', {
            id: data.id,
        }).then(res => {
            YMessage.success('操作成功')
            getList({ ...state.value.formState })
        })
    }
}

const editequipment = data => {
    state.value.editId = data.id
    http.get('/cloud/v2/machine/detail', {
        id: data.id,
    }).then(res => {
        console.log('res', res)
        state.value.medalOpen = true
        state.value.pattern = 'edit'
        // addedFormState.value.siteId = res.data.siteId
        addedFormState.value.machineName = res.data.machineName
        addedFormState.value.deviceManufacturer = res.data.deviceManufacturer
        addedFormState.value.administratorName = res.data.administratorName
        state.value.selectEcho = res.data.administratorList.map(v => {
            return {
                ...v,
                id: v.teacherId,
            }
        })
    })
}

// 切换状态
const selectStatus = item => {
    state.value.selectedStatus = item.status
    reset({ ...query, ...state.value.formState, machineStatus: item.status })
}

// 获取型号的列表
const reqBrandlistBySchool = () => {
    const schoolId = mainStore.userInfo?.schoolId
    console.log('schoolId', schoolId)
    http.post('/manage/school/device/brand/listBySchool', {
        schoolId,
        equipmentTypes: [25],
    }).then(({ data }) => {
        const allocatedArr = data.schoolEquipmentTypeList[0]?.deviceBrandDTOList || []
        state.value.schoolEquipmentTypeList = allocatedArr.filter(item => item.allocated)
    })
}

const onBindDevice = data => {
    console.log('data', data)
    state.value.deviceId = data.id
    state.value.deviceMode = data.deviceMode
    bindDeviceRefs.value.openModal()
}

const onUnbindDevice = async item => {
    const flag = await yConfirm('解绑设备', '确定解绑当前设备？解绑后对应数据会立刻清除')
    if (flag) {
        http.post('/cloud/v2/machine/unbindDeviceByNo', { deviceId: item.id }).then(res => {
            YMessage.success('操作成功')
            getList({ ...state.value.formState })
        })
    }
}

const confirmBtn = () => {
    getList({ ...state.value.formState })
}

// 型号切换
const deviceManufacturerChange = (val, opt) => {
    console.log('opt', opt)
    addedFormState.value.deviceMode = opt.deviceMode
}
reqBrandlistBySchool()
</script>

<style lang="less" scoped>
.titleBox {
    font-size: 18px;
    font-weight: 500;
    padding-bottom: 16px;
}
.searchBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 16px;
}
</style>
