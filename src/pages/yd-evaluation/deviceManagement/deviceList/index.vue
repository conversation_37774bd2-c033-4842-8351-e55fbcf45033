<template>
    <div>
        <div style="margin-left: 20px">
            <a-tabs v-model:activeKey="state.activeKey">
                <a-tab-pane key="educationExchange" tab="德育兑换机"></a-tab-pane>
                <!-- <a-tab-pane key="educationIntegral" tab="德育积分机"></a-tab-pane> -->
            </a-tabs>
        </div>
        <div style="padding: 16px">
            <component :is="comType[state.activeKey]"></component>
        </div>
    </div>
</template>

<script setup name="deviceList">
import { reactive, onMounted, ref } from 'vue'

import educationExchange from './educationExchange.vue'
import educationIntegral from './educationIntegral.vue'

const comType = {
    educationExchange,
    educationIntegral,
}

const state = reactive({
    activeKey: 'educationExchange',
})
</script>

<style lang="less" scoped></style>
