<template>
    <a-modal mt-20 mb-20 :open="open" :destroyOnClose="true" :title="title" @ok="updateSync" @cancel="cancel">
        <a-spin :spinning="loading">
            <div m-24>
                <p font-size-14 font-400 color-black text-opacity-85 line-height-22>
                    {{ title }}人员信息？
                </p>
                <p font-size-14 font-600 color-black text-opacity-85 line-height-22 mt-24>
                    当前设备
                </p>
                <!-- 后续提取 -->
                <a-checkbox v-model:checked="state.checked" w-full flex flex-justify-between flex-items-center
                    class="checkbox">
                    <div style="color:#00B781">
                        <div flex flex-justify-between flex-items-center>
                            <p class="device-name" style="color:#00B781">
                                <span class="dot" :style="{
                                    background:
                                        cloneFormState.currentDevice
                                            .machineStatus === 1
                                            ? '#00b781'
                                            : '#BFBFBF',
                                }"></span>
                                {{ cloneFormState.currentDevice.machineName }}
                            </p>
                            <p flex flex-items-center justify-content-center>
                            <p>{{ cloneFormState.currentDevice.synchronousNumber }}</p>
                            <p class="face-number" style="color:#00B781">{{ cloneFormState.currentDevice.faceNumber }}</p>
                            </p>
                        </div>
                        <p>{{ cloneFormState.currentDevice.siteName }}</p>
                    </div>
                </a-checkbox>
                <div v-if="cloneFormState.otherDevice.length > 0">
                    <p font-size-14 font-400 color-black text-opacity-85 line-height-22 mt-16 mb-16>
                        以下设备存在相同人员，可选择同时{{
                            props.type === 'sync' ? '更新' : '删除'
                        }}
                    </p>
                    <a-checkbox v-model:checked="state.checkAll" :indeterminate="state.indeterminate"
                        @change="onCheckAllChange">
                        全选
                    </a-checkbox>
                    <a-checkbox-group v-model:value="state.checkedList" style="width: 100%" mt-20>
                        <a-checkbox :value="item" v-for="item in cloneFormState.otherDevice" :key="item.id"
                            class="checkbox">
                            <div flex flex-justify-between flex-items-center mb-8>
                                <p class="device-name">
                                    <span class="dot" :style="{
                                        background:
                                            item
                                                .machineStatus === 1
                                                ? '#00b781'
                                                : '#BFBFBF',
                                    }"></span>
                                    {{ item.machineName }}
                                </p>
                                <p flex flex-items-center justify-content-center>
                                <p color-black>{{
                                    item.synchronousNumber || 0
                                }}</p>
                                <p class="face-number"> {{ item.faceNumber || 0 }}</p>
                                </p>
                            </div>
                            <p color-black text-opacity-85>{{ item.siteName }}</p>
                        </a-checkbox>
                    </a-checkbox-group>
                </div>
            </div>
        </a-spin>
    </a-modal>
</template>

<script setup name="face_Modal">
import { computed, toRaw } from 'vue'

// *********************
// Hooks
// *********************

const emit = defineEmits(['confirm', 'cancel'])
const props = defineProps({
    open: {
        type: Boolean,
        default: false,
    },
    formState: {
        type: Object,
        default: () => { },
    },
    type: {
        type: String,
        enum: ['sync', 'delete'],
        default: [],
    },
    loading: {
        type: Boolean,
        default: false,
    },
})

const cloneFormState = ref({})
let state = reactive({
    // 当前设备
    checked: true,
    // 全选
    checkAll: false,
    // 全选列表
    checkedList: [],
    indeterminate: false
})

const rulesRef = reactive({
    week: [
        {
            required: true,
            message: '请选择重复周期',
            type: 'array',
        },
    ],
})

// *********************
// Computed Function
// *********************

const title = computed(() => {
    return props.type === 'sync' ? '同步' : '删除'
})

// *********************
// Service Function
// *********************

// 转换表单数据
const convertFromState = newValue => {
    // 判断数据是否为空对象，给定初始值
    const cloneFormData = deepClone({ ...newValue })
    cloneFormState.value = cloneFormData
}

const onCheckAllChange = (e) => {
    Object.assign(state, {
        checkedList: e.target.checked ? cloneFormState.value.otherDevice : [],
        indeterminate: false
    })
}
// 初始化数据
const reset = () => {
    state = reactive({
        // 当前设备
        checked: true,
        // 全选
        checkAll: false,
        // 全选列表
        checkedList: [],
        indeterminate: false
    })
}

// 更新闭馆数据
const updateSync = () => {
    const { items } = cloneFormState.value
    emit('confirm', {
        deviceId: items.deviceId,
        userId: items.userId,
        checkedList: toRaw(state.checkedList)
    })
    reset();
}


const cancel = () => {
    reset();
    emit('cancel')
}

// *********************
// Watch Function
// *********************
watch(
    () => props.formState,
    newValue => {
        convertFromState(newValue)
    },
    { deep: true },
)

watch(
    () => props.open,
    newValue => {
        // $ 关闭弹窗时，因为组件不会重新执行声明周期，恢复原有数据
        if (!newValue) {
            convertFromState(props.formState)
        }
    },
)

watch(
    () => state.checkedList,
    (val) => {
        state.indeterminate =
            !!val.length && val.length < cloneFormState.value.otherDevice.length
        state.checkAll = val.length === cloneFormState.value.otherDevice.length
    }
)
</script>

<style lang="less" scoped>
.checkbox {
    display: flex;
    align-items: center;
    width: 100%;
    height: 72px;
    margin-bottom: 8px;

    :deep(span:nth-of-type(2)) {
        display: flex;
        flex-direction: column;
        justify-content: center;
        flex: 1;
        margin-left: 8px;
        height: 72px;
        background: rgba(0, 183, 129, 0.08);
        border-radius: 4px;
        line-height: 20px;
        box-sizing: border-box;
        padding: 12px;
    }

    .device-name {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
        line-height: 20px;

        .dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 8px;
            margin-right: 6px;
        }
    }

    .face-number {
        position: relative;
        color: rgba(0, 0, 0, 0.85);

        &::before {
            content: '/';
        }
    }
}
</style>
