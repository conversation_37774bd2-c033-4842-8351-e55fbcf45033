<template>
    <div class="deviceManagementPage" h-full flex>
        <div class="deviceList" h-full flex flex-col>
            <div flex flex-justify-between p-b-16>
                <div font-size-14 font-semibold>设备列表</div>
                <div font-size-14 font-semibold>共{{ state.deviceList.length }}台</div>
            </div>
            <div p-b-16>
                <!-- <a-input-search placeholder="请输入设备名称/序列号" allowClear v-model:value.trim="state.deviceName"
                    @search="searchDevice" /> -->
                <a-input-search allowClear v-model:value.trim="state.deviceName" placeholder="请输入"
                    @search="searchDevice">
                    <template #addonBefore>
                        <a-select class="input-search" v-model:value.trim="state.searchType">
                            <a-select-option value="no">序列号</a-select-option>
                            <a-select-option value="name">设备名称</a-select-option>
                        </a-select>
                    </template>
                </a-input-search>
            </div>
            <div flex flex-justify-between p-b-16>
                <a-button type="primary" :disabled="!state.checkedDeviceList.length"
                    @click="openModalSelect(true)">批量关联人脸库</a-button>
                <a-button type="primary" :disabled="!state.checkedDeviceList.length"
                    @click="oneAllSync(true)">批量同步人脸库</a-button>

            </div>

            <div flex-auto overflow-y-auto>
                <a-spin h-full mt-10 :spinning="state.deviceLoading" tip="数据加载中...">
                    <div ref="equipmentListRef" v-if="!!state.deviceList.length">
                        <a-checkbox style="margin-left: 12px" :disabled="!state.online_list.length"
                            v-model:checked="state.isCheckAll" :indeterminate="state.indeterminate"
                            @change="onCheckAllChange">
                            全选
                        </a-checkbox>
                        <a-checkbox-group v-model:value="state.checkedDeviceList" style="width: 100%">
                            <ul mt-15 class="book-spin">
                                <li class="device-item" width-252 h-52 pl-12 pr-12 flex flex-justify-between
                                    flex-items-center cursor-pointer v-for="item in state.deviceList" :key="item.id"
                                    @click="selectDevice(item)" :class="{
                                        'select-device': item.id === state.selectedDevice?.id,
                                    }">
                                    <a-checkbox style="margin-right: 10px;" :value="item"
                                        :disabled="item.machineStatus != 1" />
                                    <a-tooltip placement="top" :title="item.machineName" :class="{
                                        'disabled-tooltip': item.machineName?.length <= 10,
                                    }">
                                        <div flex flex-justify-between flex-items-center>
                                            <span mr-8 class="dot" :style="{
                                                background: item.machineStatus == 1 ? 'var(--primary-color)' : '#BFBFBF',
                                            }"></span>
                                            <p w-157 font-600 line-height-20 font-size-14 class="text-ellipsis">
                                                {{ item.machineName }}
                                            </p>
                                        </div>
                                    </a-tooltip>
                                    <p class="text-number">
                                        <span>
                                            {{ item.synchronousNumber || 0 }}
                                        </span>
                                        <span class="face-number" :style="{
                                            color: item.id === state.selectedDevice?.id ? 'inherit' : 'rgba(0,0,0,0.65)',
                                        }">
                                            {{ item.faceNumber || 0 }}
                                        </span>
                                    </p>
                                </li>
                            </ul>
                        </a-checkbox-group>
                    </div>
                    <div v-else flex flex-col flex-justify-center flex-items-center h-500>
                        <img src="@/assets/images/rfid/faceEmpty.png" />
                        <span font-size-14 font-500 color-black text-opacity-85 text-center>暂无设备数据</span>
                    </div>
                </a-spin>
            </div>
        </div>
        <div class="deviceTable">
            <div font-size-16 font-semibold p-b-8>
                <span>{{ state.selectedDevice?.machineName || '' }}</span>
            </div>
            <div p-b-16>
                <span font-400 font-size-14 color-black-0.85 line-height-20>
                    {{ state.selectedDevice?.buildingName ? `${state.selectedDevice?.buildingName}-` : '' }}
                    {{ state.selectedDevice?.siteName || '' }}
                </span>
                <span ml-24 font-400 font-size-14 line-height-20 color-black text-opacity-85>
                    <span color-black text-opacity-45>序列号：</span>
                    {{ state.selectedDevice?.no || '' }}
                </span>
            </div>
            <!-- 按钮 -->
            <div mb-16 flex flex-justify-between>
                <div flex-auto min-w-600>
                    <searchForm v-model:formState="query" :formList="formList" @submit="search" @reset="handleReset">
                    </searchForm>
                </div>
                <div flex flex-items-center>
                    <a-button type="primary" @click="openModalSelect(false)">关联人脸库</a-button>
                    <a-button type="primary" :disabled="!page.list.length" @click="oneAllSync(false)">一键同步</a-button>
                    <a-button danger :disabled="!page.list.length" @click="oneDelete">清 空</a-button>
                </div>
            </div>
            <div>
                <!-- 表格 -->
                <ETable hash="deviceManagementTable" :loading="page.loading" :columns="columns" :minH="560"
                    :data-source="page.list" :total="page.total" @paginationChange="paginationChange"
                    :current="query.pageNo">
                    <template #headerCell-userType>
                        <a-dropdown placement="bottomRight">
                            <a color-black @click.prevent>
                                人员类型
                                <caret-down-outlined style="color: var(--primary-color)" />
                            </a>
                            <template #overlay>
                                <a-menu>
                                    <a-menu-item v-for="item in state.userTypeList" :key="item.value"
                                        @click="selectUserType(item)">
                                        <span :style="{
                                            color: item.value === state.selectedUserType?.value ? 'var(--primary-color)' : 'inherit',
                                        }">
                                            {{ item.label }}
                                        </span>
                                    </a-menu-item>
                                </a-menu>
                            </template>
                        </a-dropdown>
                    </template>
                    <template #headerCell-status>
                        <a-dropdown placement="bottomRight">
                            <a color-black @click.prevent>
                                状态
                                <caret-down-outlined style="color: var(--primary-color)" />
                            </a>
                            <template #overlay>
                                <a-menu>
                                    <a-menu-item v-for="item in statusList" :key="item.status"
                                        @click="selectStatus(item)">
                                        <span :style="{
                                            color: item.status === state.selectedStatus?.status ? 'var(--primary-color)' : 'inherit',
                                        }">
                                            {{ item.label }}
                                        </span>
                                    </a-menu-item>
                                </a-menu>
                            </template>
                        </a-dropdown>
                    </template>

                    <template #operate="{ record }">
                        <a class="btn-link-color" @click="openModal(record, 'sync')">同步</a>
                        <a class="button-link-delete" @click="openModal(record, 'delete')">删除</a>
                    </template>
                </ETable>
            </div>
        </div>
    </div>

    <!-- 选人组件 -->
    <ModelSelect :openVisible="modelState.openVisible" :tabs="modelSelectTabs" />
    <faceModal :type="state.operateType" :formState="colRecord" :loading="state.faceModalLoading" :open="open"
        @confirm="confirm" @cancel="cancel" />
</template>

<script setup name="deviceManagement">
import { state, formList, columns, statusList, defaultUserType, TAB_ID_MAP } from './configData.js'
import faceModal from './components/faceModal.vue'
const modelSelectTabs = [
    {
        tab: '教职工',
        checked: true,
        // 区分类型
        id: TAB_ID_MAP.STAFF,
        // 用于区分是人还是其他的key
        legacy_personKey: 'userId',
        // 复选
        single: false,
    },
    {
        tab: '学生',
        checked: false,
        id: TAB_ID_MAP.STUDENT,
        // 用于区分是人还是其他的key
        legacy_personKey: 'studentCode',
        // 复选
        single: false,
    },
]
// *********************
// Hooks
// *********************
// 关联弹窗
const modelState = reactive({
    openVisible: false, // 显示弹框
    dataSource: [], // 左侧数据源
    checkVisible: 'all',
    spinning: false, // loading
    disableSelect: [], // 禁止选择的ids
    searchTable: [], // 选人搜索 table 中显示
    globalID: '', // 最顶成id
    pages: {
        pageNo: 1,
        pageSize: 500,
        total: 0,
    },
})
provide('modelState', () => modelState)
provide('callbackFunction', () => ({
    search: searchSelect,
    toggleTabs,
    toggleLevel,
    cancel: closeSelect,
    submit,
    onScroll,
}))

// 同步删除弹窗
const open = ref(false)
let colRecord = reactive({
    items: {},
    // 当前设备
    currentDevice: {},
    // 所有设备
    otherDevice: [],
    currentDeviceNum: 0
})

// const props = defineProps({
//     deviceType: {
//         type: Number,
//     },
//     faceOpen: {
//         type: Boolean,
//         default: false,
//     },
// })

// const emit = defineEmits(['update:faceOpen'])

// state.formState.deviceType = Number(props.deviceType)

// *********************
// Life Event Function
// *********************

// 获取人员类型列表
const getUserType = () => {
    http.post('/cloud/SystemDict/get', ['face_user_type']).then(res => {
        const { data } = res
        const list = data[0]?.list ?? []
        state.userTypeList = [defaultUserType, ...list]
    })
}
getUserType()

// 获取到table数据
const { query, page, getList, reset, paginationChange } = useList('/cloud/faceSync/getRecordPage', state.formState)

// 获取机器列表
const getMachineList = () => {
    state.deviceLoading = true
    const params = {
        pageSize: 100,
        pageNo: 1,
        // name: state.deviceName,
        // deviceType: 25,
        [state.searchType]: state.deviceName,
        deviceTypeList: [25, 29],
    }
    http.post('/cloud/v2/machine/page', params)
        .then(res => {
            const { list, total, pageSize, pageNo } = res.data
            state.deviceList = list
            state.online_list = list.filter((item) => item.machineStatus === 1);
            state.all = total;
            if (list.length) {
                // 当前选中的id
                const id = state.selectedDevice?.id
                // 先前被选中的items还存在
                const item = list.find(item => item.id === id)
                // 默认聚焦第一个 并且获取table数据
                selectDevice(item ?? list[0])
            }
        })
        .finally(() => {
            state.deviceLoading = false
        })
}

getMachineList()

// 获取到教职工数据
const getStaffList = () => {
    return new Promise(resolve => {
        http.get('/cloud/app/dept/list')
            .then(({ data }) => {
                state.staffList = data || []
                // 选人组件 - 首次聚焦教职工
                modelState.globalID = data[0]?.id || ''
                setDataSource(data)
            })
            .catch(err => {
                YMessage.error(`获取教职工数据失败`)
            })
            .finally(() => {
                resolve()
            })
    })
}

// 获取学生
const getStudentList = () => {
    return new Promise(resolve => {
        http.get('/cloud/app/roll/listTree')
            .then(({ data }) => {
                state.studentList = data || []
            })
            .catch(err => {
                YMessage.error(`获取学生列表数据失败`)
            })
            .finally(() => {
                resolve()
            })
    })
}

async function getPersonData() {
    modelState.spinning = true
    await getStaffList()
    await getStudentList()
    modelState.spinning = false
}

getPersonData()

// 获取到指定用户下的机器
function getFaceModalMachine(item) {
    state.faceModalLoading = true
    const params = {
        pageSize: 100,
        pageNo: 1,
        syncUserIds: [item.userId],
        syncEquipmentId: item.deviceId,
        deviceTypeList: [25, 29],
        // deviceType: state.formState.deviceType,
    }
    http.post('/cloud/v2/machine/page', params)
        .then(res => {
            const { list } = res.data
            colRecord.otherDevice = list || []
        })
        .finally(() => {
            state.faceModalLoading = false
        })
}

// 同步人脸库
function syncFaceData(options = {}) {
    const params = {
        ...options,
    }
    if (state.operateType === 'sync') {
        // 同步
        http.post('/cloud/faceSync/v2/sync', params)
            .then(({ message }) => {
                YMessage.success(message)
                getList()
            })
            .catch(() => {
                state.loading = false
            })
    } else {
        // 删除
        http.post('/cloud/faceSync/sync/clear', params)
            .then(({ message }) => {
                YMessage.success(message)
                getList()
            })
            .catch(() => {
                state.loading = false
            })
    }
}

// *********************
// Service Function
// *********************

// 搜索设备列表
const searchDevice = () => {
    // 搜索时清空全选状态
    state.checkedDeviceList = 0
    state.isCheckAll = false;
    getMachineList()
}
const equipmentListRef = shallowRef();

// 设备列表更加类型搜索
watch(
    () => state.searchType,
    (val, oldVal) => {
        // 切换类型后 滚动条回到顶部
        if (equipmentListRef.value) {
            equipmentListRef.value.scrollTop = 0;
        }

        if (val !== oldVal && state.deviceName) {
            state.deviceName = "";
            searchDevice();
        }
    }
);
// 选择机器
function selectDevice(item) {
    state.selectedDevice = item
    const { id, schoolId = '' } = item
    colRecord.currentDevice = item
    colRecord.currentDeviceNum = item.faceNumber
    reset({ ...query, id, schoolId })
}

// 选择人员类型
const selectUserType = item => {
    state.selectedUserType = item
    reset({ ...query, userType: item.value })
}

// 切换状态
const selectStatus = item => {
    state.selectedStatus = item
    reset({ ...query, status: item.status })
}

// 重置搜搜
const handleReset = () => {
    const { id, schoolId = '' } = state.selectedDevice
    reset({
        ...query,
        // deviceType: state.formState.deviceType,
        deviceTypeList: [25, 29],
        pageNo: 1,
        id,
        schoolId,
    })
    state.selectedUserType = defaultUserType
    state.selectedStatus = statusList[0]
}

// 搜索
const search = () => {
    reset({ ...query, pageNo: 1 })
}

// 查找学生
function searchStudent(name) {
    modelState.spinning = true
    http.post('/cloud/student/search', { name })
        .then(({ data }) => {
            modelState.searchTable = data
            // setDataSource(data)
        })
        .finally(() => {
            modelState.spinning = false
        })
}

function searchStaff(name) {
    modelState.spinning = true
    http.post('/cloud/dept/searchDeptUserList', { name })
        .then(({ data }) => {
            // 追加userId, 选人组件需要用户判断是人还是其他
            const userList = data.userList.map(item => ({
                ...item,
                userId: '',
            }))
            // setDataSource([...data.deptList, ...userList])
            modelState.searchTable = userList
        })
        .finally(() => {
            modelState.spinning = false
        })
}

// 查找教职工
function searchSelect(tabId, name) {
    if (name) {
        if (tabId === TAB_ID_MAP.STAFF) {
            // 选人组件 - 首次聚焦教职工
            searchStaff(name)
        } else if (tabId === TAB_ID_MAP.STUDENT) {
            searchStudent(name)
        }
    } else {
        // name为空时，不发送请求，恢复最原始数据
        tabId === TAB_ID_MAP.STAFF ? setDataSource(state.staffList) : setDataSource(state.studentList)
    }
}

// 切换tabs
function toggleTabs(tab) {
    // 切换
    if (tab.id === TAB_ID_MAP.STAFF) {
        // 选人组件 - 首次聚焦教职工
        modelState.globalID = state.staffList[0]?.id || ''
        setDataSource(state.staffList)
    } else if (tab.id === TAB_ID_MAP.STUDENT) {
        modelState.globalID = state.studentList[0]?.id || ''
        setDataSource(state.studentList)
    }
}

// 设置选人数据源
function setDataSource(data) {
    modelState.dataSource = data || []
}

// 查找教职工数据
async function getStaffPage(item) {
    modelState.spinning = true
    const { id = '' } = item
    const params = {
        deptId: id,
        status: '',
        id: '',
        name: '',
        code: 'evaluation',
        statusList: [9, 10, 11, 12, 13, 14, 15, 16, 17],
        pageNo: modelState.pages.pageNo,
        pageSize: modelState.pages.pageSize,
    }
    try {
        const { data } = await http.post('/cloud/employee/page', params)
        // 当前items数据加上员工数据
        let children = item.children || []
        modelState.dataSource = children?.concat(data.list)
        const { pageNo, total } = data
        // $ 因为后端如果接口没有员工数据时 pagesize会为0，所有这里不重新设置pageSize(他下个版本修复)
        modelState.pages.pageNo = pageNo
        modelState.pages.total = total
    } finally {
        modelState.spinning = false
    }
}

// 查找学生数据
async function getStudentPage(item) {
    modelState.spinning = true
    const { id = '', type = 0 } = item
    const params = {
        id,
        type,
        status: '',
        code: 'evaluation',
        statusList: [1, 2, 4, 5, 12, 13, 99],
        pageNo: modelState.pages.pageNo,
        pageSize: modelState.pages.pageSize,
    }
    try {
        const { data } = await http.post('/cloud/student/page', params)
        // 当前items数据加上学生数据
        let children = item.children || []
        const { studentPageListVO = {} } = data
        modelState.dataSource = children?.concat(studentPageListVO.list || [])
        const { pageNo, total } = studentPageListVO
        // $ 因为后端如果接口没有员工数据时 pagesize会为0，所有这里不重新设置pageSize(他下个版本修复)
        modelState.pages.pageNo = pageNo
        modelState.pages.total = total
    } finally {
        modelState.spinning = false
    }
}

const onScroll = async (tab, item = {}) => {
    // 切换
    if (tab.id === TAB_ID_MAP.STAFF) {
        await getStaffPage({ ...item, children: modelState.dataSource })
    } else if (tab.id === TAB_ID_MAP.STUDENT) {
        await getStudentPage({ ...item, children: modelState.dataSource })
    }
}

// 请求下一级数据
function toggleLevel(tabId, item = {}, options) {
    const firstLevel = !options.index
    if (tabId === TAB_ID_MAP.STAFF) {
        if (firstLevel) {
            // 第一层数据，恢复原本数据
            modelState.dataSource = state.staffList
        } else {
            getStaffPage(item)
        }
    } else if (tabId === TAB_ID_MAP.STUDENT) {
        if (firstLevel) {
            // 第一层数据，恢复原本数据
            modelState.dataSource = state.studentList
        } else {
            getStudentPage(item)
        }
    }
}

// 关联人脸库数据
// function postFaceData(userInfo) {
//     const params = {
//         deviceId: query.id,
//         userInfo,
//     }
//     http.post('/cloud/faceDevice/V3/bindFaceInfo', params).then(({ message }) => {
//         YMessage.success(message)
//         getList()
//     })
// }

// 提交关联人脸库
function submit(checked) {
    // 类型说明:
    // 学籍时返回:班级 classes，学生:student，家长:eltern
    // 部门时返回:部门:dept，部门员工:people_dept
    // 角色时返回:角色:role，角色员工:people_role
    // 外部人员返回:组:external，组成员:people_external
    let userInfo = checked?.map((item) => {
        const { rollValue, id, userId, name, showName, _type } = item;
        let parma = {
            id,
            name: showName || name,
        };
        const isType = (Type) => item.hasOwnProperty(Type);
        //identity 身份标识(0.学生 1.教职工 2.家长 3. 外部人员 4.自定义组)
        if (_type === 2) {
            parma.identity = 1;
            // 教职工
            if (isType("pid")) {
                parma.typeValue = "dept";
            } else {
                parma.typeValue = "people_dept";
                parma.userId = userId;
            }
        } else if (_type === 1) {
            // 学生
            parma.identity = 0;
            if (isType("pid")) {
                parma.typeValue = rollValue;
            } else {
                parma.typeValue = "student";
                parma.userId = userId;
            }
        }
        return parma;
    }) || [];
    const params = {
        personListDTO: userInfo,
    };
    // 如果有数据则是批量关联人脸库
    if (state.checkedDeviceList.length) {
        params.deviceIds = state.checkedDeviceList.map((v) => v.id);
    } else {
        params.deviceId = query.id
    }
    modelState.dataSource = state.staffList

    http.post('/cloud/faceDevice/V3/bindFaceInfo', params).then(({ message }) => {
        YMessage.success(message)
        getList()
    })
}

// 恢复第一层数据。新的接口是每一层都会请求的
function closeSelect() {
    modelState.dataSource = state.staffList
}

// 批量打开 | 单个item打开
const openModal = (record, operateType) => {
    getFaceModalMachine(record)
    state.operateType = operateType
    colRecord.items = record ?? {}
    open.value = true
}

const confirm = options => {
    // 同步数据
    const { userId, checkedList, deviceId } = options
    const ids = checkedList.map(item => item.id)
    syncFaceData({
        userIds: [userId],
        syncDeviceIds: ids,
        deviceId,
        // deviceType: state.formState.deviceType,
        deviceTypeList: [25, 29],
    })
    open.value = false
}

const cancel = () => {
    open.value = false
}

// 批量同步、一键同步
const oneAllSync = async (bol) => {
    let params = {}
    // 批量同步
    if (bol) {
        params.deviceIds = state.checkedDeviceList.map((v) => v.id);
    } else {
        state.checkedDeviceList = 0
        state.isCheckAll = false;
        params.deviceId = state.selectedDevice.id
    }
    let flag = await yConfirm('一键同步', '确定同步列表中所有人员的人脸信息？')
    if (flag) {
        state.operateType = 'sync'

        syncFaceData(params)
    }
}

// 一键清除
const oneDelete = async () => {
    let flag = await yConfirm('清空', '确定清空本机所有的人员信息？')
    if (flag) {
        state.operateType = 'delete'
        const params = { deviceId: state.selectedDevice.id }
        syncFaceData(params)
    }
}

// 选人组件
const openModalSelect = (bol) => {
    if (!bol) {
        state.checkedDeviceList = 0
        state.isCheckAll = false;
    }
    modelState.openVisible = true
}


// 全选 只选择该死的在线的设备 服了服了服了
const onCheckAllChange = (e) => {
    Object.assign(state, {
        checkedDeviceList: e.target.checked ? state.online_list : [],
        indeterminate: false,
    });
};

watch(
    () => state.checkedDeviceList,
    (val) => {
        state.indeterminate =
            !!val.length && val.length < state.online_list.length;
        state.isCheckAll =
            state.online_list.length && val.length === state.online_list.length;
    }
);
</script>

<style lang="less" scoped>
.deviceManagementPage {
    .deviceList {
        flex: 0 0 293px;
        width: 293px;
        overflow: hidden;
        border-right: 1px solid #d9d9d9;
        padding: 20px 10px;
    }

    .deviceTable {
        flex: auto;
        padding: 16px;
    }
}
</style>

<style lang="less" scoped>
.device-item {
    border-bottom: 1px solid #d9d9d9;
}

.dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 8px;
}

.act {
    color: var(--primary-color);
}

.disabled-tooltip {
    pointer-events: none;
    cursor: default;
}

.text-ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.text-number {
    width: 45px;
    text-align: right;
}

.select-device {
    background: rgba(0, 183, 129, 0.08);
    color: #00b781;
}

.face-number {
    position: relative;

    &::before {
        content: '/';
    }
}

.nav {
    border-bottom: 1px solid #d9d9d9;

    i {
        font-size: 17px;
        color: #00b781;
        font-weight: 600;
    }

    .title {
        font-size: 16px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
    }
}
</style>
