export const statusList = [
    { label: '全部', status: null },
    { label: '已同步', status: 0 },
    { label: '异常', status: 1 },
    { label: '未同步', status: 2 },
    { label: '同步中', status: 3 },
    { label: '删除中', status: 4 },
]

// tab的id映射
export const TAB_ID_MAP = {
    // 教职工
    STAFF: 2,
    // 学生
    STUDENT: 1,
    // 外部人员
    OUTSIDERS: 3,
}

// 默认人员类型
export const defaultUserType = {
    label: '全部',
    status: 0,
    machineStatus: 0,
    value: null,
}

// *********************
// Hooks
// *********************

export const state = reactive({
    // 设备表
    deviceList: [],
    // 当前聚焦的设备
    selectedDevice: {},
    // 设备列表loading
    deviceLoading: true,
    // 人员类型列表
    userTypeList: [defaultUserType],
    // 选中的人员类型
    selectedUserType: defaultUserType,
    // 选中的状态
    selectedStatus: statusList[0],
    // 查询参数
    formState: {
        id: '',
        schoolId: '',
        userType: null,
        name: '',
        status: null,
        deviceType: 1,
    },
    // 教职工列表
    staffList: [],
    // 学生列表
    studentList: [],
    // 操作类型
    operateType: 'sync',
    // 表格加载
    tableLoading: false,
    // 同步弹窗loading
    faceModalLoading: false,
    // 设备搜索-------
    // 设备名称
    deviceName: '',
    // list: [],
    searchType: 'name',
    online_list: [],
    isCheckAll: false,
    indeterminate: false,
    checkedDeviceList: [],
    // device: {
    //     pageNo: 1,
    //     pageSize: 20,
    //     total: 0,
    // },
})

export const formList = ref([
    {
        type: 'input',
        value: 'name',
        label: '',
        span: 5,
    },
])

export const columns = ref([
    { title: 'ID', dataIndex: 'id' },
    { title: '姓名', dataIndex: 'userName' },
    {
        title: '人员类型',
        dataIndex: 'userType',
        customRender: ({ text }) => {
            return text === TAB_ID_MAP.STAFF ? '老师' : text === TAB_ID_MAP.STUDENT ? '学生' : '非本校师生'
        },
    },
    {
        title: '状态',
        dataIndex: 'status',
        customRender: ({ text }) => {
            const label = statusList.find(item => text === item.status)?.label
            const background = [0].includes(text) ? '#00B781' : [null, 2, 3].includes(text) ? '#BFBFBF' : '#f5222d'
            return h(
                'p',
                {
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                },
                [
                    h('span', {
                        style: {
                            display: 'inline-block',
                            width: '6px',
                            height: '6px',
                            background,
                            borderRadius: '6px',
                            marginRight: '7px',
                        },
                    }),
                    h(
                        'span',
                        {
                            style: {
                                verticalAlign: 'middle',
                            },
                        },
                        label,
                    ),
                ],
            )
        },
    },
    { title: '有效期（结束日期）', dataIndex: 'expirationEndTime', customRender: ({ text }) => text || '-' },
    { title: '更新时间', dataIndex: 'syncTime', customRender: ({ text }) => text || '-' },
    { title: '操作', dataIndex: 'operate', width: 120 },
])
