<template>
    <div class="manage">
        <cont-header />

        <article>
            <div class="left">
                <a-input-search
                    v-model:value="state.personName"
                    placeholder="请输入姓名"
                    :allowClear="true"
                    @search="searchByName"
                    :style="{ paddingRight: '16px' }"
                />
                <a-spin :spinning="state.leftLoading">
                    <div class="no-data" v-if="isNotData">暂无数据</div>
                    <div>
                        <div class="a-tree-wrap" v-if="!state.isSearch">
                            <a-tree
                                v-model:expandedKeys="state.expandedKeys"
                                :selectedKeys="state.selectedKeys"
                                :tree-data="state.classTree"
                                :fieldNames="{ children: 'children', title: 'name', key: 'id' }"
                                @select="selectClass"
                            >
                                <template #title="{ dataRef }">
                                    <!-- <div flex flex-items-center overflow-x-hidden> -->
                                    <i
                                        :class="[
                                            'iconfont',
                                            'icon-a-Fill21',
                                            state.selectedKeys.includes(dataRef.id) ? 'icon-green' : 'icon-yellow',
                                        ]"
                                    ></i>
                                    <!-- <a-tooltip :title="dataRef.name" placement="bottomRight">
                                            <p class="ellipsis tree-title">
                                                {{ dataRef.name }}
                                            </p>
                                        </a-tooltip> -->
                                    {{ dataRef.name }}
                                    <!-- </div> -->
                                </template>
                            </a-tree>
                        </div>
                        <ul class="search-wrap" v-else>
                            <li
                                v-for="item in state.searchList"
                                :key="item.personIds"
                                @click="selectPerson(item)"
                                :style="{ background: state.personId === item.personId ? '#dff7ec' : '#fff' }"
                            >
                                {{ item.personName }}
                            </li>
                        </ul>
                    </div>
                </a-spin>
            </div>

            <div class="right">
                <ETable
                    hash="manage"
                    :columns="columns"
                    :loading="page.loading"
                    :dataSource="page.list"
                    :total="page.total"
                    @paginationChange="paginationChange"
                    :current="query.pageNo"
                    @change="handleTableChange"
                >
                    <template #totalScore="{ text }">
                        {{ text ?? '-' }}
                    </template>

                    <template #remainingScore="{ text }">
                        {{ text ?? '-' }}
                    </template>

                    <template #operate="{ record }">
                        <a-button type="link" class="btn-link-color" @click="openInDecreasePoints(record)">增减积分</a-button>
                        <a-button type="link" class="btn-link-color" @click="openDetails(record)">积分明细</a-button>
                    </template>
                </ETable>
            </div>
        </article>

        <InDecreasePoints ref="inDecreasePointsRef" @confirm="getList({ pageNo: query.pageNo })" />
        <Details ref="detailsRef" />
    </div>
</template>

<script setup>
import InDecreasePoints from './InDecreasePoints.vue'
import Details from './Details.vue'

const columns = [
    { title: '学生姓名', dataIndex: 'personName' },
    { title: '所在班级', dataIndex: 'groupName' },
    { title: '累计获得积分', dataIndex: 'totalScore' },
    { title: '当前剩余积分', dataIndex: 'remainingScore' },
    { title: '操作', dataIndex: 'operate' },
]

// *********************
// Hooks Function
// *********************

const inDecreasePointsRef = ref(null)
const detailsRef = ref(null)

const state = reactive({
    formState: {
        classesId: '',
        studentId: '',
    },
    personId: '',
    personName: '',
    isSearch: false,
    searchList: [],
    expandedKeys: [],
    selectedKeys: [],
    classTree: [],
    leftLoading: false,
})

const isNotData = computed(() => {
    // 左侧列表没有数据
    return (!state.isSearch && !state.classTree.length) || (state.isSearch && !state.searchList.length)
})

// *********************
// Default Function
// *********************

// 获取到table数据
const { query, page, getList, paginationChange } = useList('/cloud/evalScoreManage/pagePersonScore', state.formState)

const getClassTree = async () => {
    try {
        state.leftLoading = true
        const { data } = await http.get('/cloud/evalScoreManage/listTree')
        state.classTree = data
        // 默认全部展开
        state.expandedKeys = data.map(item => item.id)
        // 默认选中第一个班级
        for (let item of data) {
            if (item.children?.length) {
                state.selectedKeys = [item.children[0].id]
                query.classesId = item.children[0].id
                break
            }
        }
        getList()
    } finally {
        state.leftLoading = false
    }
}

// *********************
// Life Event Function
// *********************

getClassTree()

// *********************
// Service Function
// *********************

const openInDecreasePoints = record => {
    inDecreasePointsRef.value.showModal(record)
}

const openDetails = record => {
    detailsRef.value.showModal(record)
}

const selectClass = async (selectedKeys, evt) => {
    const { selectedNodes } = evt

    const index = state.expandedKeys.findIndex(item => item === selectedKeys[0])
    if (~index) {
        state.expandedKeys.splice(index, 1)
    } else {
        state.expandedKeys.push(selectedKeys[0])
    }
    if (!selectedNodes[0].children.length) {
        state.selectedKeys = [selectedNodes[0].id]
        query.classesId = selectedNodes[0].id
        getList()
    }
}

const searchByName = async value => {
    if (value) {
        try {
            state.leftLoading = true
            state.isSearch = true
            const { data } = await http.post('/cloud/evalScoreManage/searchPersonScoreStudent', {
                personName: state.personName,
            })
            state.searchList = data
            // 默认选中第一个
            selectPerson(data[0])
        } finally {
            state.leftLoading = false
        }
    } else {
        query.classesId = state.selectedKeys[0]
        query.studentId = null
        state.isSearch = false
        getList()
    }
}

const selectPerson = (item = {}) => {
    query.classesId = null
    query.studentId = item.personId || null
    state.personId = item.personId || null
    getList()
}
</script>

<style lang="less" scoped>
.manage {
    display: flex;
    flex-direction: column;
    height: 100%;
}

article {
    box-sizing: border-box;
    display: flex;
    flex: 1;
    overflow: hidden;
    .left {
        display: flex;
        flex-direction: column;
        height: 100%;
        width: 284px;
        padding: 24px 0 24px 16px;
        border-right: 1px solid #d9d9d9;
        overflow: hidden;

        :deep(.ant-spin-nested-loading) {
            height: 100%;
            overflow-y: auto;
            padding-right: 16px;
        }
        :deep(.ant-spin-container) {
            height: 100%;
        }

        .no-data {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: rgba(0, 0, 0, 0.25);
        }

        .a-tree-wrap {
            margin-top: 24px;
        }

        .search-wrap {
            margin-top: 24px;
            li {
                height: 30px;
                line-height: 30px;
                cursor: pointer;

                &:hover {
                    background: #f6f6f6 !important;
                }
            }
        }
    }
    .right {
        box-sizing: border-box;
        flex: 1;
        height: 100%;
        padding: 24px;
        overflow-y: auto;
    }
}

:deep(.ant-tree-treenode) {
    width: 100%;
    overflow: hidden;
}

:deep(.ant-tree-node-content-wrapper) {
    width: 100%;
    overflow: hidden;
}

.tree-title {
    flex: 1;
}
.icon-yellow {
    color: @warning-color;
    padding-right: 6px;
    font-size: 14px;
}

.icon-green {
    color: @primary-color;
    padding-right: 6px;
    font-size: 14px;
}
</style>
