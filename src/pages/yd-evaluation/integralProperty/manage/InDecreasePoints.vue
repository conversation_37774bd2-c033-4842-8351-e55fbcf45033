<template>
    <YModal v-model:open="state.open" :width="569" title="增减积分" :confirmLoading="state.confirmLoading" @confirm="confirm">
        <div p-24>
            <ul class="grid-info">
                <li>
                    <p class="label">学生姓名：</p>
                    <p class="ellipsis value" :title="state.record.personName">{{ state.record.personName }}</p>
                </li>
                <li>
                    <p class="label">所在班级：</p>
                    <p class="ellipsis value" :title="state.record.groupName">{{ state.record.groupName }}</p>
                </li>
                <li>
                    <p class="label">当前剩余积分：</p>
                    <p class="ellipsis value" :title="state.record.remainingScore">{{ state.record.remainingScore }}</p>
                </li>
            </ul>

            <a-form ref="formRef" class="form-grid" :model="state.formState" autocomplete="off" :labelCol="{ style: 'width: 88px' }">
                <a-form-item label="增减积分：" name="score" :rules="[{ required: true, validator: validator }]">
                    <a-input-number
                        v-model:value="state.formState.score"
                        :addon-before="addonBefore"
                        :addon-after="addonAfter"
                        :min="-state.record.remainingScore"
                        :controls="false"
                    ></a-input-number>
                    <p :style="{ color: '#FAAD14' }" mt-2>说明：输入正数为增加积分，输入负数为减少积分</p>
                </a-form-item>
                <a-form-item label="备注：" name="remark">
                    <a-textarea :rows="5" v-model:value="state.formState.remark" :maxlength="200" showCount />
                </a-form-item>
            </a-form>
        </div>
    </YModal>
</template>

<script setup>
const defaultFormState = {
    remark: '',
    score: 0,
}

// *********************
// Hooks Function
// *********************

const emit = defineEmits(['confirm'])

const formRef = ref(null)

const state = reactive({
    open: false,
    record: {},
    formState: deepClone(defaultFormState),
    confirmLoading: false,
})

// *********************
// Default Function
// *********************

const validator = (rule, value) => {
    console.log('value: ', value)
    if (!value) {
        return Promise.reject(new Error('请输入非零的数字'))
    }
    return Promise.resolve()
}

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

const plusNumber = () => {
    state.formState.score = state.formState.score + 1
}

const minusNumber = () => {
    if (state.formState.score > -state.record.remainingScore) {
        state.formState.score = state.formState.score - 1
    }
}

const addonBefore = h(
    'span',
    {
        style: {
            display: 'inline-block',
            cursor: 'pointer',
            width: '28px',
            height: '30px',
            lineHeight: '30px',
            userSelect: 'none',
        },
        onClick: minusNumber,
    },
    '-',
)

const addonAfter = h(
    'span',
    {
        style: {
            display: 'inline-block',
            cursor: 'pointer',
            width: '28px',
            height: '30px',
            lineHeight: '30px',
            userSelect: 'none',
        },
        onClick: plusNumber,
    },
    '+',
)

const confirm = async () => {
    try {
        await formRef.value.validate()
        state.confirmLoading = true
        const params = {
            personId: state.record.personId,
            ...state.formState,
        }
        const res = await http.post('/cloud/evalScoreManage/changePersonScore', params)
        YMessage.success(res.message)

        emit('confirm')
        state.open = false
    } finally {
        state.confirmLoading = false
    }
}

// *********************
// DefineExpose Function
// *********************

const showModal = record => {
    state.formState = deepClone(defaultFormState)
    state.record = record
    state.open = true
}

defineExpose({ showModal })
</script>

<style lang="less" scoped>
.grid-info {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-column-gap: 10px;
    li {
        display: flex;
        width: 100%;
        overflow: hidden;
        .label {
            color: #262626;
        }
        .value {
            flex: 1;
            color: #595959;
        }
    }
}

.form-grid {
    display: grid;
    grid-row-gap: 24px;
    margin-top: 24px;

    :deep(.ant-input-number-input) {
        text-align: center;
    }

    :deep(.ant-input-textarea-show-count::after) {
        position: relative;
        margin-top: -25px;
        margin-right: 8px;
        z-index: 1;
    }
}
</style>
