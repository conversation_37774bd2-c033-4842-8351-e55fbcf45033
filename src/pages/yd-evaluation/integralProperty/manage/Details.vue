<template>
    <YModal v-model:open="state.open" :width="1160" title="积分明细" :footer="null" @confirm="confirm">
        <div p-24 pb-50>
            <ul class="grid-info">
                <li>
                    <p class="label">学生姓名：</p>
                    <p class="ellipsis value" :title="state.record.personName">{{ state.record.personName }}</p>
                </li>
                <li>
                    <p class="label">所在班级：</p>
                    <p class="ellipsis value" :title="state.record.groupName">{{ state.record.groupName }}</p>
                </li>
                <li>
                    <p class="label">累计获得积分：</p>
                    <p class="ellipsis value" :title="state.record.totalScore">{{ state.record.totalScore || '-' }}</p>
                </li>
                <li>
                    <p class="label">当前剩余积分：</p>
                    <p class="ellipsis value" :title="state.record.remainingScore">{{ state.record.remainingScore || '-' }}</p>
                </li>
            </ul>

            <searchForm
                v-model:formState="query"
                :formList="formList"
                @submit="getList()"
                @reset="reset({ identity: 0, personId: state.record.personId })"
                mt-24
                mb-24
            ></searchForm>
            <div w-full>
                <ETable
                    hash="Details"
                    :loading="page.loading"
                    :columns="columns"
                    :dataSource="page.list"
                    :total="page.total"
                    @paginationChange="paginationChange"
                    :current="query.pageNo"
                    @change="sortByTable"
                >
                    <template #scoreFormat="{ text }">
                        {{ scoreFormatName(text) }}
                    </template>
                    <template #_type="{ record }">
                        {{ record.scoreFormat === 1 ? '兑换' : '得分' }}
                    </template>

                    <template #evalTypeName="{ text }">
                        {{ text || '-' }}
                    </template>
                </ETable>
            </div>
        </div>
    </YModal>
</template>

<script setup>
// *********************
// Hooks Function
// *********************

const emit = defineEmits(['confirm'])

const formList = ref([
    {
        type: 'select',
        value: 'scoreFormat',
        label: '得分形式',
        list: [
            {
                value: 1,
                label: '积分卡兑换',
            },
            {
                value: 2,
                label: '参与评价活动',
            },
            {
                value: 3,
                label: '德育机兑换',
            },
            {
                value: 4,
                label: '勋章奖励',
            },
            {
                value: 5,
                label: '积分退还',
            },
            {
                value: 6,
                label: '手动增减积分',
            },
        ],
        span: 8,
    },
    {
        type: 'select',
        value: 'evalTypeId',
        label: '评价类型',
        list: [],
        attrs: {
            fieldNames: {
                label: 'name',
                value: 'id',
            },
        },
        span: 8,
    },
])

const state = reactive({
    open: false,
    formState: {
        personId: '',
        evalTypeId: null,
        scoreFormat: null,
        identity: 0,
        order: null,
    },
    record: {},
    sorter: {},
})

const columns = computed(() => {
    const { field, order } = state.sorter

    return [
        { title: '积分类型', dataIndex: '_type', width: 100 },
        { title: '得分形式', dataIndex: 'scoreFormat' },
        { title: '评价类型 ', dataIndex: 'evalTypeName' },
        { title: '得分 ', dataIndex: 'score', width: 100 },
        { title: '当前剩余积分 ', dataIndex: 'remainingScore', width: 120 },
        { title: '时间 ', dataIndex: 'createTime', sorter: true, sortOrder: field === 'createTime' ? order : null },
        { title: '备注 ', dataIndex: 'remark' },
    ]
})

const scoreFormatName = computed(() => {
    return i => {
        return (
            formList.value
                .find(item => {
                    return item.value == 'scoreFormat'
                })
                .list.find(item => item.value == i)?.label || '-'
        )
    }
})

// *********************
// Default Function
// *********************

const { query, page, getList, reset, paginationChange } = useList('/cloud/evalScoreRecord/page', state.formState)

/** 更新list下的选项列表 */
const formEchoData = (key, list) => {
    formList.value = formList.value.map(item => {
        return {
            ...item,
            list: item.value == key ? list : item.list,
        }
    })
}

const getEvalTypeList = async () => {
    const { data } = await http.get('/cloud/evalType/listBySchool')
    formEchoData('evalTypeId', data || [])
}

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

// 排序
const sortByTable = (page, filters, sorter) => {
    state.sorter = sorter
    if (sorter.order) {
        const order = sorter.order === 'descend' ? '1' : '2'
        query.order = order
        getList()
    } else {
        getList({ order: null })
    }
}

// *********************
// DefineExpose Function
// *********************

const showModal = record => {
    state.record = record
    query.personId = record.personId
    getList()
    getEvalTypeList()
    state.open = true
}

defineExpose({ showModal })
</script>

<style lang="less" scoped>
.grid-info {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-column-gap: 10px;
    li {
        display: flex;
        width: 100%;
        overflow: hidden;
        .label {
            color: #262626;
        }
        .value {
            flex: 1;
            color: #595959;
        }
    }
}
</style>
