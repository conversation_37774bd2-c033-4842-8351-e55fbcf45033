<!--
 * @Author: 得了得了又不是造原子弹
 * @Date: 2024-09-21 17:15:54
 * @LastEditors: 杨剑兴
 * @LastEditTime: 2024-09-21 17:20:32
 * @FilePath: \退积分了\yd-admin\src\pages\yd-evaluation\integralProperty\pointsRecord\index.vue
-->
<template>
    <cont-header type="menu" :menuList="menuList" v-model:activeKey="activeKey"></cont-header>
    <PointsRecord v-if="activeKey === 1"></PointsRecord>
    <CancelRecord v-if="activeKey === 2"></CancelRecord>
</template>
<script setup name="tagPrint">
import PointsRecord from './pointsRecord.vue'
import CancelRecord from './cancelRecord.vue'
const activeKey = ref(1)

const menuList = ref([
    { name: '积分记录', value: 1 },
    { name: '积分卡核销记录', value: 2 },
])
</script>
<style lang="less" scoped></style>
