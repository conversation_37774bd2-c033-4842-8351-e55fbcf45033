<!-- 积分记录 -->
<template>
    <div p-20>
        <div mb-20>
            <a-form :model="query" autocomplete="off">
                <a-row :gutter="[25, 16]">
                    <YCol>
                        <a-form-item label="学生姓名：">
                            <a-input v-model:value="query.personName" placeholder="请输入" />
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-form-item label="所在班级：">
                            <a-input v-model:value="query.groupName" placeholder="请输入" />
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-form-item label="活动名称：">
                            <a-input v-model:value="query.activityTitle" placeholder="请输入" />
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-form-item label="得分形式：">
                            <a-select :options="state.scoreFormatList" v-model:value="query.scoreFormat" placeholder="请输入"></a-select>
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-form-item label="评价类型：">
                            <a-select
                                :options="state.evalTypeList"
                                v-model:value="query.evalTypeId"
                                placeholder="请输入"
                                :field-names="{ label: 'name', value: 'id' }"
                            ></a-select>
                        </a-form-item>
                    </YCol>
                    <YCol :span="5">
                        <a-form-item label="得分时间：">
                            <RangePicker v-model:startTime="query.scoreStartTime" v-model:endTime="query.scoreEndTime"></RangePicker>
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-button type="primary" @click="queryData">
                            <SearchOutlined />
                            查 询
                        </a-button>
                        <a-button @click="resetData">
                            <reload-outlined />
                            重 置
                        </a-button>
                    </YCol>
                </a-row>
            </a-form>
        </div>
        <div min-h-520 mb-20>
            <ETable
                :loading="page.loading"
                :dataSource="page.list"
                :total="page.total"
                @paginationChange="paginationChange"
                :current="query.pageNo"
                @change="handleTableChange"
            >
                <a-table-column title="学生姓名" data-index="personName" />
                <a-table-column title="所在班级" data-index="groupNameList" />
                <a-table-column title="得分形式" data-index="scoreFormat" :width="150">
                    <template #default="{ record }">
                        {{ scoreFormatName(record.scoreFormat) }}
                    </template>
                </a-table-column>
                <a-table-column title="评价类型" data-index="evalTypeId">
                    <template #default="{ record }">
                        {{ state.evalTypeLists.find(item => item.id === record.evalTypeId)?.name || '-' }}
                    </template>
                </a-table-column>
                <a-table-column title="活动名称" data-index="activityTitle">
                    <template #default="{ text }">
                        {{ text || '-' }}
                    </template>
                </a-table-column>
                <a-table-column title="得分" data-index="score" />
                <a-table-column title="得分时间" data-index="createTime" :sorter="true" />
            </ETable>
        </div>
    </div>
</template>
<script setup name="pointsRecord">
const allOption = { id: null, name: '全部' }

const orderType = {
    ascend: '1',
    descend: '2',
}

// *********************
// Hooks Function
// *********************

const state = ref({
    formState: {
        identity: 0,
    },
    evalTypeList: [], // 得分类型数组
    evalTypeLists: [],
    scoreFormatList: [
        {
            value: null,
            label: '全部',
        },
        {
            value: '1',
            label: '积分卡兑换',
        },
        {
            value: '2',
            label: '参与评价活动',
        },
        {
            value: '3',
            label: '德育机兑换',
        },
        {
            value: '4',
            label: '勋章奖励',
        },
        {
            value: '5',
            label: '积分退还',
        },
        {
            value: '6',
            label: '手动增减积分',
        },
    ],
})

const scoreFormatName = computed(() => {
    return i => {
        return state.value.scoreFormatList.find(item => item.value == i)?.label || '-'
    }
})

// *********************
// Default Function
// *********************

const { query, page, getList, reset, paginationChange } = useList('/cloud/evalScoreRecord/page', {
    ...state.value.formState,
    evalTypeId: null,
    scoreFormat: null,
})

// *********************
// Life Event Function
// *********************

getList()

// *********************
// Service Function
// *********************

const queryData = () => {
    getList()
}

const resetData = () => {
    reset({
        ...state.value.formState,
    })
}

// table排序切换
const handleTableChange = (pag, filters, sorter) => {
    query.order = orderType[sorter.order]
    getList()
}

const getEvalTypeList = () => {
    http.get('/cloud/evalType/listBySchool').then(res => {
        state.value.evalTypeList = [allOption, ...res.data]
        state.value.evalTypeLists = res.data
    })
}

getEvalTypeList()
</script>
<style lang="less" scoped></style>
