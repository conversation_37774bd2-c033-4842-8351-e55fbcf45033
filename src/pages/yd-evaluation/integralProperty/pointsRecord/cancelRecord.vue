<!-- 积分记录 -->
<template>
    <div p-20>
        <div mb-20>
            <a-form :model="query" autocomplete="off">
                <a-row :gutter="[25, 16]">
                    <YCol>
                        <a-form-item label="学生姓名：">
                            <a-input v-model:value="query.personName" placeholder="请输入" />
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-form-item label="所在班级：">
                            <a-input v-model:value="query.groupName" placeholder="请输入" />
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-form-item label="积分卡ID：">
                            <a-input v-model:value="query.scoreCardId" placeholder="请输入" />
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-form-item label="得分类型：">
                            <a-select
                                :options="state.evalTypeList"
                                v-model:value="query.evalTypeId"
                                placeholder="请输入"
                                :field-names="{ label: 'name', value: 'id' }"
                            ></a-select>
                        </a-form-item>
                    </YCol>
                    <YCol :span="5">
                        <a-form-item label="核销时间：">
                            <RangePicker v-model:startTime="query.writeOffStartTime" v-model:endTime="query.writeOffEndTime"></RangePicker>
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-button type="primary" @click="queryData">
                            <SearchOutlined />
                            查 询
                        </a-button>
                        <a-button @click="resetData">
                            <reload-outlined />
                            重 置
                        </a-button>
                    </YCol>
                </a-row>
            </a-form>
        </div>
        <div min-h-520 mb-20>
            <ETable
                :loading="page.loading"
                :dataSource="page.list"
                :total="page.total"
                @paginationChange="paginationChange"
                :current="query.pageNo"
                @change="handleTableChange"
            >
                <a-table-column title="学生姓名" data-index="personName" />
                <a-table-column title="所在班级" data-index="groupNameList" />
                <a-table-column title="得分类型" data-index="evalTypeId">
                    <template #default="{ record }">
                        {{ state.evalTypeList.find(item => item.id === record.evalTypeId)?.name || '-' }}
                    </template>
                </a-table-column>
                <a-table-column title="积分卡ID" data-index="scoreCardId" />
                <a-table-column title="得分" data-index="cardScore" />
                <a-table-column title="核销时间" data-index="createTime" :sorter="true" />
            </ETable>
        </div>
    </div>
</template>
<script setup name="cancelRecord">
const allOption = { id: null, name: '全部' }
const state = ref({
    formState: {
        identity: 0,
    },
    evalTypeList: [], // 得分类型数组
    scoreFormatList: [
        {
            value: null,
            label: '全部',
        },
        {
            value: '1',
            label: '积分卡兑换',
        },
        {
            value: '2',
            label: '参与评价活动',
        },
        {
            value: '3',
            label: '德育机兑换',
        },
        {
            value: '4',
            label: '勋章奖励',
        },
    ],
})

const { query, page, getList, reset, paginationChange } = useList('/cloud/evalScoreCard/off/page', {...state.value.formState, scoreFormat: null, evalTypeId: null})
getList()

const queryData = () => {
    getList()
}

const resetData = () => {
    reset({
        ...state.value.formState,
    })
}

const orderType = {
    ascend: '1',
    descend: '2',
}

// table排序切换
const handleTableChange = (pag, filters, sorter) => {
    query.order = orderType[sorter.order]
    getList()
}

const getEvalTypeList = () => {
    http.get('/cloud/evalType/listBySchool').then(res => {
        state.value.evalTypeList = [allOption, ...res.data]
    })
}

getEvalTypeList()
</script>
<style lang="less" scoped></style>
