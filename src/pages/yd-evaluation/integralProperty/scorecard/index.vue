<template>
    <cont-header />
    <div p-20>
        <div mb-20>
            <a-form :model="query" autocomplete="off">
                <a-row :gutter="[25, 16]">
                    <YCol>
                        <a-form-item label="积分卡ID：">
                            <a-input v-model:value.trim="query.scoreCardId" placeholder="请输入" />
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-form-item label="评价类型：">
                            <a-select
                                :options="state.evalTypeList"
                                v-model:value="query.evalTypeId"
                                placeholder="请输入"
                                :field-names="{ label: 'name', value: 'id' }"
                            ></a-select>
                        </a-form-item>
                    </YCol>
                    <YCol :span="5">
                        <a-form-item label="核销时间：">
                            <RangePicker v-model:startTime="query.writeOffStartTime" v-model:endTime="query.writeOffEndTime"></RangePicker>
                        </a-form-item>
                    </YCol>
                    <YCol :span="5">
                        <a-form-item label="创建时间：">
                            <RangePicker v-model:startTime="query.createStartTime" v-model:endTime="query.createEndTime"></RangePicker>
                        </a-form-item>
                    </YCol>
                    <YCol>
                        <a-button type="primary" @click="queryData">
                            <SearchOutlined />
                            查 询
                        </a-button>
                        <a-button @click="reset">
                            <reload-outlined />
                            重 置
                        </a-button>
                    </YCol>
                </a-row>
            </a-form>
        </div>
        <div flex grid-justify-end mb-24 mt-20>
            <a-button type="primary" @click="addScoreCard">
                <template #icon>
                    <PlusOutlined />
                </template>
                添加积分卡
            </a-button>
            <a-button @click="bulkImport">批量导入</a-button>
        </div>
        <div min-h-520 mb-20>
            <ETable
                rowKey="scoreCardId"
                :loading="page.loading"
                :dataSource="page.list"
                :total="page.total"
                @paginationChange="paginationChange"
                :row-selection="{
                    selectedRowKeys: selected,
                    onChange: onSelectChange,
                }"
                :current="query.pageNo"
                @change="handleTableChange"
            >
                <a-table-column title="积分卡ID" data-index="scoreCardId" />
                <a-table-column title="积分卡名称" data-index="scoreCardName" />
                <a-table-column title="关联评价类型" data-index="evalTypeName" />
                <a-table-column title="积分价值" data-index="cardScore" />
                <a-table-column title="核销时间" data-index="writeOffTime" :sorter="true" />
                <a-table-column title="创建人" data-index="createBy" />
                <a-table-column title="创建时间" data-index="createTime" :sorter="true" />
                <a-table-column title="操作" data-index="operate" :width="100">
                    <template #default="{ record }">
                        <a-button type="link" class="btn-link-color" @click="particularsCard(record)">详情</a-button>
                        <a-button v-if="record.writeOffStatus === 0" type="link" class="btn-link-color" @click="compileCard(record)">编辑</a-button>
                    </template>
                </a-table-column>
            </ETable>
        </div>
        <YImport
            v-model:show="state.batchOpen"
            @cancel="getList"
            templateSrc="/template/积分卡模版.xlsx"
            title="积分卡导入"
            :importType="7"
            uploadUrl="/cloud/common/import"
            :progressRequest="progressRequest"
            errorLogUrl="/cloud/common/export/importErrorLog"
        ></YImport>
    </div>

    <!-- 添加积分卡的抽屉的弹窗 -->
    <a-drawer
        :width="500"
        v-model:open="state.integralOpen"
        :keyboard="false"
        :destroyOnClose="true"
        :maskClosable="false"
        :title="cardTitle"
        @close="closeThing"
    >
        <div class="details">
            <a-form
                :model="integralFormState"
                autocomplete="off"
                layout="vertical"
                :rules="integralRules"
                :label-col="{ span: 8 }"
                :wrapper-col="{ span: 16 }"
                ref="integralFormRef"
            >
                <a-form-item :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }" name="medalIconUrl" label="积分卡卡面：">
                    <div flex flex-items-center>
                        <div w-320 h-180>
                            <uploadImage :disabled="state.drawerType === 'detail'" v-model:url="integralFormState.scoreCardIconUrl" :limit="2" />
                        </div>
                    </div>
                </a-form-item>
                <div class="tipImg">建议图片尺寸为320x180，支持图片格式为png/jpg/bmp/webp，大小不超过2M</div>

                <a-form-item label="积分卡ID：" name="scoreCardId" :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }">
                    <a-input
                        :disabled="state.drawerType === 'detail'"
                        v-model:value.trim="integralFormState.scoreCardId"
                        placeholder="请输入"
                    />
                </a-form-item>
                <a-form-item label="积分卡名称：" name="scoreCardName" :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }" mt-12>
                    <a-input
                        :disabled="state.drawerType === 'detail'"
                        v-model:value.trim="integralFormState.scoreCardName"
                        placeholder="请输入"
                    />
                </a-form-item>
                <a-form-item label="评价类型：" name="evalTypeId" :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }" mt-12>
                    <a-select
                        :disabled="state.drawerType === 'detail'"
                        :options="state.evalTypeList"
                        v-model:value="integralFormState.evalTypeId"
                        placeholder="请选择"
                        :field-names="{ label: 'name', value: 'id' }"
                    ></a-select>
                </a-form-item>
                <a-form-item label="积分价值：" name="cardScore" :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }" mt-12>
                    <a-input
                        :disabled="state.drawerType === 'detail'"
                        v-model:value.trim="integralFormState.cardScore"
                        placeholder="请输入"
                    />
                </a-form-item>
                <div mt-24 mb-24 v-if="state.drawerType === 'add'">
                    <a-button ghost type="primary" @click="pushTableItem">
                        <PlusOutlined />
                        添 加
                    </a-button>
                </div>
            </a-form>
        </div>
        <div v-if="state.drawerType === 'add'">
            <a-table :columns="columns" :pagination="false" :data-source="addtableList" size="middle">
                <template #bodyCell="{ column, record, index }">
                    <template v-if="column.dataIndex === 'evalTypeId'">
                        {{ state.evalTypeList.find(item => item.id === record.evalTypeId).name }}
                    </template>
                </template>
            </a-table>
        </div>
        <template #footer v-if="state.drawerType !== 'detail'">
            <div class="footer">
                <a-button @click="closeThing">取消</a-button>
                <a-button v-if="state.drawerType === 'add'" type="primary" @click="submitThing">确认</a-button>
                <a-button v-if="state.drawerType === 'edit'" type="primary" @click="editSubmitThing">编辑确认</a-button>
            </div>
        </template>
    </a-drawer>
</template>
<script setup name="scorecard">
import uploadImage from './uploadImage.vue'
let state = ref({
    drawerType: 'add',
    integralOpen: false,
    selectValue: 'title',
    sourceTypeList: [],
    libraryList: [],
    formState: {
        libraryId: null,
    },
    rankingInfo: {},
    evalTypeList: [],
    batchOpen: false,
})

const selected = ref([])

// 批量选择
const onSelectChange = (selectedRowKeys, selectedRow) => {
    selected.value = selectedRowKeys
}

// 积分卡抽屉的表单对象
let integralFormState = ref({})

// 添加积分的table list 数组
const addtableList = ref([])

// 表单实例对象
const integralFormRef = ref()

const cardTitle = computed(() => {
    const titleMap = {
        add: '添加积分卡',
        edit: '编辑积分卡',
        detail: '积分卡详情',
    }
    return titleMap[state.value.drawerType] || '积分卡'
})

// 表单校验
const integralRules = {
    scoreCardId: [
        {
            required: true,
            message: '请输入!',
        },
        // {
        //     min: 3,
        //     max: 5,
        //     message: 'Length should be 3 to 5',
        //     trigger: 'blur',
        // },
    ],
    evalTypeId: [
        {
            required: true,
            message: '请选择!',
        },
    ],
    scoreCardName: [
        {
            required: true,
            message: '请输入!',
        },
    ],
    cardScore: [
        {
            required: true,
            message: '请输入!',
        },
    ],
}

const { query, page, getList, reset, paginationChange } = useList('/cloud/evalScoreCard/pageEvalScoreCard', state.formState)
getList()
const columns = [
    { title: '积分卡ID', dataIndex: 'scoreCardId' },
    { title: '名称', dataIndex: 'scoreCardName' },
    { title: '评价类型', dataIndex: 'evalTypeId' },
    { title: '积分价值', dataIndex: 'cardScore' },
]

// 直接获取一个评价类型列表
const getEvalTypeList = () => {
    http.get('/cloud/evalType/listBySchool').then(res => {
        state.value.evalTypeList = res.data || []
    })
}

getEvalTypeList()

const orderType = {
    ascend: '1',
    descend: '2',
}

const fieldName = {
    createTime: '1',
    writeOffTime: '2',
}

// table排序切换
const handleTableChange = (pag, filters, sorter) => {
    query.order = orderType[sorter.order]

    query.field = fieldName[sorter.field]
    getList()
}

// 添加积分卡
const addScoreCard = () => {
    state.value.drawerType = 'add'
    state.value.integralOpen = true
}

// 积分卡详情
const particularsCard = data => {
    state.value.drawerType = 'detail'
    state.value.integralOpen = true
    integralFormState.value = JSON.parse(JSON.stringify(data))
}

// 编辑积分卡
const compileCard = data => {
    state.value.drawerType = 'edit'
    integralFormState.value = JSON.parse(JSON.stringify(data))
    console.log('integralFormState.value', integralFormState.value)
    state.value.integralOpen = true
}

// 批量导入
const bulkImport = () => {
    state.value.batchOpen = true
}

// 请求进度条数据
async function progressRequest(importId) {
    const { data } = await http.post(`/cloud/common/import/progress`, { importId })
    return data
}

// 查询
function queryData() {
    getList()
}

// 表单校验完 把表单对象push 进table
const pushTableItem = () => {
    // 最多只能 push 100条数据
    if (addtableList.value.length >= 100) {
        YMessage.info('一次最多添加100条!')
    } else {
        integralFormRef.value
            .validate()
            .then(() => {
                if(addtableList.value.some(item=>item.scoreCardId===integralFormState.value.scoreCardId) ){
                    YMessage.info('积分卡ID已存在!')
                    return
                }
                addtableList.value.push({ ...integralFormState.value })
                YMessage.success('积分卡已添加!')
                integralFormState.value.scoreCardId = ''
                integralFormState.value.scoreCardIconUrl = ''
            })
            .catch(error => {
                console.log('error', error)
            })
    }
}

// 抽屉的确认提交按钮
const submitThing = () => {
    // 如果table没数据就不进行操作
    if (!addtableList.value.length) return
    // 发送添加积分卡的请求
    http.post('/cloud/evalScoreCard/bachCreate', addtableList.value)
        .then(res => {
            // 清除表单
            integralFormRef.value.resetFields()
            YMessage.success(res.message)
            addtableList.value = []
            state.value.integralOpen = false
            getList()
        })
        .catch(error => {
            YMessage.error('操作失败')
        })
}

// 编辑确认
const editSubmitThing = () => {
    http.post('/cloud/evalScoreCard/updateEvalScoreCard', integralFormState.value)
        .then(res => {
            // 清除表单
            YMessage.success(res.message)
            closeThing()
            getList()
        })
        .catch(error => {
            YMessage.error('操作失败')
        })
}

const closeThing = () => {
    // 清除表单
    integralFormRef.value.resetFields()
    addtableList.value = []
    integralFormState.value = {}
    state.value.integralOpen = false
}
</script>
<style lang="less" scoped>
.reader_info {
    padding: 20px;
    display: flex;
    flex-direction: column;
    min-height: 134px;
    background: #f5f5f5;
    border-radius: 4px;
    .title {
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        line-height: 20px;
        margin-bottom: 16px;
    }
    .item {
        display: flex;
        justify-content: space-around;
        flex-wrap: wrap;
    }
}

.tipImg {
    font-weight: 400;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.25);
    padding-top: 12px;
    padding-bottom: 12px;
}
</style>
