const config = {
    'yd-rfid': {
        title: 'RFID图书管理系统',
    },
    shRFID: {
        title: '社会图书馆-RFID',
    },
    'yd-permission': {
        title: '壹德权限管理系统',
    },
    'yd-library': {
        title: '图书馆馆务系统',
        script: [],
        style: ["@import url('//at.alicdn.com/t/c/font_4223717_j89ukkxsqfk.css')"],
    },
    shLibrary: {
        title: '社会图书馆-馆务系统',
        script: [],
        style: ["@import url('//at.alicdn.com/t/c/font_4223717_j89ukkxsqfk.css')"],
    },
    'yd-dormitory': {
        title: '智慧宿舍系统',
        script: [],
        style: [],
    },
    'yd-security': {
        title: '校园安防系统',
        script: [],
        style: [],
    },
    'yd-evaluation': {
        title: '评价系统',
        script: [],
        style: [],
    },
    'yd-xyf': {
        title: '校易付系统',
        script: [],
        style: ["@import url('//at.alicdn.com/t/c/font_4606441_nyi7631onp.css')"],
    },
    'yd-paySystem': {
        title: '缴费系统',
        script: [],
        style: ["@import url('//at.alicdn.com/t/c/font_4606441_nyi7631onp.css')"],
    },
}
const code = useCode()
useDiy(config[code])
