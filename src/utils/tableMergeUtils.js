/**
 * 表格合并工具库
 * 提供完整的表格行合并解决方案，支持多层级、多字段、条件合并等复杂场景
 * 
 * <AUTHOR> Agent
 * @version 1.0.0
 * @date 2024-08-02
 */

/**
 * 基础行合并计算器
 * @param {Array} dataSource - 数据源
 * @param {number} currentIndex - 当前行索引
 * @param {string|Function} compareField - 比较字段名或比较函数
 * @param {Object} options - 配置选项
 * @returns {number} 合并行数，0表示隐藏
 */
export function calculateRowSpan(dataSource, currentIndex, compareField, options = {}) {
  const {
    direction = 'down', // 'down' | 'up' | 'both'
    maxSpan = Infinity,
    minSpan = 1,
    condition = null // 额外的合并条件函数
  } = options

  if (!dataSource || currentIndex < 0 || currentIndex >= dataSource.length) {
    return 0
  }

  const currentRecord = dataSource[currentIndex]
  const compareValue = typeof compareField === 'function' 
    ? compareField(currentRecord, currentIndex)
    : currentRecord[compareField]

  // 检查是否满足合并条件
  if (condition && !condition(currentRecord, currentIndex)) {
    return 1
  }

  let rowSpan = 1
  let startIndex = currentIndex
  let endIndex = currentIndex

  // 向上查找相同值
  if (direction === 'up' || direction === 'both') {
    for (let i = currentIndex - 1; i >= 0; i--) {
      const record = dataSource[i]
      const value = typeof compareField === 'function' 
        ? compareField(record, i)
        : record[compareField]
      
      if (isEqual(value, compareValue) && (!condition || condition(record, i))) {
        startIndex = i
        rowSpan++
      } else {
        break
      }
    }
  }

  // 向下查找相同值
  if (direction === 'down' || direction === 'both') {
    for (let i = currentIndex + 1; i < dataSource.length; i++) {
      const record = dataSource[i]
      const value = typeof compareField === 'function' 
        ? compareField(record, i)
        : record[compareField]
      
      if (isEqual(value, compareValue) && (!condition || condition(record, i))) {
        endIndex = i
        if (direction === 'down') rowSpan++
      } else {
        break
      }
    }
  }

  // 应用最大最小限制
  rowSpan = Math.min(Math.max(rowSpan, minSpan), maxSpan)

  // 只有第一行显示合并，其他行隐藏
  if (currentIndex === startIndex) {
    return rowSpan
  } else {
    return 0
  }
}

/**
 * 多字段组合合并计算器
 * @param {Array} dataSource - 数据源
 * @param {number} currentIndex - 当前行索引
 * @param {Array} compareFields - 比较字段数组
 * @param {Object} options - 配置选项
 * @returns {number} 合并行数
 */
export function calculateMultiFieldRowSpan(dataSource, currentIndex, compareFields, options = {}) {
  const {
    operator = 'AND', // 'AND' | 'OR'
    ...restOptions
  } = options

  const combinedCompareFunction = (record, index) => {
    const values = compareFields.map(field => {
      return typeof field === 'function' ? field(record, index) : record[field]
    })
    
    if (operator === 'AND') {
      return values.join('|') // 所有字段都相同才合并
    } else {
      return values.some(v => v !== null && v !== undefined) ? values[0] : null // 任一字段相同就合并
    }
  }

  return calculateRowSpan(dataSource, currentIndex, combinedCompareFunction, restOptions)
}

/**
 * 层级合并计算器（支持多层级数据）
 * @param {Array} dataSource - 数据源
 * @param {number} currentIndex - 当前行索引
 * @param {Array} levelFields - 层级字段配置
 * @param {number} targetLevel - 目标合并层级
 * @param {Object} options - 配置选项
 * @returns {number} 合并行数
 */
export function calculateHierarchyRowSpan(dataSource, currentIndex, levelFields, targetLevel, options = {}) {
  if (targetLevel >= levelFields.length) {
    return 1
  }

  const targetField = levelFields[targetLevel]
  const compareFields = levelFields.slice(0, targetLevel + 1)

  return calculateMultiFieldRowSpan(dataSource, currentIndex, compareFields, {
    ...options,
    operator: 'AND'
  })
}

/**
 * 条件合并计算器
 * @param {Array} dataSource - 数据源
 * @param {number} currentIndex - 当前行索引
 * @param {string|Function} compareField - 比较字段
 * @param {Function} conditionFn - 条件函数
 * @param {Object} options - 配置选项
 * @returns {number} 合并行数
 */
export function calculateConditionalRowSpan(dataSource, currentIndex, compareField, conditionFn, options = {}) {
  return calculateRowSpan(dataSource, currentIndex, compareField, {
    ...options,
    condition: conditionFn
  })
}

/**
 * 分组合并计算器
 * @param {Array} dataSource - 数据源
 * @param {number} currentIndex - 当前行索引
 * @param {string|Function} groupField - 分组字段
 * @param {string|Function} mergeField - 合并字段
 * @param {Object} options - 配置选项
 * @returns {number} 合并行数
 */
export function calculateGroupRowSpan(dataSource, currentIndex, groupField, mergeField, options = {}) {
  const currentRecord = dataSource[currentIndex]
  const groupValue = typeof groupField === 'function' 
    ? groupField(currentRecord, currentIndex)
    : currentRecord[groupField]

  // 只在同一组内进行合并
  const groupCondition = (record, index) => {
    const recordGroupValue = typeof groupField === 'function' 
      ? groupField(record, index)
      : record[groupField]
    return isEqual(recordGroupValue, groupValue)
  }

  return calculateRowSpan(dataSource, currentIndex, mergeField, {
    ...options,
    condition: groupCondition
  })
}

/**
 * 智能合并计算器（自动检测最佳合并策略）
 * @param {Array} dataSource - 数据源
 * @param {number} currentIndex - 当前行索引
 * @param {string|Function|Array} compareField - 比较字段
 * @param {Object} options - 配置选项
 * @returns {number} 合并行数
 */
export function calculateSmartRowSpan(dataSource, currentIndex, compareField, options = {}) {
  // 数组类型 - 多字段合并
  if (Array.isArray(compareField)) {
    return calculateMultiFieldRowSpan(dataSource, currentIndex, compareField, options)
  }
  
  // 函数类型 - 自定义比较
  if (typeof compareField === 'function') {
    return calculateRowSpan(dataSource, currentIndex, compareField, options)
  }
  
  // 字符串类型 - 单字段合并
  return calculateRowSpan(dataSource, currentIndex, compareField, options)
}

/**
 * 批量生成合并配置
 * @param {Array} dataSource - 数据源
 * @param {Array} mergeConfigs - 合并配置数组
 * @returns {Object} 合并结果映射
 */
export function generateMergeConfigs(dataSource, mergeConfigs) {
  const mergeResults = new Map()

  mergeConfigs.forEach(config => {
    const {
      field,
      type = 'basic', // 'basic' | 'multi' | 'hierarchy' | 'conditional' | 'group' | 'smart'
      options = {}
    } = config

    dataSource.forEach((record, index) => {
      const key = `${field}-${index}`
      let rowSpan = 1

      switch (type) {
        case 'basic':
          rowSpan = calculateRowSpan(dataSource, index, field, options)
          break
        case 'multi':
          rowSpan = calculateMultiFieldRowSpan(dataSource, index, field, options)
          break
        case 'hierarchy':
          rowSpan = calculateHierarchyRowSpan(dataSource, index, field, options.targetLevel || 0, options)
          break
        case 'conditional':
          rowSpan = calculateConditionalRowSpan(dataSource, index, field, options.condition, options)
          break
        case 'group':
          rowSpan = calculateGroupRowSpan(dataSource, index, options.groupField, field, options)
          break
        case 'smart':
          rowSpan = calculateSmartRowSpan(dataSource, index, field, options)
          break
        default:
          rowSpan = calculateRowSpan(dataSource, index, field, options)
      }

      mergeResults.set(key, rowSpan)
    })
  })

  return mergeResults
}

/**
 * 创建表格列合并配置
 * @param {string} dataIndex - 数据字段名
 * @param {string|Function|Array} compareField - 比较字段
 * @param {Object} options - 配置选项
 * @returns {Function} customCell 函数
 */
export function createMergeColumn(dataIndex, compareField, options = {}) {
  const {
    type = 'smart',
    cacheResults = true,
    ...restOptions
  } = options

  let cache = new Map()

  return function customCell(record, rowIndex, column) {
    // 获取数据源（需要从外部传入或通过其他方式获取）
    const dataSource = this?.dataSource || options.dataSource
    
    if (!dataSource) {
      console.warn('TableMergeUtils: dataSource not found, please provide dataSource in options')
      return { rowSpan: 1 }
    }

    // 缓存检查
    const cacheKey = `${dataIndex}-${rowIndex}`
    if (cacheResults && cache.has(cacheKey)) {
      return { rowSpan: cache.get(cacheKey) }
    }

    let rowSpan = 1

    switch (type) {
      case 'basic':
        rowSpan = calculateRowSpan(dataSource, rowIndex, compareField || dataIndex, restOptions)
        break
      case 'multi':
        rowSpan = calculateMultiFieldRowSpan(dataSource, rowIndex, compareField, restOptions)
        break
      case 'hierarchy':
        rowSpan = calculateHierarchyRowSpan(dataSource, rowIndex, compareField, restOptions.targetLevel || 0, restOptions)
        break
      case 'conditional':
        rowSpan = calculateConditionalRowSpan(dataSource, rowIndex, compareField || dataIndex, restOptions.condition, restOptions)
        break
      case 'group':
        rowSpan = calculateGroupRowSpan(dataSource, rowIndex, restOptions.groupField, compareField || dataIndex, restOptions)
        break
      case 'smart':
      default:
        rowSpan = calculateSmartRowSpan(dataSource, rowIndex, compareField || dataIndex, restOptions)
    }

    // 缓存结果
    if (cacheResults) {
      cache.set(cacheKey, rowSpan)
    }

    return { rowSpan }
  }
}

/**
 * 清除合并缓存
 * @param {Function} customCellFn - customCell 函数
 */
export function clearMergeCache(customCellFn) {
  if (customCellFn && customCellFn.cache) {
    customCellFn.cache.clear()
  }
}

/**
 * 值比较工具函数
 * @param {any} value1 - 值1
 * @param {any} value2 - 值2
 * @returns {boolean} 是否相等
 */
function isEqual(value1, value2) {
  if (value1 === value2) return true
  if (value1 == null || value2 == null) return value1 === value2
  if (typeof value1 !== typeof value2) return false
  
  if (Array.isArray(value1) && Array.isArray(value2)) {
    if (value1.length !== value2.length) return false
    return value1.every((item, index) => isEqual(item, value2[index]))
  }
  
  if (typeof value1 === 'object' && typeof value2 === 'object') {
    const keys1 = Object.keys(value1)
    const keys2 = Object.keys(value2)
    if (keys1.length !== keys2.length) return false
    return keys1.every(key => isEqual(value1[key], value2[key]))
  }
  
  return false
}

/**
 * 数据预处理工具
 */
export const DataProcessor = {
  /**
   * 扁平化嵌套数据
   * @param {Array} nestedData - 嵌套数据
   * @param {Object} config - 配置
   * @returns {Array} 扁平化数据
   */
  flattenNestedData(nestedData, config) {
    const {
      levelConfigs = [], // 层级配置
      keyMapping = {}, // 字段映射
      preserveOriginal = true // 是否保留原始数据引用
    } = config

    const flatData = []

    const traverse = (data, level = 0, parentPath = []) => {
      if (level >= levelConfigs.length) {
        // 叶子节点，创建扁平化记录
        const flatRecord = {}
        
        // 添加层级信息
        parentPath.forEach((parent, index) => {
          const levelConfig = levelConfigs[index]
          if (levelConfig.idField) {
            flatRecord[levelConfig.idField] = parent[levelConfig.sourceIdField || 'id']
          }
          if (levelConfig.nameField) {
            flatRecord[levelConfig.nameField] = parent[levelConfig.sourceNameField || 'name']
          }
        })

        // 添加当前层级数据
        Object.keys(data).forEach(key => {
          const mappedKey = keyMapping[key] || key
          flatRecord[mappedKey] = data[key]
        })

        // 保留原始数据引用
        if (preserveOriginal) {
          flatRecord._originalData = parentPath.concat(data)
        }

        flatData.push(flatRecord)
        return
      }

      const levelConfig = levelConfigs[level]
      const children = data[levelConfig.childrenField] || []

      if (Array.isArray(children) && children.length > 0) {
        children.forEach(child => {
          traverse(child, level + 1, parentPath.concat(data))
        })
      } else {
        // 没有子级，直接处理当前数据
        traverse(data, levelConfigs.length, parentPath)
      }
    }

    if (Array.isArray(nestedData)) {
      nestedData.forEach(item => traverse(item))
    } else {
      traverse(nestedData)
    }

    return flatData
  },

  /**
   * 数据分组
   * @param {Array} data - 数据
   * @param {string|Function} groupBy - 分组字段或函数
   * @returns {Object} 分组结果
   */
  groupData(data, groupBy) {
    const groups = {}
    
    data.forEach((item, index) => {
      const key = typeof groupBy === 'function' ? groupBy(item, index) : item[groupBy]
      const groupKey = key != null ? String(key) : 'null'
      
      if (!groups[groupKey]) {
        groups[groupKey] = []
      }
      groups[groupKey].push({ item, index })
    })
    
    return groups
  },

  /**
   * 数据排序
   * @param {Array} data - 数据
   * @param {Array} sortConfigs - 排序配置
   * @returns {Array} 排序后的数据
   */
  sortData(data, sortConfigs) {
    return [...data].sort((a, b) => {
      for (const config of sortConfigs) {
        const { field, order = 'asc', type = 'auto' } = config
        
        let valueA = typeof field === 'function' ? field(a) : a[field]
        let valueB = typeof field === 'function' ? field(b) : b[field]
        
        // 类型转换
        if (type === 'number') {
          valueA = Number(valueA) || 0
          valueB = Number(valueB) || 0
        } else if (type === 'string') {
          valueA = String(valueA || '')
          valueB = String(valueB || '')
        }
        
        let result = 0
        if (valueA < valueB) result = -1
        else if (valueA > valueB) result = 1
        
        if (order === 'desc') result = -result
        
        if (result !== 0) return result
      }
      return 0
    })
  }
}

/**
 * 性能监控工具
 */
export const PerformanceMonitor = {
  timers: new Map(),

  /**
   * 开始计时
   * @param {string} label - 标签
   */
  start(label) {
    this.timers.set(label, performance.now())
  },

  /**
   * 结束计时
   * @param {string} label - 标签
   * @returns {number} 耗时（毫秒）
   */
  end(label) {
    const startTime = this.timers.get(label)
    if (startTime) {
      const duration = performance.now() - startTime
      this.timers.delete(label)
      return duration
    }
    return 0
  },

  /**
   * 测量函数执行时间
   * @param {Function} fn - 函数
   * @param {string} label - 标签
   * @returns {any} 函数返回值
   */
  measure(fn, label = 'anonymous') {
    this.start(label)
    const result = fn()
    const duration = this.end(label)
    console.log(`${label} 执行时间: ${duration.toFixed(2)}ms`)
    return result
  }
}
