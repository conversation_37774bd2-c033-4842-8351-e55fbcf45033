import ISBN from 'isbn3'
/**
 * @desc  函数防抖
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(method, wait, immediate) {
    let timeout
    return function (...args) {
        let context = this
        if (timeout) {
            clearTimeout(timeout)
        }
        // 立即执行需要两个条件，一是immediate为true，二是timeout未被赋值或被置为null
        if (immediate) {
            /**
             * 如果定时器不存在，则立即执行，并设置一个定时器，wait毫秒后将定时器置为null
             * 这样确保立即执行后wait毫秒内不会被再次触发
             */
            let callNow = !timeout
            timeout = setTimeout(() => {
                timeout = null
            }, wait)
            if (callNow) {
                method.apply(context, args)
            }
        } else {
            // 如果immediate为false，则函数wait毫秒后执行
            timeout = setTimeout(() => {
                /**
                 * args是一个类数组对象，所以使用fn.apply
                 * 也可写作method.call(context, ...args)
                 */
                method.apply(context, args)
            }, wait)
        }
    }
}

/**
 *
 * @param {HTMLElement} el
 * @param {Function} cb
 * @return {ResizeObserver}
 */
export function useResize(el, cb) {
    const observer = new ResizeObserver(entries => {
        cb(entries[0].contentRect)
    })
    observer.observe(el)
    return observer
}
export function deepClone(obj) {
    return JSON.parse(JSON.stringify(obj))
}

export function formatISBN(isbn) {
    return ISBN.hyphenate(isbn)
}

export function getUrlParams() {
    const url = window.location.href
    let urlStr = url.split('?')[1]
    if (!urlStr) return {}
    let obj = {}
    let paramsArr = urlStr.split('&')
    for (let i = 0, len = paramsArr.length; i < len; i++) {
        let arr = paramsArr[i].split('=')
        obj[arr[0]] = arr[1]
    }
    return obj
}
export function generateRandomId() {
    let timestampPart = Date.now().toString(36) //
    var randomPart = parseInt(Math.random() * Math.random() * 9999999).toString(36)
    timestampPart = timestampPart + randomPart
    return timestampPart
}
