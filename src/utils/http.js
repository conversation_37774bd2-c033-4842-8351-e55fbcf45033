import { message } from 'ant-design-vue'
import axios from 'axios'
const store = useStore()
const yStore = useTypeStore()
const code = useCode()
axios.defaults.baseURL = import.meta.env.VITE_BASE_API
axios.defaults.timeout = 100000
let dropLineMessage = true

/** 参数是否一致 */
let postStr = {}
const isRepeat = (url, data) => {
    let flag = true
    const key = url + JSON.stringify(data)
    if (postStr[key]) {
        flag = false
    } else {
        flag = true
        postStr[key] = true
    }
    return flag
}

if (code === 'yd-library' || code === 'shLibrary') {
    axios.defaults.headers['X-Yide-Library-Id'] = yStore.LibId
} else if (code === 'yd-rfid' || code === 'shRFID') {
    const rfidStore = useRfidStore()
    axios.defaults.headers['X-Yide-Lib-Branch-Id'] = rfidStore.LibId
} else if (code === 'yd-permission') {
}
const getToken = () => {
    if (store.token) {
        return store.token.includes('Bearer') ? store.token : `Bearer ${store.token}`
    } else {
        return toLogin()
    }
}
// 请求拦截器
axios.interceptors.request.use(
    config => {
        config.headers.Authorization = getToken()
        config.headers.platform = yStore.platform
        if (code === 'yd-library' || code == 'shLibrary') {
            config.headers['X-Yide-Library-Id'] = yStore.LibId
        }
        return config
    },
    error => {
        return Promise.reject(error)
    },
)

const toLoginCode = [
    1001001001, 1001001002, 1001001003, 1001001005, 1001001006, 1001001007, 1002002002, 1002002009, 1002002012, 1002002013, 1002003014, 401,
    450, 1002003012, 1002003017,
]
// 响应拦截器
axios.interceptors.response.use(
    response => {
        let { data, config, status } = response
        if (config.method == 'post') {
            const key = config.url + config.data
            if (postStr[key]) {
                postStr[key] = null
            }
        }
        if (config?.responseType == 'arraybuffer') {
            return data
        }
        if (status == 200 && data.code == 0) {
            return data
        } else if (status == 200 && data.code == 1002002014) {
            if (window.location.href.indexOf('/#/login') || window.location.href.indexOf('/#/xyfLogin')) {
                // 本身在登录页(不跳转到没有权限页面)
                return data
            } else {
                window.location.replace('/#/no-auth')
            }
        } else if (status == 200 && data.code == 1002002024) {
            // 账号在别的地方登录
            store.token = null
            const loginPlatform = store.loginPlatform
            window.localStorage.clear()
            dropLineMessage && message.error(data?.message || '该账号在别处登录,请重新登录')
            dropLineMessage = false
            setTimeout(() => {
                dropLineMessage = true
                if (loginPlatform === 'cloud') {
                    // 跳转到云平台登录页
                    window.location.replace(import.meta.env.VITE_BASE_API_CLOUD + '/#/login')
                } else {
                    const code = useCode()
                    if (code === 'yd-xyf') {
                        window.location.replace('/#/xyfLogin')
                    } else if (code === 'shRFID') {
                        window.location.replace('/#/shRfidLogin')
                    } else if (code === 'shLibrary') {
                        window.location.replace('/#/shLibraryLogin')
                    } else {
                        // 跳转到当前系统登录页
                        window.location.replace('/#/login')
                    }
                }
            }, 2000)
            return data
        } else if (toLoginCode.includes(data.code)) {
            // token过期或丢失后 判断是否是科大讯飞进入  跳他们的网址
            const source = localStorage.getItem('source')
            if (source === 'IFlytek') {
                window.location.href = 'https://szjz.jyyun.com'
                return
            }
            const code = useCode()
            if (code === 'yd-permission') {
                console.log('token过期或丢失')
            } else {
                store.token = null
                const loginPlatform = store.loginPlatform
                window.localStorage.clear()
                setTimeout(() => {
                    if (loginPlatform === 'cloud') {
                        // 跳转到云平台登录页
                        window.location.replace(import.meta.env.VITE_BASE_API_CLOUD + '/#/login')
                    } else {
                        const code = useCode()
                        if (code === 'yd-xyf') {
                            yStore?.clearUser()
                            YMessage.error(data.message)
                            window.location.replace('/#/xyfLogin')
                        } else if (code === 'shRFID') {
                            yStore?.clearUser()
                            YMessage.error(data.message)
                            window.location.replace('/#/shRfidLogin')
                        } else if (code === 'shLibrary') {
                            yStore?.clearUser()
                            YMessage.error(data.message)
                            window.location.replace('/#/shLibraryLogin')
                        } else {
                            // 跳转到当前系统登录页
                            window.location.replace('/#/login')
                        }
                    }
                }, 100)
                return data
            }
        } else {
            message.error(data?.message || '未知异常')

            return Promise.reject(data)
        }
    },
    error => {
        const { config } = error
        if (config?.method == 'post') {
            // 计算当前URL传递的参数
            const key = config.url + config.data
            if (postStr[key]) {
                postStr[key] = null
            }
        }

        if (error.message.indexOf('timeout') != -1) {
            message.error('请求超时！')
        }
        return Promise.reject(error)
    },
)
const get = (url, data, responseType = 'json') => {
    return axios({
        method: 'get',
        url: url,
        params: data,
        responseType,
    })
}
const post = (url, data, params, headers = { 'Content-Type': 'application/json' }) => {
    const flag = isRepeat(url, data)
    console.log('flag: ', flag)
    if (flag) {
        return axios({
            method: 'post',
            url: url,
            headers,
            data,
            params,
        })
    } else {
        return Promise.reject()
    }
}

const download = (url, data, name, fn, type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') => {
    axios({
        url: url,
        method: 'post',
        data,
        responseType: 'arraybuffer',
    }).then(blob => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type,
            }),
        )
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', `${name}`)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        fn && fn()
        setTimeout(() => window.URL.revokeObjectURL(url), 1000)
    })
}
const form = (url, data) => {
    let fd = new FormData()
    for (let key in data) {
        fd.append(key, data[key])
    }
    return axios({
        method: 'post',
        headers: {
            'Content-Type': 'multipart/form-data',
        },
        url: url,
        data: fd,
    })
}

const put = (url, data) => {
    return axios({
        method: 'put',
        url: url,
        data,
    })
}

export default { get, post, download, form, put }
export { axios }
