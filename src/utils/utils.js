const mainStore = useStore()

/** 复制链接 */
export function copyLink(text) {
    const textarea = document.createElement('textarea') // 构建textarea
    textarea.value = text // 设置内容
    document.body.appendChild(textarea) // 添加临时实例
    textarea.select() // 选择实例内容
    document.execCommand('Copy') // 执行复制
    document.body.removeChild(textarea) // 删除临时实例
}

/** 前往云平台 */
export function toCouldPlatform() {
    const token = encodeURI(mainStore.token)
    window.open(`${import.meta.env.VITE_BASE_API_CLOUD}/#/son?token=${token}`)
}

/**
 * 字符长度
 * @param value input框的值
 * @param maxLength 需求最大限制的字符
 * @param minLength 需求最小限制的字符
 * @returns 克隆后的对象
 */
export function checkField(value, minLength, maxLength) {
    const newvalue = value.replace(/[^\x00-\xff]/g, '**')
    const length = newvalue.length
    // 当填写的字节数小于设置的字节数
    const min = length * 1 < minLength * 1
    const max = length * 1 > maxLength * 1
    if (min || max) {
        return
    }
    const limitDate = newvalue.substring(0, maxLength)
    let count = 0
    let limitvalue = ''
    for (let i = 0; i < limitDate.length; i++) {
        const flat = limitDate.substring(i, 1)
        if (flat == '*') count++
    }
    let size = 0
    const istar = newvalue.substring(maxLength * 1 - 1, 1) // 校验点是否为“×”
    // if 基点是×; 判断在基点内有×为偶数还是奇数
    if (count % 2 === 0) {
        // 当为偶数时
        size = count / 2 + (maxLength * 1 - count)
        limitvalue = value.substring(0, size)
    } else {
        // 当为奇数时
        size = (count - 1) / 2 + (maxLength * 1 - count)
        limitvalue = value.substring(0, size)
    }
    return limitvalue
}
/**
 * 手机号码
 * @param val 当前值字符串
 * @returns 返回 true: 手机号码正确
 */
export function verifyPhone(val) {
    // false: 手机号码不正确
    // if (!/^((12[0-9])|(13[0-9])|(14[5])|(15([0-3]|[5-9]))|(18[0,5-9]))\d{8}$/.test(val)) return false;
    if (!/^[1][3-9][0-9]{9}$/.test(val)) return false
    // true: 手机号码正确
    else return true
}

/**
 * 邮箱
 * @param val 当前值字符串
 * @returns 返回 true: 邮箱正确
 */
export function verifyEmail(val) {
    // false: 邮箱不正确 // true: 邮箱正确
    const reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/
    return reg.test(val)
}

/**
 * 去掉中文及空格
 * @param val 当前值字符串
 * @returns 返回处理后的字符串
 */
export function verifyCnAndSpace(val) {
    // 匹配中文与空格
    let v = val.replace(/[\u4e00-\u9fa5\s]+/g, '')
    // 匹配空格
    v = v.replace(/(^\s*)|(\s*$)/g, '')
    // 特殊字符
    v = v.replace(/[~'！!@#￥$%^&*()-+_=:]/g, '')
    // 返回结果
    return v
}

// 密码正则特殊字符
export const regularSpecialCharacters =
    /^(?=.*[A-Za-z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{}|;':",.\/<>?])[A-Za-z\d!@#$%^&*()_+\-=\[\]{}|;':",.\/<>?]{8,20}$/
