# 表格合并工具库快速入门指南

## 🚀 5分钟快速上手

### 1. 导入工具库

```javascript
import { createMergeColumn } from '@/utils/tableMergeUtils.js'
```

### 2. 基础使用

```javascript
// 在 Ant Design Vue 表格中使用
const columns = [
  {
    title: '类别',
    dataIndex: 'category',
    customCell: createMergeColumn('category', 'category', {
      type: 'basic',
      dataSource: tableData
    })
  },
  {
    title: '类型',
    dataIndex: 'type'
  }
]
```

### 3. 完整示例

```vue
<template>
  <a-table :dataSource="tableData" :columns="columns" />
</template>

<script setup>
import { createMergeColumn } from '@/utils/tableMergeUtils.js'

const tableData = [
  { id: 1, category: 'A', type: 'X' },
  { id: 2, category: 'A', type: 'Y' },
  { id: 3, category: 'B', type: 'X' }
]

const columns = [
  {
    title: '类别',
    dataIndex: 'category',
    customCell: createMergeColumn('category', 'category', {
      dataSource: tableData
    })
  },
  { title: '类型', dataIndex: 'type' }
]
</script>
```

## 📋 常用场景

### 场景1：简单字段合并

```javascript
// 合并相同类别的行
const categoryColumn = {
  title: '类别',
  dataIndex: 'category',
  customCell: createMergeColumn('category', 'category', {
    dataSource: tableData
  })
}
```

### 场景2：多字段组合合并

```javascript
// 同时满足类别和状态相同才合并
const multiFieldColumn = {
  title: '类别',
  dataIndex: 'category',
  customCell: createMergeColumn('category', ['category', 'status'], {
    type: 'multi',
    operator: 'AND',
    dataSource: tableData
  })
}
```

### 场景3：层级数据合并

```javascript
// 三层数据：一级指标 → 二级指标 → 评分标准
import { DataProcessor } from '@/utils/tableMergeUtils.js'

// 1. 扁平化嵌套数据
const flatData = DataProcessor.flattenNestedData(nestedData, {
  levelConfigs: [
    { childrenField: 'children', idField: 'level1Id', nameField: 'level1Name' },
    { childrenField: 'children', idField: 'level2Id', nameField: 'level2Name' },
    { idField: 'level3Id', nameField: 'level3Name' }
  ]
})

// 2. 配置层级合并
const levelFields = ['level1Id', 'level2Id', 'level3Id']

const columns = [
  {
    title: '一级指标',
    dataIndex: 'level1Name',
    customCell: createMergeColumn('level1Name', levelFields, {
      type: 'hierarchy',
      targetLevel: 0,
      dataSource: flatData
    })
  },
  {
    title: '二级指标',
    dataIndex: 'level2Name',
    customCell: createMergeColumn('level2Name', levelFields, {
      type: 'hierarchy',
      targetLevel: 1,
      dataSource: flatData
    })
  }
]
```

### 场景4：条件合并

```javascript
// 只合并状态为激活的记录
const conditionalColumn = {
  title: '类别',
  dataIndex: 'category',
  customCell: createMergeColumn('category', 'category', {
    type: 'conditional',
    condition: (record) => record.status === 'active',
    dataSource: tableData
  })
}
```

### 场景5：分组内合并

```javascript
// 在同一部门内合并相同职位
const groupColumn = {
  title: '职位',
  dataIndex: 'position',
  customCell: createMergeColumn('position', 'position', {
    type: 'group',
    groupField: 'department',
    dataSource: tableData
  })
}
```

## 🎯 合并类型说明

| 类型 | 说明 | 使用场景 |
|------|------|----------|
| `basic` | 基础单字段合并 | 简单的相同值合并 |
| `multi` | 多字段组合合并 | 需要多个条件同时满足 |
| `hierarchy` | 层级合并 | 多层级嵌套数据 |
| `conditional` | 条件合并 | 需要满足特定条件 |
| `group` | 分组合并 | 在指定分组内合并 |
| `smart` | 智能合并（默认） | 自动检测最佳策略 |

## ⚙️ 配置选项

### 基础配置

```javascript
{
  type: 'smart',           // 合并类型
  dataSource: [],          // 数据源（必需）
  cacheResults: true,      // 是否缓存结果
  maxSpan: Infinity,       // 最大合并行数
  minSpan: 1,             // 最小合并行数
  direction: 'down'        // 合并方向：'down' | 'up' | 'both'
}
```

### 多字段配置

```javascript
{
  type: 'multi',
  operator: 'AND',         // 'AND' | 'OR'
  dataSource: tableData
}
```

### 层级配置

```javascript
{
  type: 'hierarchy',
  targetLevel: 0,          // 目标层级（0开始）
  dataSource: flatData
}
```

### 条件配置

```javascript
{
  type: 'conditional',
  condition: (record, index) => {
    return record.status === 'active'
  },
  dataSource: tableData
}
```

### 分组配置

```javascript
{
  type: 'group',
  groupField: 'department', // 分组字段
  dataSource: tableData
}
```

## 🛠️ 数据处理工具

### 扁平化嵌套数据

```javascript
import { DataProcessor } from '@/utils/tableMergeUtils.js'

const flatData = DataProcessor.flattenNestedData(nestedData, {
  levelConfigs: [
    {
      childrenField: 'children',    // 子级字段名
      sourceIdField: 'id',         // 源ID字段
      sourceNameField: 'name',     // 源名称字段
      idField: 'levelId',          // 目标ID字段
      nameField: 'levelName'       // 目标名称字段
    }
  ],
  keyMapping: {                    // 字段映射
    oldField: 'newField'
  },
  preserveOriginal: true           // 保留原始数据引用
})
```

### 数据分组

```javascript
// 按字段分组
const groups = DataProcessor.groupData(tableData, 'category')

// 按函数分组
const groups = DataProcessor.groupData(tableData, (item) => {
  return item.category + '-' + item.type
})
```

### 数据排序

```javascript
const sortedData = DataProcessor.sortData(tableData, [
  { field: 'category', order: 'asc', type: 'string' },
  { field: 'score', order: 'desc', type: 'number' }
])
```

## ⚡ 性能优化

### 启用缓存

```javascript
const optimizedColumn = {
  customCell: createMergeColumn('field', 'compareField', {
    cacheResults: true,  // 启用缓存
    dataSource: tableData
  })
}
```

### 限制合并行数

```javascript
const limitedColumn = {
  customCell: createMergeColumn('field', 'compareField', {
    maxSpan: 100,       // 最大合并100行
    dataSource: tableData
  })
}
```

### 性能监控

```javascript
import { PerformanceMonitor } from '@/utils/tableMergeUtils.js'

// 开始计时
PerformanceMonitor.start('mergeCalculation')

// 执行合并计算
const result = calculateMerge(data)

// 结束计时
const duration = PerformanceMonitor.end('mergeCalculation')
console.log(`合并计算耗时: ${duration}ms`)
```

## 🔧 常见问题解决

### 问题1：合并不生效

**原因**：数据源未正确传入

**解决**：
```javascript
// ❌ 错误：未传入 dataSource
customCell: createMergeColumn('field', 'compareField')

// ✅ 正确：传入 dataSource
customCell: createMergeColumn('field', 'compareField', {
  dataSource: tableData
})
```

### 问题2：性能问题

**原因**：大数据量未优化

**解决**：
```javascript
// ✅ 启用缓存和限制
customCell: createMergeColumn('field', 'compareField', {
  cacheResults: true,
  maxSpan: 100,
  dataSource: tableData
})
```

### 问题3：层级合并错误

**原因**：数据未正确扁平化

**解决**：
```javascript
// ✅ 正确的扁平化配置
const flatData = DataProcessor.flattenNestedData(nestedData, {
  levelConfigs: [
    { childrenField: 'children', idField: 'level1Id' },
    { childrenField: 'children', idField: 'level2Id' },
    { idField: 'level3Id' }
  ]
})
```

## 📚 完整示例

### 教育评价系统示例

```vue
<template>
  <a-table 
    :dataSource="evaluationData" 
    :columns="evaluationColumns"
    bordered
    :pagination="false"
  />
</template>

<script setup>
import { ref } from 'vue'
import { createMergeColumn, DataProcessor } from '@/utils/tableMergeUtils.js'

// 原始嵌套数据
const nestedData = [
  {
    id: '1',
    name: '学习态度',
    children: [
      {
        id: '1-1',
        name: '课堂参与',
        children: [
          { id: '1-1-1', name: '积极发言', score: 85 },
          { id: '1-1-2', name: '认真听讲', score: 90 }
        ]
      }
    ]
  }
]

// 扁平化数据
const evaluationData = DataProcessor.flattenNestedData(nestedData, {
  levelConfigs: [
    { childrenField: 'children', idField: 'firstId', nameField: 'firstName' },
    { childrenField: 'children', idField: 'secondId', nameField: 'secondName' },
    { idField: 'standardId', nameField: 'standardName' }
  ]
})

// 层级字段
const levelFields = ['firstId', 'secondId', 'standardId']

// 表格列配置
const evaluationColumns = [
  {
    title: '一级指标',
    dataIndex: 'firstName',
    width: 150,
    align: 'center',
    customCell: createMergeColumn('firstName', levelFields, {
      type: 'hierarchy',
      targetLevel: 0,
      dataSource: evaluationData
    })
  },
  {
    title: '二级指标',
    dataIndex: 'secondName',
    width: 150,
    align: 'center',
    customCell: createMergeColumn('secondName', levelFields, {
      type: 'hierarchy',
      targetLevel: 1,
      dataSource: evaluationData
    })
  },
  {
    title: '评分标准',
    dataIndex: 'standardName',
    width: 200
  },
  {
    title: '分数',
    dataIndex: 'score',
    width: 100,
    align: 'center'
  }
]
</script>
```

## 🎯 下一步

1. **查看完整文档**：[表格合并工具库使用文档.md](./表格合并工具库使用文档.md)
2. **运行测试**：使用 `TableMergeUtilsTest.vue` 组件测试所有功能
3. **查看示例**：参考 `tableMergeExamples.js` 中的各种使用示例
4. **性能优化**：根据实际数据量调整缓存和限制配置

## 📞 技术支持

如果遇到问题：
1. 检查数据源是否正确传入
2. 验证比较字段的数据类型
3. 查看浏览器控制台的错误信息
4. 参考完整文档和示例代码
