let store
export default app => {
    app.directive('focus', {
        mounted: el => el.focus(),
    })
    app.directive('auth', {
        mounted(el, binding) {
            if (!binding.value) {
                return
            }
            if (store) {
                if (!store.Perms.includes(binding.value)) {
                    el.parentNode.removeChild(el)
                }
            } else {
                store = useStore()
                if (!store.Perms.includes(binding.value)) {
                    el.parentNode.removeChild(el)
                }
            }
        },
    })
}
