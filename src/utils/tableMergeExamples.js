/**
 * 表格合并工具库使用示例
 * 包含各种场景的完整示例代码
 */

import {
  calculateRowSpan,
  calculateMultiFieldRowSpan,
  calculateHierarchyRowSpan,
  calculateConditionalRowSpan,
  calculateGroupRowSpan,
  calculateSmartRowSpan,
  createMergeColumn,
  generateMergeConfigs,
  DataProcessor,
  PerformanceMonitor
} from './tableMergeUtils.js'

/**
 * 示例数据集
 */
export const ExampleData = {
  // 基础二层数据
  basicData: [
    { id: 1, category: 'A', type: 'X', score: 85, status: 'active' },
    { id: 2, category: 'A', type: 'Y', score: 90, status: 'active' },
    { id: 3, category: 'A', type: 'Z', score: 78, status: 'inactive' },
    { id: 4, category: 'B', type: 'X', score: 92, status: 'active' },
    { id: 5, category: 'B', type: 'Y', score: 88, status: 'active' },
    { id: 6, category: 'C', type: 'X', score: 76, status: 'active' }
  ],

  // 多层级嵌套数据
  nestedData: [
    {
      id: 'first-1',
      name: '学习态度评价',
      children: [
        {
          id: 'second-1-1',
          name: '课堂参与度',
          children: [
            { id: 'standard-1-1-1', name: '积极发言', score: 85, maxScore: 100 },
            { id: 'standard-1-1-2', name: '认真听讲', score: 90, maxScore: 100 },
            { id: 'standard-1-1-3', name: '课堂纪律', score: 95, maxScore: 100 }
          ]
        },
        {
          id: 'second-1-2',
          name: '作业完成质量',
          children: [
            { id: 'standard-1-2-1', name: '按时提交', score: 88, maxScore: 100 },
            { id: 'standard-1-2-2', name: '内容完整', score: 92, maxScore: 100 }
          ]
        }
      ]
    },
    {
      id: 'first-2',
      name: '学习能力评价',
      children: [
        {
          id: 'second-2-1',
          name: '理解能力',
          children: [
            { id: 'standard-2-1-1', name: '概念理解', score: 87, maxScore: 100 },
            { id: 'standard-2-1-2', name: '知识运用', score: 89, maxScore: 100 }
          ]
        }
      ]
    }
  ],

  // 分组数据
  groupData: [
    { id: 1, department: 'IT', position: '开发工程师', level: 'P6', project: 'A' },
    { id: 2, department: 'IT', position: '开发工程师', level: 'P7', project: 'A' },
    { id: 3, department: 'IT', position: '测试工程师', level: 'P6', project: 'A' },
    { id: 4, department: 'IT', position: '开发工程师', level: 'P6', project: 'B' },
    { id: 5, department: 'HR', position: '招聘专员', level: 'P5', project: 'C' },
    { id: 6, department: 'HR', position: '招聘专员', level: 'P6', project: 'C' }
  ]
}

/**
 * 基础合并示例
 */
export const BasicMergeExample = {
  /**
   * 简单字段合并
   */
  simpleFieldMerge() {
    const data = ExampleData.basicData
    
    // 按类别合并
    const categoryMergeColumn = {
      title: '类别',
      dataIndex: 'category',
      customCell: createMergeColumn('category', 'category', {
        type: 'basic',
        dataSource: data
      })
    }

    return {
      data,
      columns: [
        categoryMergeColumn,
        { title: '类型', dataIndex: 'type' },
        { title: '分数', dataIndex: 'score' },
        { title: '状态', dataIndex: 'status' }
      ]
    }
  },

  /**
   * 自定义比较函数合并
   */
  customCompareMerge() {
    const data = ExampleData.basicData
    
    const customMergeColumn = {
      title: '组合字段',
      dataIndex: 'category',
      customCell: createMergeColumn('category', (record, index) => {
        // 自定义合并逻辑：相同类别且分数差距小于10的合并
        return `${record.category}-${Math.floor(record.score / 10)}`
      }, {
        type: 'basic',
        dataSource: data
      })
    }

    return { data, columns: [customMergeColumn] }
  }
}

/**
 * 多字段合并示例
 */
export const MultiFieldMergeExample = {
  /**
   * AND 操作合并
   */
  andOperatorMerge() {
    const data = ExampleData.basicData
    
    const multiFieldColumn = {
      title: '类别-类型',
      dataIndex: 'category',
      customCell: createMergeColumn('category', ['category', 'type'], {
        type: 'multi',
        operator: 'AND',
        dataSource: data
      })
    }

    return { data, columns: [multiFieldColumn] }
  },

  /**
   * OR 操作合并
   */
  orOperatorMerge() {
    const data = ExampleData.basicData
    
    const orMergeColumn = {
      title: '类别或类型',
      dataIndex: 'category',
      customCell: createMergeColumn('category', ['category', 'type'], {
        type: 'multi',
        operator: 'OR',
        dataSource: data
      })
    }

    return { data, columns: [orMergeColumn] }
  }
}

/**
 * 层级合并示例
 */
export const HierarchyMergeExample = {
  /**
   * 三层级数据合并
   */
  threeLayerMerge() {
    // 扁平化嵌套数据
    const flatData = DataProcessor.flattenNestedData(ExampleData.nestedData, {
      levelConfigs: [
        {
          childrenField: 'children',
          sourceIdField: 'id',
          sourceNameField: 'name',
          idField: 'firstLevelId',
          nameField: 'firstLevelName'
        },
        {
          childrenField: 'children',
          sourceIdField: 'id',
          sourceNameField: 'name',
          idField: 'secondLevelId',
          nameField: 'secondLevelName'
        },
        {
          sourceIdField: 'id',
          sourceNameField: 'name',
          idField: 'standardId',
          nameField: 'standardName'
        }
      ]
    })

    const levelFields = ['firstLevelId', 'secondLevelId', 'standardId']

    const columns = [
      {
        title: '一级指标',
        dataIndex: 'firstLevelName',
        customCell: createMergeColumn('firstLevelName', levelFields, {
          type: 'hierarchy',
          targetLevel: 0,
          dataSource: flatData
        })
      },
      {
        title: '二级指标',
        dataIndex: 'secondLevelName',
        customCell: createMergeColumn('secondLevelName', levelFields, {
          type: 'hierarchy',
          targetLevel: 1,
          dataSource: flatData
        })
      },
      {
        title: '评分标准',
        dataIndex: 'standardName'
      },
      {
        title: '分数',
        dataIndex: 'score'
      },
      {
        title: '满分',
        dataIndex: 'maxScore'
      }
    ]

    return { data: flatData, columns }
  }
}

/**
 * 条件合并示例
 */
export const ConditionalMergeExample = {
  /**
   * 状态条件合并
   */
  statusConditionMerge() {
    const data = ExampleData.basicData
    
    const conditionalColumn = {
      title: '激活类别',
      dataIndex: 'category',
      customCell: createMergeColumn('category', 'category', {
        type: 'conditional',
        condition: (record) => record.status === 'active',
        dataSource: data
      })
    }

    return { data, columns: [conditionalColumn] }
  },

  /**
   * 分数条件合并
   */
  scoreConditionMerge() {
    const data = ExampleData.basicData
    
    const scoreConditionalColumn = {
      title: '高分类别',
      dataIndex: 'category',
      customCell: createMergeColumn('category', 'category', {
        type: 'conditional',
        condition: (record) => record.score >= 85,
        maxSpan: 3,
        dataSource: data
      })
    }

    return { data, columns: [scoreConditionalColumn] }
  }
}

/**
 * 分组合并示例
 */
export const GroupMergeExample = {
  /**
   * 部门内职位合并
   */
  departmentPositionMerge() {
    const data = ExampleData.groupData
    
    const groupColumn = {
      title: '职位',
      dataIndex: 'position',
      customCell: createMergeColumn('position', 'position', {
        type: 'group',
        groupField: 'department',
        dataSource: data
      })
    }

    return { data, columns: [groupColumn] }
  },

  /**
   * 项目内级别合并
   */
  projectLevelMerge() {
    const data = ExampleData.groupData
    
    const projectGroupColumn = {
      title: '级别',
      dataIndex: 'level',
      customCell: createMergeColumn('level', 'level', {
        type: 'group',
        groupField: 'project',
        dataSource: data
      })
    }

    return { data, columns: [projectGroupColumn] }
  }
}

/**
 * 智能合并示例
 */
export const SmartMergeExample = {
  /**
   * 自动检测合并策略
   */
  autoDetectMerge() {
    const data = ExampleData.basicData
    
    const smartColumns = [
      {
        title: '智能单字段',
        dataIndex: 'category',
        customCell: createMergeColumn('category', 'category', {
          type: 'smart',
          dataSource: data
        })
      },
      {
        title: '智能多字段',
        dataIndex: 'type',
        customCell: createMergeColumn('type', ['category', 'type'], {
          type: 'smart',
          dataSource: data
        })
      },
      {
        title: '智能函数',
        dataIndex: 'score',
        customCell: createMergeColumn('score', (record) => {
          return record.score >= 85 ? 'high' : 'low'
        }, {
          type: 'smart',
          dataSource: data
        })
      }
    ]

    return { data, columns: smartColumns }
  }
}

/**
 * 批量配置示例
 */
export const BatchConfigExample = {
  /**
   * 批量生成合并配置
   */
  batchGenerate() {
    const data = ExampleData.basicData
    
    const mergeConfigs = [
      {
        field: 'category',
        type: 'basic'
      },
      {
        field: ['category', 'type'],
        type: 'multi',
        options: { operator: 'AND' }
      },
      {
        field: 'status',
        type: 'conditional',
        options: {
          condition: (record) => record.score >= 80
        }
      }
    ]

    const mergeResults = generateMergeConfigs(data, mergeConfigs)

    const columns = [
      {
        title: '类别',
        dataIndex: 'category',
        customCell: (record, rowIndex) => ({
          rowSpan: mergeResults.get(`category-${rowIndex}`)
        })
      },
      {
        title: '类型',
        dataIndex: 'type'
      },
      {
        title: '状态',
        dataIndex: 'status',
        customCell: (record, rowIndex) => ({
          rowSpan: mergeResults.get(`status-${rowIndex}`)
        })
      }
    ]

    return { data, columns, mergeResults }
  }
}

/**
 * 性能测试示例
 */
export const PerformanceExample = {
  /**
   * 大数据量性能测试
   */
  largeDataPerformance() {
    // 生成大量测试数据
    const generateLargeData = (size) => {
      const data = []
      for (let i = 0; i < size; i++) {
        data.push({
          id: i,
          category: `Category${Math.floor(i / 100)}`,
          type: `Type${Math.floor(i / 50)}`,
          score: Math.floor(Math.random() * 100),
          status: i % 3 === 0 ? 'active' : 'inactive'
        })
      }
      return data
    }

    const testSizes = [100, 500, 1000, 5000]
    const results = {}

    testSizes.forEach(size => {
      const data = generateLargeData(size)
      
      // 测试基础合并性能
      const basicResult = PerformanceMonitor.measure(() => {
        return data.map((record, index) => 
          calculateRowSpan(data, index, 'category')
        )
      }, `basic-merge-${size}`)

      // 测试多字段合并性能
      const multiResult = PerformanceMonitor.measure(() => {
        return data.map((record, index) => 
          calculateMultiFieldRowSpan(data, index, ['category', 'type'])
        )
      }, `multi-merge-${size}`)

      // 测试智能合并性能
      const smartResult = PerformanceMonitor.measure(() => {
        return data.map((record, index) => 
          calculateSmartRowSpan(data, index, 'category')
        )
      }, `smart-merge-${size}`)

      results[size] = {
        basic: basicResult,
        multi: multiResult,
        smart: smartResult
      }
    })

    return results
  },

  /**
   * 缓存性能对比
   */
  cachePerformanceComparison() {
    const data = ExampleData.basicData.concat(ExampleData.basicData) // 重复数据

    // 无缓存测试
    const noCacheResult = PerformanceMonitor.measure(() => {
      const column = createMergeColumn('category', 'category', {
        cacheResults: false,
        dataSource: data
      })
      
      // 多次调用模拟实际使用
      for (let i = 0; i < 100; i++) {
        data.forEach((record, index) => {
          column.call({ dataSource: data }, record, index)
        })
      }
    }, 'no-cache')

    // 有缓存测试
    const withCacheResult = PerformanceMonitor.measure(() => {
      const column = createMergeColumn('category', 'category', {
        cacheResults: true,
        dataSource: data
      })
      
      // 多次调用模拟实际使用
      for (let i = 0; i < 100; i++) {
        data.forEach((record, index) => {
          column.call({ dataSource: data }, record, index)
        })
      }
    }, 'with-cache')

    return {
      noCache: noCacheResult,
      withCache: withCacheResult,
      improvement: ((noCacheResult - withCacheResult) / noCacheResult * 100).toFixed(2) + '%'
    }
  }
}

/**
 * 完整应用示例
 */
export const CompleteApplicationExample = {
  /**
   * 教育评价系统完整示例
   */
  educationEvaluationSystem() {
    // 使用嵌套数据
    const flatData = DataProcessor.flattenNestedData(ExampleData.nestedData, {
      levelConfigs: [
        {
          childrenField: 'children',
          sourceIdField: 'id',
          sourceNameField: 'name',
          idField: 'firstLevelId',
          nameField: 'firstLevelName'
        },
        {
          childrenField: 'children',
          sourceIdField: 'id',
          sourceNameField: 'name',
          idField: 'secondLevelId',
          nameField: 'secondLevelName'
        },
        {
          sourceIdField: 'id',
          sourceNameField: 'name',
          idField: 'standardId',
          nameField: 'standardName'
        }
      ],
      keyMapping: {
        score: 'currentScore',
        maxScore: 'totalScore'
      }
    })

    // 添加评分数据
    const enhancedData = flatData.map(item => ({
      ...item,
      othersScore: Math.floor(Math.random() * item.totalScore),
      thisScore: null,
      finalScore: null,
      comment: '',
      status: 'pending'
    }))

    const levelFields = ['firstLevelId', 'secondLevelId', 'standardId']

    const columns = [
      {
        title: '一级指标',
        dataIndex: 'firstLevelName',
        width: 150,
        align: 'center',
        customCell: createMergeColumn('firstLevelName', levelFields, {
          type: 'hierarchy',
          targetLevel: 0,
          dataSource: enhancedData
        })
      },
      {
        title: '二级指标',
        dataIndex: 'secondLevelName',
        width: 150,
        align: 'center',
        customCell: createMergeColumn('secondLevelName', levelFields, {
          type: 'hierarchy',
          targetLevel: 1,
          dataSource: enhancedData
        })
      },
      {
        title: '评分标准',
        dataIndex: 'standardName',
        width: 200
      },
      {
        title: '分数范围',
        dataIndex: 'scoreRange',
        width: 100,
        align: 'center',
        customRender: ({ record }) => `0-${record.totalScore}`
      },
      {
        title: '他人评分',
        dataIndex: 'othersScore',
        width: 100,
        align: 'center'
      },
      {
        title: '本次评分',
        dataIndex: 'thisScore',
        width: 100,
        align: 'center'
      },
      {
        title: '最终得分',
        dataIndex: 'finalScore',
        width: 100,
        align: 'center'
      },
      {
        title: '评语',
        dataIndex: 'comment',
        width: 200
      }
    ]

    return {
      data: enhancedData,
      columns,
      originalNested: ExampleData.nestedData,
      flattenConfig: {
        levelConfigs: [
          { childrenField: 'children', idField: 'firstLevelId', nameField: 'firstLevelName' },
          { childrenField: 'children', idField: 'secondLevelId', nameField: 'secondLevelName' },
          { idField: 'standardId', nameField: 'standardName' }
        ]
      }
    }
  }
}

/**
 * 导出所有示例
 */
export default {
  ExampleData,
  BasicMergeExample,
  MultiFieldMergeExample,
  HierarchyMergeExample,
  ConditionalMergeExample,
  GroupMergeExample,
  SmartMergeExample,
  BatchConfigExample,
  PerformanceExample,
  CompleteApplicationExample
}
