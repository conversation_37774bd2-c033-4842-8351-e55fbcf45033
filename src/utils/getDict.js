const systemDict = {}
// 查询数据字典
export default async type => {
    if (Array.isArray(type)) {
        const { data } = await http.post('/cloud/SystemDict/get', type)
        data?.forEach(item => {
            systemDict[item.type] = item.list
        })
        return systemDict
    } else {
        if (systemDict[type]) {
            return systemDict[type]
        } else {
            const { data } = await http.post('/cloud/SystemDict/get', [type])
            systemDict[type] = data && data.length > 0 ? data[0].list : []
            return systemDict[type]
        }
    }
}
