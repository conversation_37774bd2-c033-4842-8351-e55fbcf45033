import { message } from 'ant-design-vue'

export default {
  success: (content, config = {}) => {
    return message.success({
      content: () => content,
      ...config,
    })
  },
  error: (content, config = {}) => {
    return message.error({
      content: () => content,
      ...config,
    })
  },
  warning: (content, config = {}) => {
    return message.warning({
      content: () => content,
      ...config,
    })
  },
  info: (content, config = {}) => {
    return message.info({
      content: () => content,
      ...config,
    })
  },
  loading: (content, config = {}) => {
    return message.loading({
      content: () => content,
      ...config,
    })
  },
}
