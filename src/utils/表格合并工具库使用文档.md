# 表格合并工具库使用文档

## 📋 概述

这是一个功能完整的表格合并工具库，提供了多种合并策略和工具函数，能够处理各种复杂的表格合并场景。

## 🚀 快速开始

### 安装引入

```javascript
import {
  calculateRowSpan,
  calculateMultiFieldRowSpan,
  calculateHierarchyRowSpan,
  createMergeColumn,
  DataProcessor,
  PerformanceMonitor
} from '@/utils/tableMergeUtils.js'
```

### 基础使用

```javascript
// 在 Ant Design Vue 表格中使用
const columns = [
  {
    title: '一级指标',
    dataIndex: 'firstIndicator',
    customCell: createMergeColumn('firstIndicator', 'firstIndicatorId', {
      type: 'basic',
      dataSource: tableData
    })
  }
]
```

## 🔧 核心函数详解

### 1. calculateRowSpan - 基础行合并计算器

**功能**：计算单个字段的行合并数量

**语法**：
```javascript
calculateRowSpan(dataSource, currentIndex, compareField, options)
```

**参数**：
- `dataSource` (Array): 数据源数组
- `currentIndex` (number): 当前行索引
- `compareField` (string|Function): 比较字段名或比较函数
- `options` (Object): 配置选项

**配置选项**：
```javascript
{
  direction: 'down',        // 'down' | 'up' | 'both' - 合并方向
  maxSpan: Infinity,        // 最大合并行数
  minSpan: 1,              // 最小合并行数
  condition: null          // 额外的合并条件函数
}
```

**示例**：
```javascript
// 基础使用
const rowSpan = calculateRowSpan(tableData, 0, 'category')

// 使用自定义比较函数
const rowSpan = calculateRowSpan(tableData, 0, (record, index) => {
  return `${record.category}-${record.type}`
})

// 使用条件合并
const rowSpan = calculateRowSpan(tableData, 0, 'category', {
  condition: (record) => record.status === 'active',
  maxSpan: 5
})
```

### 2. calculateMultiFieldRowSpan - 多字段组合合并

**功能**：基于多个字段的组合进行合并

**语法**：
```javascript
calculateMultiFieldRowSpan(dataSource, currentIndex, compareFields, options)
```

**参数**：
- `compareFields` (Array): 比较字段数组
- `options.operator` (string): 'AND' | 'OR' - 字段组合方式

**示例**：
```javascript
// 多字段 AND 合并
const rowSpan = calculateMultiFieldRowSpan(tableData, 0, ['category', 'type'], {
  operator: 'AND'
})

// 多字段 OR 合并
const rowSpan = calculateMultiFieldRowSpan(tableData, 0, ['field1', 'field2'], {
  operator: 'OR'
})

// 混合字段和函数
const rowSpan = calculateMultiFieldRowSpan(tableData, 0, [
  'category',
  (record) => record.subCategory?.id
], {
  operator: 'AND'
})
```

### 3. calculateHierarchyRowSpan - 层级合并计算器

**功能**：处理多层级数据的合并

**语法**：
```javascript
calculateHierarchyRowSpan(dataSource, currentIndex, levelFields, targetLevel, options)
```

**参数**：
- `levelFields` (Array): 层级字段配置数组
- `targetLevel` (number): 目标合并层级

**示例**：
```javascript
// 三层级数据合并
const levelFields = ['firstLevelId', 'secondLevelId', 'thirdLevelId']

// 合并第一层级（一级指标）
const firstLevelSpan = calculateHierarchyRowSpan(tableData, 0, levelFields, 0)

// 合并第二层级（二级指标）
const secondLevelSpan = calculateHierarchyRowSpan(tableData, 0, levelFields, 1)
```

### 4. calculateConditionalRowSpan - 条件合并计算器

**功能**：基于条件函数进行合并

**语法**：
```javascript
calculateConditionalRowSpan(dataSource, currentIndex, compareField, conditionFn, options)
```

**示例**：
```javascript
// 只合并状态为激活的记录
const rowSpan = calculateConditionalRowSpan(
  tableData, 
  0, 
  'category',
  (record, index) => record.status === 'active' && record.score > 80
)
```

### 5. calculateGroupRowSpan - 分组合并计算器

**功能**：在指定分组内进行合并

**语法**：
```javascript
calculateGroupRowSpan(dataSource, currentIndex, groupField, mergeField, options)
```

**示例**：
```javascript
// 在同一部门内合并相同职位
const rowSpan = calculateGroupRowSpan(
  tableData, 
  0, 
  'department',  // 分组字段
  'position'     // 合并字段
)
```

### 6. calculateSmartRowSpan - 智能合并计算器

**功能**：自动检测最佳合并策略

**语法**：
```javascript
calculateSmartRowSpan(dataSource, currentIndex, compareField, options)
```

**示例**：
```javascript
// 自动检测合并策略
const rowSpan = calculateSmartRowSpan(tableData, 0, 'category')

// 多字段自动合并
const rowSpan = calculateSmartRowSpan(tableData, 0, ['category', 'type'])

// 函数自动合并
const rowSpan = calculateSmartRowSpan(tableData, 0, (record) => record.groupKey)
```

## 🎯 高级功能

### 1. createMergeColumn - 创建表格列合并配置

**功能**：为 Ant Design Vue 表格创建 customCell 函数

**语法**：
```javascript
createMergeColumn(dataIndex, compareField, options)
```

**配置选项**：
```javascript
{
  type: 'smart',           // 合并类型
  cacheResults: true,      // 是否缓存结果
  dataSource: [],          // 数据源
  // ... 其他选项
}
```

**合并类型**：
- `'basic'` - 基础合并
- `'multi'` - 多字段合并
- `'hierarchy'` - 层级合并
- `'conditional'` - 条件合并
- `'group'` - 分组合并
- `'smart'` - 智能合并（默认）

**示例**：
```javascript
// 基础列合并
const basicColumn = {
  title: '类别',
  dataIndex: 'category',
  customCell: createMergeColumn('category', 'categoryId', {
    type: 'basic',
    dataSource: tableData
  })
}

// 多字段列合并
const multiColumn = {
  title: '组合字段',
  dataIndex: 'combined',
  customCell: createMergeColumn('combined', ['field1', 'field2'], {
    type: 'multi',
    operator: 'AND',
    dataSource: tableData
  })
}

// 层级列合并
const hierarchyColumn = {
  title: '一级指标',
  dataIndex: 'firstIndicator',
  customCell: createMergeColumn('firstIndicator', ['level1', 'level2', 'level3'], {
    type: 'hierarchy',
    targetLevel: 0,
    dataSource: tableData
  })
}

// 条件列合并
const conditionalColumn = {
  title: '条件合并',
  dataIndex: 'status',
  customCell: createMergeColumn('status', 'statusCode', {
    type: 'conditional',
    condition: (record) => record.isActive,
    dataSource: tableData
  })
}
```

### 2. generateMergeConfigs - 批量生成合并配置

**功能**：批量处理多个字段的合并配置

**语法**：
```javascript
generateMergeConfigs(dataSource, mergeConfigs)
```

**示例**：
```javascript
const mergeConfigs = [
  {
    field: 'firstIndicator',
    type: 'hierarchy',
    options: { targetLevel: 0 }
  },
  {
    field: 'secondIndicator', 
    type: 'hierarchy',
    options: { targetLevel: 1 }
  },
  {
    field: 'category',
    type: 'basic'
  }
]

const mergeResults = generateMergeConfigs(tableData, mergeConfigs)

// 在表格中使用
const columns = [
  {
    title: '一级指标',
    dataIndex: 'firstIndicator',
    customCell: (record, rowIndex) => ({
      rowSpan: mergeResults.get(`firstIndicator-${rowIndex}`)
    })
  }
]
```

## 🛠️ 数据处理工具

### DataProcessor.flattenNestedData - 数据扁平化

**功能**：将嵌套数据转换为扁平化表格数据

**语法**：
```javascript
DataProcessor.flattenNestedData(nestedData, config)
```

**配置**：
```javascript
{
  levelConfigs: [
    {
      childrenField: 'children',      // 子级字段名
      sourceIdField: 'id',           // 源ID字段
      sourceNameField: 'name',       // 源名称字段
      idField: 'levelId',            // 目标ID字段
      nameField: 'levelName'         // 目标名称字段
    }
  ],
  keyMapping: {},                    // 字段映射
  preserveOriginal: true             // 保留原始数据引用
}
```

**示例**：
```javascript
// 三层嵌套数据扁平化
const nestedData = [
  {
    id: '1',
    name: '一级指标1',
    children: [
      {
        id: '1-1',
        name: '二级指标1-1',
        children: [
          {
            id: '1-1-1',
            name: '评分标准1-1-1',
            score: 85,
            comment: '评语'
          }
        ]
      }
    ]
  }
]

const config = {
  levelConfigs: [
    {
      childrenField: 'children',
      sourceIdField: 'id',
      sourceNameField: 'name',
      idField: 'firstLevelId',
      nameField: 'firstLevelName'
    },
    {
      childrenField: 'children',
      sourceIdField: 'id',
      sourceNameField: 'name',
      idField: 'secondLevelId',
      nameField: 'secondLevelName'
    },
    {
      sourceIdField: 'id',
      sourceNameField: 'name',
      idField: 'thirdLevelId',
      nameField: 'thirdLevelName'
    }
  ],
  keyMapping: {
    score: 'currentScore',
    comment: 'remark'
  }
}

const flatData = DataProcessor.flattenNestedData(nestedData, config)
```

### DataProcessor.groupData - 数据分组

**示例**：
```javascript
// 按字段分组
const groups = DataProcessor.groupData(tableData, 'category')

// 按函数分组
const groups = DataProcessor.groupData(tableData, (item) => {
  return `${item.category}-${item.type}`
})
```

### DataProcessor.sortData - 数据排序

**示例**：
```javascript
const sortConfigs = [
  { field: 'category', order: 'asc', type: 'string' },
  { field: 'score', order: 'desc', type: 'number' },
  { 
    field: (item) => item.createTime, 
    order: 'desc', 
    type: 'auto' 
  }
]

const sortedData = DataProcessor.sortData(tableData, sortConfigs)
```

## ⚡ 性能监控

### PerformanceMonitor - 性能监控工具

**示例**：
```javascript
// 计时器使用
PerformanceMonitor.start('dataProcessing')
const processedData = processLargeData(rawData)
const duration = PerformanceMonitor.end('dataProcessing')
console.log(`数据处理耗时: ${duration}ms`)

// 函数执行时间测量
const result = PerformanceMonitor.measure(() => {
  return calculateComplexMerge(tableData)
}, 'complexMerge')
```

## 📝 完整使用示例

### 示例1：基础二层合并

```javascript
import { createMergeColumn, DataProcessor } from '@/utils/tableMergeUtils.js'

// 数据准备
const tableData = [
  { id: 1, category: 'A', type: 'X', score: 85 },
  { id: 2, category: 'A', type: 'Y', score: 90 },
  { id: 3, category: 'B', type: 'X', score: 78 }
]

// 表格列配置
const columns = [
  {
    title: '类别',
    dataIndex: 'category',
    customCell: createMergeColumn('category', 'category', {
      type: 'basic',
      dataSource: tableData
    })
  },
  {
    title: '类型',
    dataIndex: 'type'
  },
  {
    title: '分数',
    dataIndex: 'score'
  }
]
```

### 示例2：多层级合并

```javascript
// 三层数据结构
const hierarchyData = DataProcessor.flattenNestedData(nestedData, {
  levelConfigs: [
    { childrenField: 'secondIndicators', idField: 'firstId', nameField: 'firstName' },
    { childrenField: 'scoreStandards', idField: 'secondId', nameField: 'secondName' },
    { idField: 'standardId', nameField: 'standardName' }
  ]
})

// 多层级合并列配置
const hierarchyColumns = [
  {
    title: '一级指标',
    dataIndex: 'firstName',
    customCell: createMergeColumn('firstName', ['firstId', 'secondId', 'standardId'], {
      type: 'hierarchy',
      targetLevel: 0,
      dataSource: hierarchyData
    })
  },
  {
    title: '二级指标',
    dataIndex: 'secondName',
    customCell: createMergeColumn('secondName', ['firstId', 'secondId', 'standardId'], {
      type: 'hierarchy',
      targetLevel: 1,
      dataSource: hierarchyData
    })
  },
  {
    title: '评分标准',
    dataIndex: 'standardName'
  }
]
```

### 示例3：条件合并

```javascript
// 只合并激活状态的记录
const conditionalColumns = [
  {
    title: '部门',
    dataIndex: 'department',
    customCell: createMergeColumn('department', 'departmentId', {
      type: 'conditional',
      condition: (record) => record.status === 'active' && record.visible,
      dataSource: tableData
    })
  }
]
```

### 示例4：分组合并

```javascript
// 在同一项目内合并相同任务类型
const groupColumns = [
  {
    title: '任务类型',
    dataIndex: 'taskType',
    customCell: createMergeColumn('taskType', 'taskTypeId', {
      type: 'group',
      groupField: 'projectId',
      dataSource: tableData
    })
  }
]
```

## 🔧 高级配置

### 缓存优化

```javascript
// 启用缓存（默认开启）
const column = {
  customCell: createMergeColumn('field', 'compareField', {
    cacheResults: true,
    dataSource: tableData
  })
}

// 清除缓存
import { clearMergeCache } from '@/utils/tableMergeUtils.js'
clearMergeCache(column.customCell)
```

### 性能优化建议

1. **大数据量处理**：
```javascript
// 使用分页或虚拟滚动
const options = {
  maxSpan: 100,  // 限制最大合并行数
  cacheResults: true  // 启用缓存
}
```

2. **复杂计算优化**：
```javascript
// 预处理数据
const preprocessedData = DataProcessor.sortData(rawData, sortConfigs)

// 使用性能监控
PerformanceMonitor.start('mergeCalculation')
const result = calculateComplexMerge(preprocessedData)
PerformanceMonitor.end('mergeCalculation')
```

## ⚠️ 注意事项

1. **数据源一致性**：确保传入的 `dataSource` 与表格使用的数据源一致
2. **索引准确性**：`currentIndex` 必须对应正确的数据行
3. **性能考虑**：大数据量时建议启用缓存和限制合并行数
4. **内存管理**：及时清理不需要的缓存

## 🐛 故障排除

### 常见问题

1. **合并不生效**
   - 检查 `dataSource` 是否正确传入
   - 验证比较字段的数据类型和值
   - 确认 `customCell` 函数返回格式正确

2. **性能问题**
   - 启用缓存：`cacheResults: true`
   - 限制合并行数：`maxSpan: 100`
   - 使用性能监控工具分析瓶颈

3. **内存泄漏**
   - 定期清理缓存：`clearMergeCache()`
   - 避免在 `compareField` 函数中创建大对象
   - 合理设置组件的生命周期

## 📚 API 参考

完整的 API 参考请查看源码中的 JSDoc 注释，包含了所有函数的详细参数说明和返回值类型。
