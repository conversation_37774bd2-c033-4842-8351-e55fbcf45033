const code = useCode()
export default () => {
    if (code == 'yd-library' || code == 'shLibrary') {
        const library = useLibraryStore()
        code == 'shLibrary' && library.changeTOsociety()
        return useLibraryStore()
    } else if (code == 'yd-rfid' || code == 'shRFID') {
        const frid = useRfidStore()
        code == 'shRFID' && frid.changeTOsociety()
        return useRfidStore()
    } else if (code == 'yd-permission') {
        return usePermissionStore()
    } else if (code == 'yd-dormitory') {
        return useDormitoryStore()
    } else if (code == 'yd-security') {
        return useSecurityStore()
    } else if (code == 'yd-evaluation') {
        return useEvaluationStore()
    } else if (code == 'yd-xyf') {
        return useXYFStore()
    } else if (code == 'yd-paySystem') {
        return usePaySystemStore()
    }
}
