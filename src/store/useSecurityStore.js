import { defineStore } from 'pinia'
export default defineStore('Security', {
    state: () => {
        return {
            userInfo: {},
            title: '校园安防系统',
            logo: 'icon-logo1',
            platform: 'yd-security',
            platformCode: 'security',
            clientId: 'yide-cloud',
            collapsed: false,
            tags: [
                {
                    key: 'home',
                    title: '首页',
                },
            ],
            exTag: '',
            sysColor: '#00B781',
        }
    },
    persist: {
        enabled: true,
        key: 'security',
        storage: localStorage,
        paths: ['tags', 'collapsed'],
    },
})
