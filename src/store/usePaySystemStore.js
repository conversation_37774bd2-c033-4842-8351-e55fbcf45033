import { defineStore } from 'pinia'
export default defineStore('paySystem', {
    state: () => {
        return {
            userInfo: {},
            title: '缴费管理系统',
            logo: 'icon-logo1',
            platform: 'paySystem',
            platformCode: 'paySystem',
            clientId: 'yide-cloud',
            collapsed: false,
            tags: [],
            exTag: '',
            sysColor: '#00B781',
        }
    },
    persist: {
        enabled: true,
        key: 'paySystem',
        storage: localStorage,
        paths: ['tags', 'collapsed'],
    },
})
