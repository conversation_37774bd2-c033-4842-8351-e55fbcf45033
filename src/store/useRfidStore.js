import { defineStore } from 'pinia'
export default defineStore('Rfid', {
    state: () => {
        return {
            userInfo: {},
            deviceId: '888',
            LibId: '100',
            title: 'RFID图书管理系统',
            logo: 'icon-logo1',
            platform: 'yd-rfid',
            platformCode: 'libRfid',
            clientId: 'yide-rfid',
            collapsed: false,
            tags: [
                {
                    key: 'home',
                    title: '首页',
                },
            ],
            exTag: '',
            sysColor: '#00B781',
        }
    },
    actions: {
        changeTOsociety() {
            const society = {
                title: 'RFID社会图书管理系统',
                platform: 'shRFID',
                clientId: 'yide-rfid-sh',
                platformCode: 'shRFID',
            }
            for (let key in society) {
                this[key] = society[key]
            }
        },
    },
    persist: {
        enabled: true,
        key: 'rfid',
        storage: localStorage,
        paths: ['tags', 'collapsed'],
    },
})
