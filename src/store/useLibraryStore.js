import { defineStore } from 'pinia'
export default defineStore('Library', {
    state: () => {
        return {
            userInfo: {},
            LibId: null,
            title: null,
            platform: 'library',
            platformCode: 'smartLib',
            clientId: 'yide-library',
            logo: 'icon-logo1',
            collapsed: false,
            libList: [],
            tags: [
                {
                    key: 'home',
                    title: '首页',
                },
            ],
            exTag: '',
            bookDefaultNumber: 0,
            sysColor: '#00B781',
            // 展开首页统计
            statisticalOptions: {
                expand: false,
                show: false,
            },
        }
    },
    actions: {
        setLibId(id, name) {
            this.LibId = id
            this.title = name || '一加壹图书馆'
        },
        changeTOsociety() {
            const society = {
                platform: 'shLibrary',
                clientId: 'yide-library-sh',
                platformCode: 'shLibrary',
            }
            for (let key in society) {
                this[key] = society[key]
            }
        },
    },
    persist: {
        enabled: true,
        key: 'library',
        storage: sessionStorage,
        paths: ['tags', 'collapsed', 'LibId', 'title'],
    },
})
