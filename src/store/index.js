import { defineStore } from 'pinia'
import axios from 'axios'

export const useStore = defineStore('main', {
    state: () => {
        return {
            // cloud：云平台、libRfid：RFID系统、smartLib：图书馆管理系统、smartDormitory：宿舍管理系统
            loginPlatform: '',
            userInfo: {},
            token: null,
            spinning: false,
            remember: true,
            accountFrom: {},
            Perms: [],
            // 获取学校JSON模板组件
            commentJsonKeys: {},
            agreement: false, // 登录协议是否同意
            // !!! 是否登录完成，可能子系统选择学校步骤时，用户可能就刷新了页面
            isCompletedLogin: true,
            faceReader: {},
        }
    },
    persist: {
        enabled: true,
        key: 'yide',
        storage: localStorage,
        paths: [
            'userInfo',
            'token',
            'remember',
            'accountFrom',
            'agreement',
            'loginPlatform',
            'isCompletedLogin',
            'commentJsonKeys',
            'faceReader',
        ],
    },
    actions: {
        setFaceReader(key, value) {
            this.faceReader[key] = value
        },
        setToken(token) {
            this.token = token
        },
        setAgreement(agreement) {
            this.agreement = agreement
        },
        clearUser() {
            this.userInfo = {}
            this.token = null
        },
        async getCommentKeys(schoolId) {
            try {
                const params = {
                    schoolId,
                    commentKey: [
                        'INIT_ACT_APPROVE',
                        'INIT_ACT_LEAVE',
                        'INIT_IDENTITY_LIST',
                        'INIT_STUDENT_IMPORT_EXPORT_LIST',
                        'INIT_IMPORT_EXPORT_LIST',
                        'SCHOOL_TYPE',
                        'INIT_SELECT',
                        'INIT_SCHOOL',
                        'INIT_STUDENT_TABLE_LIST',
                        'INIT_OPERATION_LIST',
                        'INIT_ROLL_LIST',
                    ],
                }
                const res = await axios.post('/cloud/school/template/getJsonCommentKey', params)
                this.commentJsonKeys = res.data
                return res
            } catch (error) {
                return error
            }
        },
    },
})
