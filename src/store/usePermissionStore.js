import { defineStore } from 'pinia'
export default defineStore('Permission', {
    state: () => {
        return {
            userInfo: {},
            title: '壹德权限管理系统',
            logo: 'icon-logo1',
            collapsed: false,
            platform: 'permission',
            clientId: 'yide-cloud',
            tags: [
                {
                    key: 'home',
                    title: '首页',
                },
            ],
            exTag: '',
            sysColor: '#00B781',
        }
    },
    actions: {},
    persist: {
        enabled: true,
        key: 'permission',
        paths: ['tags', 'collapsed'],
    },
})
