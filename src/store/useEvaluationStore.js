import { defineStore } from 'pinia'
export default defineStore('Evaluation', {
    state: () => {
        return {
            userInfo: {},
            title: '评价管理系统',
            logo: 'icon-logo1',
            collapsed: false,
            platform: 'evaluation',
            clientId: 'yide-evaluation',
            tags: [
                {
                    key: 'home',
                    title: '首页',
                },
            ],
            exTag: '',
            sysColor: '#00B781',
        }
    },
    actions: {},
    persist: {
        enabled: true,
        key: 'evaluation',
        paths: ['tags', 'collapsed'],
    },
})
