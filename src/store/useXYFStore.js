import { defineStore } from 'pinia'
export default defineStore('xyf', {
    state: () => {
        return {
            userInfo: {},
            title: '校易付',
            logo: 'icon-xiaoyifu5',
            //  partner-服务商管理端
            //  merchant-商户端
            platform: '',
            partnerList: [],
            PartnerId: null,
            platformCode: 'yidexyf',
            clientId: 'yide-partner',
            collapsed: false,
            tags: [
                {
                    key: 'home',
                    title: '首页',
                },
            ],
            exTag: '',
            sysColor: '#00B781',
        }
    },
    actions: {
        setPlatformId(id) {
            this.PartnerId = id
        },
        clearUser() {
            this.PartnerId = null
            this.platform = ''
            this.title = '校易付'
            this.tags = []
            this.sysColor = '#00B781'
        },
    },
    persist: {
        enabled: true,
        key: 'xyf',
        storage: localStorage,
        paths: ['tags', 'collapsed', 'PartnerId', 'title', 'platform', 'sysColor'],
    },
})
