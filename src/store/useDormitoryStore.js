import { defineStore } from 'pinia'
import axios from 'axios'
export default defineStore('Dormitory', {
    state: () => {
        return {
            userInfo: {},
            title: '智慧宿舍系统',
            logo: 'icon-logo1',
            platform: 'yd-dormitory',
            platformCode: 'smartDormitory',
            clientId: 'yide-cloud',
            collapsed: false,
            tags: [
                {
                    key: 'home',
                    title: '首页',
                },
            ],
            exTag: '',
            sysColor: '#00B781',

            // 区分某些功能相同但模块不同的接口-智慧宿舍：4
            moduleType: 4,
            // 当前选中宿舍楼栋
            activeDorm: {},
            // 宿舍列表
            dorms: [],
            // 宿舍入住状态
            checkInStatus: [
                { label: '未入住', value: 1 },
                { label: '已入住', value: 2 },
                { label: '已退宿', value: 3 },
            ],
            // 考勤类型
            attendanceTypes: [
                { label: '循环考勤', value: 0 },
                { label: '单次考勤', value: 1 },
            ],
            // 考勤状态
            attendanceStatus: [
                { label: '正常', value: 0, color: '#00B781' },
                { label: '未归寝', value: 1, color: '#F5222D' },
                { label: '晚归', value: 2, color: '#FC941F' },
                { label: '请假', value: 5, color: '#3896EA' },
                { label: '未打卡', value: 6, color: '#5534F0' },
            ],
            // 楼栋列表
            buildings: [],
            // 场地类型
            siteTypes: [],
            // 设备列表
            devices: [],
            // 寝室床位设置方式
            bedSetTypes: [
                { label: '按每层设置', value: 1 },
                { label: '按整栋设置', value: 2 },
            ],
            // 通行方向
            directions: [
                { label: '出', value: 1, color: '#FAAD14 ' },
                { label: '入', value: 2, color: '#00B781' },
            ],
            // 通行方式
            accessTypes: [
                { label: '人脸', value: 1 },
                { label: '刷卡', value: 2 },
            ],
            // 寝室状态
            roomStatus: [
                { label: '满员', value: 2, color: '#FAAD14' },
                { label: '未满', value: 1, color: '#ffffff' },
                { label: '空房', value: 0, color: '#00B781' },
                // { label: '未分房', value: 3, color: '#D9D9D9' },
            ],
            // 调动方式
            transferTypes: [
                { label: '换寝', value: 1 },
                { label: '退寝', value: 2 },
            ],
            // 性别
            genders: [
                { label: '男生', value: 1 },
                { label: '女生', value: 0 },
            ],
            // 住宿方式
            dorm_options: [],
            // 寝室类型数据
            dormitory_room_type: [],
            // 分寝数据（被选中的数据）
            dorm_students: [],
            dorm_beds: [],
            // 场地数据(批量)
            batchData: {},
            // 考勤详情
            statisticsDetail: {
                row: {},
                query: {},
            },
        }
    },
    persist: {
        enabled: true,
        key: 'dormitory',
        storage: localStorage,
        paths: ['tags', 'collapsed', 'dorm_students', 'dorm_beds', 'statisticsDetail'],
    },
    actions: {
        // 获取数据字典 select
        async getDataDictionary() {
            try {
                const res = await axios.post('/cloud/SystemDict/get', ['dormitory_room_type', 'accommodation'])
                if (res.data?.length) {
                    res.data.forEach(v => {
                        if (v.type == 'dormitory_room_type') {
                            this.dormitory_room_type = v.list
                        } else if (v.type == 'accommodation') {
                            this.dorm_options = v.list
                        }
                    })
                }
            } catch (error) {
                // 让表单组件显示错误
                return error
            }
        },
        // 宿舍列表
        async getDorms() {
            try {
                const res = await axios.post('/cloud/dormitory/buildingManagement/dormitoryBuilding')
                this.dorms = res.data
                return res
            } catch (error) {
                return error
            }
        },
    },
})
