import { EventSourceParserStream } from 'eventsource-parser/stream'
const baseUrl = import.meta.env.VITE_BASE_API
const { token } = useStore()
export const useFetchReader = () => {
    const url = `${baseUrl}/rfid/management/device-interaction/person-recognition`
    let controller = null

    const send = async body => {
        try {
            controller = new AbortController()

            const response = await fetch(url, {
                method: 'POST',
                body: JSON.stringify(body),
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: token,
                },
                signal: controller.signal,
            })

            const reader = response?.body?.pipeThrough(new TextDecoderStream()).pipeThrough(new EventSourceParserStream()).getReader()
            let shouldContinue = true

            while (shouldContinue) {
                const x = await reader?.read()
                if (x) {
                    const { value } = x
                    try {
                        const val = JSON.parse(value?.data || '')
                        const d = val?.data
                        if (
                            val.event == 'PERSON_RECOGNITION_SUCCEEDED' ||
                            val.event == 'PERSON_RECOGNITION_FAILED' ||
                            val.event == 'PERSON_RECOGNITION_TIMEOUT'
                        ) {
                            shouldContinue = false
                            if (val.event != 'PERSON_RECOGNITION_SUCCEEDED') {
                                YMessage.error(d.message)
                            } else {
                                YMessage.success('识别成功')
                            }
                            return { data: d, event: val.event }
                        } else {
                            YMessage.warning(d.message)
                        }
                    } catch (e) {
                        shouldContinue = false
                        throw new Error(`数据解析错误: ${e.message}`)
                    }
                }
            }
        } catch (error) {
            if (error.name === 'AbortError') {
                return { data: null, event: 'ABORTED' }
            }
            throw new Error(`请求失败: ${error.message}`)
        }
    }

    const abort = () => {
        if (controller) {
            controller.abort()
            controller = null
        }
    }

    return {
        send,
        abort,
    }
}
