export default (url, other = {}) => {
    const query = reactive({
        pageNo: 1,
        pageSize: 10,
        ...other,
    })
    const page = reactive({
        // 加载状态
        loading: false,
        list: [],
        total: 0,
    })
    const getList = async params => {
        query.pageNo = 1
        params && Object.assign(query, params)
        try {
            page.loading = true
            const { data } = await http.post(url, query)
            Object.assign(page, data)
            return Promise.resolve(data)
        } catch (e) {
            console.log('异常', e)
        } finally {
            page.loading = false
        }
    }
    // 如果有默认搜索条件 op
    const reset = op => {
        const pageSize = query.pageSize
        for (const key in query) {
            query[key] = null
        }
        query.pageNo = 1
        query.pageSize = pageSize
        op && Object.assign(query, op)
        getList()
    }
    const paginationChange = val => {
        if (val.pageSize == query.pageSize) {
            getList(val)
        } else {
            getList({ pageSize: val.pageSize, pageNo: 1 })
        }
    }
    return { page, query, getList, reset, paginationChange }
}
