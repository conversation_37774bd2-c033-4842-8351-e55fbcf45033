<template>
    <div>
        <a-config-provider
            :locale="zh_CN"
            :theme="{
                token: {
                    colorPrimary: typeStore.sysColor,
                },
            }"
        >
            <router-view></router-view>
        </a-config-provider>
    </div>
</template>
<script setup>
import zh_CN from 'ant-design-vue/es/locale/zh_CN'
import 'dayjs/locale/zh-cn'
const typeStore = useTypeStore()

document.documentElement.style.setProperty('--primary-color', typeStore.sysColor)

// 动态修改项目的logo
function changeFavicon(link) {
    let $favicon = document.querySelector('link[rel="icon"]')
    if ($favicon !== null) {
        $favicon.href = link
    } else {
        $favicon = document.createElement('link')
        $favicon.rel = 'icon'
        $favicon.href = link
        document.head.appendChild($favicon)
    }
}

const url = typeStore.platform === 'merchant' ? '/logo1.png' : '/logo.png'
changeFavicon(url)
</script>
<style></style>
