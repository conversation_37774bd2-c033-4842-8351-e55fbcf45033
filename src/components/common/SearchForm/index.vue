<template>
    <a-form :model="formState" :labelCol="labelCol" :layout="layout">
        <a-row :gutter="gutter">
            <YCol v-for="item in formList" :key="item.value" v-bind="item.attrs || {}" :span="span(item)">
                <a-form-item :label="item.label" v-bind="validateInfos[item.value]" :labelCol="item.labelCol" :wrapperCol="item.wrapperCol">
                    <slot :name="item.value" v-bind="item" :formState="formState" v-if="item.type === 'slot'"></slot>
                    <template v-else>
                        <!-- 输入框 -->
                        <a-input
                            v-if="item.type === 'input'"
                            v-model:value.trim="formState[item.value]"
                            :placeholder="`请输入${item.label}`"
                            allowClear
                            v-bind="item.attrs || {}"
                        />
                        <!-- 文本框 -->
                        <a-textarea
                            v-if="item.type === 'textarea'"
                            v-model:value="formState[item.value]"
                            :placeholder="`请输入${item.label}`"
                            allowClear
                            v-bind="item.attrs || {}"
                        />
                        <!-- 选择器 -->
                        <a-select
                            v-else-if="item.type === 'select'"
                            v-model:value="formState[item.value]"
                            :options="item.list"
                            :placeholder="`请选择${item.label}`"
                            allowClear
                            v-bind="item.attrs || {}"
                        ></a-select>
                        <!-- 日期选择器 -->
                        <a-date-picker
                            v-else-if="item.type === 'datePicker'"
                            v-model:value="formState[item.value]"
                            allowClear
                            v-bind="item.attrs || {}"
                        />
                        <!-- 日期选择区间 -->
                        <!-- -->
                        <RangePicker
                            v-else-if="item.type === 'rangePicker'"
                            v-model:startTime="formState[item.value[0]]"
                            v-model:endTime="formState[item.value[1]]"
                        ></RangePicker>
                        <!-- 时间选择区间 -->
                        <a-time-picker
                            v-else-if="item.type === 'timePicker'"
                            v-model:value="formState[item.value]"
                            allowClear
                            v-bind="item.attrs || {}"
                        />
                        <!-- TreeSelect 树选择 -->
                        <a-tree-select
                            v-else-if="item.type === 'tree-select'"
                            v-model:value="formState[item.value]"
                            allowClear
                            :placeholder="`请选择${item.label}`"
                            v-bind="item.attrs || {}"
                            :tree-data="item.list"
                        />
                        <!-- 省市区级联 -->
                        <a-cascader
                            v-else-if="item.type === 'cascader'"
                            v-model:value="formState[item.value]"
                            allowClear
                            :placeholder="`请选择${item.label}`"
                            v-bind="item.attrs || {}"
                            :options="item.list"
                        />
                        <a-input
                            v-else-if="item.type === 'text'"
                            v-model:value="formState[item.value]"
                            :placeholder="`请选择${item.label}`"
                            v-bind="item.attrs || {}"
                        />
                        <!-- 单选框 -->
                        <a-radio-group
                            v-else-if="item.type === 'radio'"
                            v-model:value="formState[item.value]"
                            :placeholder="`请选择${item.label}`"
                            v-bind="item.attrs || {}"
                        >
                            <a-radio v-for="it in item.list" :value="it.value">{{ it.label }}</a-radio>
                        </a-radio-group>
                    </template>
                </a-form-item>
            </YCol>
            <YCol v-if="showBtn" :span="btnSpan.span">
                <a-form-item>
                    <div style="display: flex; align-items: center">
                        <a-button type="primary" @click="submit">
                            <template #icon>
                                <SearchOutlined />
                            </template>
                            查询
                        </a-button>
                        <a-button v-if="!!isBarCode" type="primary" @click="barCodeFn">
                            <template #icon>
                                <i class="iconfont icon-tiaoma2" style="font-size: 12px" mr-6></i>
                            </template>
                            条码查缺
                        </a-button>
                        <a-button @click="reset">
                            <template #icon>
                                <ReloadOutlined />
                            </template>
                            重置
                        </a-button>
                    </div>
                </a-form-item>
            </YCol>
        </a-row>
    </a-form>
</template>
<script setup>
import { Form } from 'ant-design-vue'
import { computed } from 'vue'

const useForm = Form.useForm
const prop = defineProps({
    //  表单类型
    isBarCode: {
        type: Boolean,
        default: false,
    },
    //  表单布局
    layout: {
        type: String,
        //  'horizontal' | 'vertical' | 'inline';
        default: 'horizontal',
    },
    //  表单数据值
    formState: {
        type: Object,
        default: () => {},
    },
    //  表单数据列表
    formList: {
        type: Array,
        default: [],
    },
    //  校验规则
    rules: {
        type: Object,
        default: () => {},
    },
    //   是否显示提交按钮
    showBtn: {
        type: Boolean,
        default: true,
    },
    //   表单列宽度
    labelCol: {
        type: Object,
        default: {},
    },
    //   表单列宽度
    btnSpan: {
        type: Object,
        default: {},
    },
    gutter: {
        type: Array,
        default: () => [24, 18],
    },
})

const span = computed(() => {
    return item => {
        // $ 类型为rangePicker且没设置span，默认值给5
        if (!item.span && item.type === 'rangePicker') {
            return 5
        }
        return item.span
    }
})

const rules = computed(() => {
    const rules = prop.formList.reduce((pre, cur) => {
        if (cur.rules) {
            pre[cur.value] = cur.rules
        }
        return pre
    }, {})
    return Object.assign(rules, prop.rules)
})

// 白名单 (不进行初始化的类型白名单)
const whitelist = ['slot', 'rangePicker']

watch(
    () => prop.formList,
    () => {
        prop.formList.forEach(i => {
            if (!prop.formState[i.value] && !whitelist.includes(i.type)) {
                const _value = prop.formState[i.value]
                if (Array.isArray(_value)) {
                    // 数组：Array.isArray(item)
                    prop.formState[i.value] = []
                } else if (typeof _value === 'number') {
                    // 数字：typeof item === 'number'
                    prop.formState[i.value] = _value
                } else if (typeof _value === 'string') {
                    // 字符串：typeof item === 'string'
                    prop.formState[i.value] = ''
                } else if (typeof _value === 'boolean') {
                    // 布尔值：
                    prop.formState[i.value] = _value
                } else {
                    // null：item === null
                    prop.formState[i.value] = null
                }
            }
        })
    },
    {
        immediate: true,
        deep: true,
    },
)

let { resetFields, validate, validateInfos } = useForm(prop.formState, rules)

const emit = defineEmits(['submit', 'reset', 'barCode'])

const submit = id => {
    return validate()
        .then(() => {
            emit('submit', id)
            return true
        })
        .catch(() => {
            return false
        })
}

const barCodeFn = () => {
    emit('barCode')
}

const reset = () => {
    resetFields()
    emit('reset')
}

defineExpose({ submit, reset })
</script>
<style lang="less" scoped>
:deep(.ant-input-textarea-show-count) {
    position: relative;

    &:after {
        position: absolute;
        right: 17px;
        bottom: 20px;
        margin-bottom: -22px;
        z-index: 9;
    }
}
</style>
