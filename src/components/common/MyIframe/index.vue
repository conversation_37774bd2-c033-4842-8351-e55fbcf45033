<template>
    <a-spin :spinning="state.spinning" wrapperClassName="iframe_warp">
        <iframe :src="src" ref="myIfarmeRef" frameborder="0" class="my_iframe" id="_iframe"></iframe>
    </a-spin>
</template>

<script setup>
defineProps({
    src: String,
})

const myIfarmeRef = ref()
const state = reactive({
    spinning: true,
})

function init() {
    const iframe = document.getElementById('_iframe')
    if (!iframe) return false
    iframe.onload = () => {
        state.spinning = false
    }
}

onMounted(() => nextTick(() => init()))
</script>

<style lang="less">
.iframe_warp {
    // width: 100%;
    height: 100%;

    .ant-spin-container,
    .my_iframe {
        width: 100%;
        height: calc(100% - 5px);
    }
}
</style>
