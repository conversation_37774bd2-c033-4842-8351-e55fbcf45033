<template>
    <editor v-model="content" :init="init" tag-name="div" />
</template>
<script setup>
import tinymce from 'tinymce/tinymce'
import Editor from '@tinymce/tinymce-vue'
import 'tinymce/themes/silver/theme' // 引用主题文件
import 'tinymce/icons/default' // 引用图标文件
import 'tinymce/models/dom'
// tinymce插件可按自己的需要进行导入
// 更多插件参考：https://www.tiny.cloud/docs/plugins/
import 'tinymce/plugins/advlist'
import 'tinymce/plugins/anchor'
import 'tinymce/plugins/autolink'
import 'tinymce/plugins/autoresize'
import 'tinymce/plugins/autosave'
import 'tinymce/plugins/charmap' // 特殊字符
import 'tinymce/plugins/code' // 查看源码
import 'tinymce/plugins/codesample' // 插入代码
import 'tinymce/plugins/directionality'
import 'tinymce/plugins/emoticons'
import 'tinymce/plugins/fullscreen' //全屏
// import 'tinymce/plugins/help'
import 'tinymce/plugins/image' // 插入上传图片插件
import 'tinymce/plugins/importcss' //图片工具
import 'tinymce/plugins/insertdatetime' //时间插入
import 'tinymce/plugins/link'
import 'tinymce/plugins/lists' // 列表插件
import 'tinymce/plugins/media' // 插入视频插件
import 'tinymce/plugins/nonbreaking'
import 'tinymce/plugins/pagebreak' //分页
import 'tinymce/plugins/preview' // 预览
import 'tinymce/plugins/quickbars'
import 'tinymce/plugins/save' // 保存
import 'tinymce/plugins/searchreplace' //查询替换
import 'tinymce/plugins/table' // 插入表格插件
// import 'tinymce/plugins/template' //插入模板
import 'tinymce/plugins/visualblocks'
import 'tinymce/plugins/visualchars'
import 'tinymce/plugins/wordcount' // 字数统计插件
// import 'tinymce/plugins/fontsizeselect'

// v-model
const props = defineProps({
    modelValue: {
        type: String,
        default: '',
    },
    // 上传文件 类型
    folderType: {
        type: String,
        default: '',
    },
})
const emit = defineEmits(['update:modelValue'])
// 配置
const init = {
    language_url: '/tinymce/langs/zh-Hans.js', // 中文语言包路径
    language: 'zh-Hans',
    skin_url: '/tinymce/skins/ui/oxide', // 编辑器皮肤样式
    content_css: '/tinymce/skins/content/default/content.min.css',
    menubar: false, // 隐藏菜单栏
    autoresize_bottom_margin: 50,
    max_height: 500,
    min_height: 350,
    toolbar_sticky: false,
    file_picker_types: 'media',
    plugins:
        'wordcount visualchars  visualblocks  searchreplace save preview pagebreak nonbreaking media insertdatetime importcss image fullscreen directionality codesample code charmap code lists advlist anchor autolink autoresize autosave',
    toolbar:
        //  outdent indent
        'undo redo aligncenter alignleft alignright alignjustify lineheight underline numlist bullist removeformat forecolor backcolor bold italic image media fontsize fontfamily fullscreen',
    content_style: 'p {margin: 0px; border:0px; padding: 0px;}',
    font_size_formats: '12px 14px 16px 18px 24px 36px 48px 56px 72px',
    font_family_formats:
        '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方= PingFang SC, Microsoft YaHei, sans- serif; 宋体 = simsun, serif; 仿宋体 = FangSong, serif; 黑体 = SimHei, sans - serif; Arial = arial, helvetica, sans - serif;Arial Black = arial black, avant garde;Book Antiqua = book antiqua, palatino; ',
    branding: false,
    elementpath: false,
    resize: false, // 禁止改变大小
    statusbar: false, // 隐藏底部状态栏
    contextmenu_never_use_native: false,
    urlconverter_callback: (url, node, onSave, name) => {
        if (node === 'img' && url.startsWith('blob:')) {
            tinymce.activeEditor && tinymce.activeEditor.uploadImages()
        }
        return url
    },
    images_upload_handler: (blobInfo, progress) =>
        new Promise((resolve, reject) => {
            progress(0)
            // blobInfo.blob() 得到图片的file对象
            let file = blobInfo.blob()
            http.form('/file/common/upload', { file, folderType: props.folderType }, {})
                .then(res => {
                    progress(100)
                    const url = res.data[0].url
                    resolve(url)
                    return
                })
                .catch(err => {
                    reject('上传失败')
                    return
                })
        }),
    file_picker_callback: async (callback, value, meta) => {
        if (meta.filetype === 'media') {
            let input = document.createElement('input')
            input.type = 'file'
            input.onchange = async function () {
                let file = this.files[0]
                http.form('/file/common/upload', { file, folderType: props.folderType }, {}).then(({ data }) => {
                    callback(data[0]?.url)
                })
            }
            input.click()
        }
    },
}
tinymce.init // 初始化
const content = ref(props.modelValue)
watch(props, newVal => (content.value = newVal.modelValue))
watch(content, newVal => emit('update:modelValue', newVal))
</script>
<style scoped>
.tox-tinymce-aux {
    z-index: 9999 !important;
}
</style>
