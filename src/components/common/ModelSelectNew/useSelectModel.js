// TODO: 选人组件hooks
const useSelectModel = modelSelectRef => {
    const modalState = reactive({})

    const openSelectModal = async () => {
        modelSelectRef.value.modelState.open = true
    }

    const getSelectModal = async () => {
        try {
            modelSelectRef.value.modelState.loading = true
            const { data } = await http.get('/marketing/dept/getDeptTree')
        } finally {
            modelSelectRef.value.modelState.loading = false
        }
    }

    const toggleLevel = async (tabId, item, options) => {}

    return { modalState, getSelectModal, openSelectModal }
}

export default useSelectModel
