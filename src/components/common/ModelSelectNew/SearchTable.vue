<template>
    <div :class="{ 'single-table': single }">
        <a-table
            rowKey="id"
            :loading="loading"
            :columns="columns"
            :data-source="list"
            :scroll="{ x: 200, y: list.length ? 360 : null }"
            :pagination="false"
            :row-selection="{
                hideSelectAll: single,
                selectedRowKeys: selectKeys,
                onChange: onSelectChange,
            }"
        >
            <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'detClass'">
                    <Tooltip :title="tipTitle(record)"></Tooltip>
                </template>
            </template>
        </a-table>
        <Pagination
            :total="total"
            @paginationChange="paginationChange"
            :current="current"
            :showQuickJumper="false"
            :showSizeChanger="false"
            size="small"
        ></Pagination>
    </div>
</template>

<script setup>
const columns = [
    {
        title: '姓名',
        dataIndex: 'name',
    },
    {
        title: '部门/班级',
        dataIndex: 'detClass',
    },
]

// *********************
// Hooks Function
// *********************

const props = defineProps({
    // 数据列表
    data: {
        type: Object,
        default: {},
    },
    // 加载中
    loading: {
        type: Boolean,
        default: false,
    },
    // true为单选  false为复选
    single: {
        type: Boolean,
        default: false,
    },
    // 勾选中的列表
    selectKeys: {
        type: Array,
        default: [],
    },
})
const emit = defineEmits(['change', 'paginationChange'])

const list = computed(() => {
    return props.data.list || []
})

const total = computed(() => {
    return props.data.total || 0
})

const current = computed(() => {
    return props.data.pageNo || 1
})

const tipTitle = computed(() => {
    return item => item.deptName || item.deptString || item.className || item.pName || '--'
})

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

// 查询人员分页
const paginationChange = ({ pageNo, pageSize }) => {
    emit('paginationChange', pageNo, pageSize)
}

// 复选框
const onSelectChange = (ron, node) => {
    emit('change', ron, node)
}
</script>

<style lang="less" scoped>
.single-table {
    :deep(.ant-checkbox-inner) {
        border-radius: 100%;
    }
    :deep(.ant-checkbox-checked) {
        &::after {
            border-radius: 100%;
        }
    }
    :deep(.ant-checkbox-wrapper) {
        border-radius: 100%;
    }
}
</style>
