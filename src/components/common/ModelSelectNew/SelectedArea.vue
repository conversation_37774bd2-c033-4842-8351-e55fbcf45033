<template>
    <div class="select-area">
        <header>
            <div class="label">
                已选
                <span class="count">{{ list.length }} {{ unit }}</span>
            </div>
            <a-button type="link" class="btn-link-color" v-if="list.length" @click="clear">清空</a-button>
        </header>

        <ul class="selected-list">
            <li v-for="(item, index) in list" :key="item[fieldNames.value]">
                <a-avatar shape="square" :src="(!isPerson(item) && structure) || ''" :size="24">
                    <span v-if="isPerson(item)">
                        {{ item.studentName?.slice(-2) || item.name?.slice(-2) }}
                    </span>
                </a-avatar>
                <div class="label ellipsis" :title="item[fieldNames.label]">
                    {{ item.showName || item[fieldNames.label] }}
                </div>

                <i class="iconfont icon-close-circle-fill delete-icon" @click="deleteItem(index)"></i>
            </li>
        </ul>
    </div>
</template>

<script setup>
import structure from '@/assets/images/icon-structure.png'
import { Checkbox as ACheckbox, Radio as ARadio } from 'ant-design-vue'
import { SELECT_TYPE } from './constants'

// *********************
// Hooks Function
// *********************

const props = defineProps({
    list: {
        type: Array,
        default: [],
    },
    type: {
        type: String,
        default: SELECT_TYPE.DEPARTMENT,
    },
    tabs: {
        type: Array,
        default: () => [],
    },
    fieldNames: {
        type: Object,
        default: () => {
            return { label: 'name', value: 'id' }
        },
    },
})

const emit = defineEmits(['clear', 'deleteItem'])

// 单位
const unit = computed(() => {
    if (props.type === SELECT_TYPE.DEPARTMENT) {
        // 选部门
        return '部门'
    } else if (props.type === SELECT_TYPE.PEOPLE) {
        return '人'
    } else if ([SELECT_TYPE.CLASS, SELECT_TYPE.ALL].includes(props.type) === SELECT_TYPE.CLASS) {
        // 班级、宿舍、选人和选部门
        return '项'
    }
})

const isPerson = computed(() => {
    return item => {
        if ([SELECT_TYPE.PEOPLE, SELECT_TYPE.ALL].includes(props.type)) {
            // $ tab有一个符合就算是人（切换tab,保持头像必须不变）
            return props.tabs.some(tab => tab.personField?.value.includes(item[tab.personField.key]))
        }
        return false
    }
})

// *********************
// Service Function
// *********************

const clear = () => {
    emit('clear')
}

const deleteItem = index => {
    emit('deleteItem', index)
}
</script>

<style lang="less" scoped>
.select-area {
    display: flex;
    flex-direction: column;
    flex: 1;
    padding: 15px 12px;
    border-left: 1px solid #d9d9d9;
    box-sizing: border-box;
    overflow: hidden;

    header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 15px;
        height: 24px;

        .label {
            font-size: 14px;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
        }

        .count {
            font-size: 12px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.85);
            margin-left: 8px;
        }
    }

    .selected-list {
        display: flex;
        flex-wrap: wrap;
        overflow: hidden auto;

        li {
            box-sizing: border-box;
            max-width: 100%;
            display: flex;
            align-items: center;
            padding: 4px;
            background: #f6f6f6;
            border-radius: 4px;
            margin: 0 10px 10px 0;
        }

        .label {
            flex: 1;
            font-size: 12px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.85);
            margin: 0 4px;
        }

        .ellipsis {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .delete-icon {
            color: #c2c3c3;
            cursor: pointer;
        }
    }

    :deep(.ant-avatar) {
        font-size: 14px;
        background: var(--primary-color);
    }

    :deep(.ant-avatar-image) {
        background: transparent;
    }
}
</style>
