<template>
    <div class="department-list">
        <!-- 搜索和添加按钮区域 -->
        <div class="header-search">
            <a-input-search v-model:value="searchText" allowClear placeholder="搜索员工和部门" @search="emit('emitSearch', $event)" />
        </div>
        <div class="header-actions" v-if="isShowSearch">
            <slot name="MultiFunctionSearch"></slot>
        </div>
        <template v-else>
            <a-space class="header-actions" :size="10">
                <slot name="headerActions"></slot>
            </a-space>

            <!-- 部门树形列表 -->
            <a-directory-tree
                class="department-tree"
                multiple
                blockNode
                :tree-data="departmentTree"
                :fieldNames="replaceFields"
                :showIcon="true"
                :selectable="true"
                :expandOnClick="false"
                v-bind="$attrs"
            >
                <!-- <template #switcherIcon="{ expanded }">
                <caret-down-outlined :class="{ 'tree-switcher-icon': true, expanded }" />
            </template> -->
                <template #icon></template>
                <template #title="{ name, id, pid }">
                    <div class="tree-node">
                        <div class="tree-node-content" @click.stop="emit('emitNodeClick', name, id, pid)">
                            <div class="tree-node-icon-text">
                                <i class="tree-node-icon iconfont icon-yellow icon-a-Fill21"></i>
                                <span class="tree-node-text">{{ name }}</span>
                            </div>
                            <a-dropdown :trigger="['click']" @click.stop>
                                <a class="action-trigger" @click.stop>
                                    <ellipsis-outlined color="#333" />
                                </a>
                                <template #overlay>
                                    <a-menu>
                                        <a-menu-item @click="emit('emitEdit', pid, name, id)">
                                            <!-- <edit-outlined /> -->
                                            <span>编辑部门</span>
                                        </a-menu-item>
                                        <a-menu-item @click="emit('emitAddSubDept', id)">
                                            <!-- <edit-outlined /> -->
                                            <span>添加子部门</span>
                                        </a-menu-item>
                                        <a-menu-item @click="emit('emitDelete', pid, id)" style="color: red">
                                            <!-- <delete-outlined /> -->
                                            <span>删除</span>
                                        </a-menu-item>
                                    </a-menu>
                                </template>
                            </a-dropdown>
                        </div>
                    </div>
                </template>
            </a-directory-tree>
        </template>
    </div>
</template>

<script setup>
import { ref } from 'vue'
const _useSlots = useSlots()
const props = defineProps({
    // 模拟的部门树形数据
    departmentTree: {
        type: Array,
        default: () => [],
    },
    isShowSearch: {
        type: Boolean,
        default: false,
    },
})

const emit = defineEmits(['emitSearch', 'emitNodeClick', 'emitEdit', 'emitAddSubDept', 'emitDelete'])
const replaceFields = {
    title: 'name',
    key: 'id',
    children: 'children',
}

// 搜索文本
const searchText = ref('')
</script>

<style lang="less" scoped>
@primary-color: #00b781;

.department-list {
    padding: 20px;
    width: 300px;
    height: 100%;
    border: 1px solid #f0f0f0;
    background-color: #fff;
    text-align: center;
    overflow: hidden;

    .ant-input-search,
    .header-search {
        width: 100% !important;
    }

    .header-actions {
        margin: 10px auto;
    }

    :deep(.ant-tree-treenode) {
        padding: 6px 0;

        &.ant-tree-treenode-selected {
            &:hover::before,
            &::before {
                background: rgba(0, 183, 129, 0.08);
            }

            .tree-node-content {
                color: @primary-color;
            }

            .anticon-caret-down {
                svg {
                    fill: inherit;
                }
            }

            .action-trigger {
                display: block;
            }

            .tree-node-icon {
                color: @primary-color !important;
            }
        }

        .tree-node-icon {
            color: #faad14 !important;
            margin-right: 8px;
            font-size: 12px;
        }

        .action-trigger {
            display: none;

            .anticon {
                transform: rotate(90deg);
            }
        }

        .tree-node,
        .ant-tree-title,
        .tree-node-content,
        .ant-tree-node-content-wrapper {
            display: flex;
            flex: 1;
        }

        .tree-node-content {
            justify-content: space-between;
        }

        .ant-tree-switcher {
            cursor: pointer;
        }

        .ant-tree-node-content-wrapper {
            cursor: pointer;
        }
    }

    :deep(.tree-switcher-icon) {
        transition: transform 0.3s;

        &.expanded {
            transform: rotate(180deg);
        }
    }

    :deep(.ant-tree-treenode) {
        .anticon-folder {
            color: @primary-color;
        }
    }

    .tree-node-icon-text {
        display: flex;
        align-items: center;

        .tree-node-text {
            // 字数超出隐藏
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: inline-block;
            width: 165px;
            text-align: left;
        }
    }
}
</style>
