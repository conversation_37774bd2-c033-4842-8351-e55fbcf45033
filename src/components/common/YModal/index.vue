<template>
    <a-modal
        v-model:open="state.open"
        :maskClosable="false"
        :destroyOnClose="true"
        :centered="true"
        v-bind="$attrs"
        @cancel="cancel"
        @ok="confirm"
    >
        <div class="y-modal-container">
            <slot></slot>
        </div>
        <template #footer v-if="isFooterSlot">
            <slot name="footer"></slot>
        </template>
    </a-modal>
</template>

<script setup>
const slots = useSlots()

// *********************
// Hooks Function
// *********************
const props = defineProps({
    open: {
        type: Boolean,
        default: false,
    },
})

const emit = defineEmits(['update:open', 'cancel', 'confirm'])

const state = reactive({
    open: false,
})

// 是否有自定义插槽
const isFooterSlot = computed(() => {
    return slots.footer
})
// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

const cancel = () => {
    state.open = false
    emit('update:open', state.open)
    emit('cancel')
}

const confirm = () => {
    emit('confirm')
}

// *********************
// Watch Function
// *********************

watch(
    () => props.open,
    open => {
        state.open = open
    },
    {
        immediate: true,
        deep: true,
    },
)
</script>

<style lang="less" scoped>
.y-modal-container {
    overflow-y: auto;
    max-height: 606px;
}
</style>
