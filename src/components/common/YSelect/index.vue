<template>
  <a-modal
    class="mSelect"
    :maskClosable="false"
    :open="visible"
    title="选择"
    width="688px"
    v-bind="$attrs"
    @cancel="onCancel"
    @ok="onOk"
  >
    <div class="mSelect-wrap">
      <div class="section">
        <!-- 搜索 -->
        <a-input-search
          v-model:value.trim="state.name"
          placeholder="请输入搜索的内容"
          @search="handerSearch"
          allow-clear
        />

        <div class="select-wrap">
          <!-- 分类 -->
          <div class="tabs">
            <a-radio-group
              v-model:value="activeTabIndex"
              button-style="solid"
              @change="onTabsChange"
            >
              <a-radio-button
                v-for="(item, index) in tabs"
                :key="index"
                :value="index"
              >
                {{ item.tab }}
              </a-radio-button>
            </a-radio-group>
          </div>

          <!-- 面包屑 -->
          <a-breadcrumb>
            <template #separator>
              <a-avatar
                shape="square"
                src="@/assets/images/library/icon-arrow.png"
                :size="20"
              ></a-avatar>
            </template>
            <template
              v-for="(item, index) in state.breadcrumbs"
              :key="item[fieldNames.value]"
            >
              <a-breadcrumb-item
                href=""
                @click="handleBreadcrumb(item, index)"
                >{{ item[fieldNames.label] }}</a-breadcrumb-item
              >
            </template>
          </a-breadcrumb>

          <!-- 源数据 -->
          <div class="structures">
            <div class="row">
              <a-checkbox
                :checked="checkAll"
                :disabled="
                  state.dataSource.some((item) => !item.choosable) ||
                  activeTab.single
                "
                @change="onCheckAllChange"
                >全选</a-checkbox
              >
            </div>

            <a-spin :spinning="state.spinning">
              <component
                v-model:value="originCheckedList"
                style="width: 100%"
                :is="activeTab.single ? ARadio.Group : ACheckbox.Group"
              >
                <div
                  class="row"
                  v-for="(item, index) in state.dataSource"
                  :key="item.key"
                >
                  <component
                    class="check"
                    :class="{ 'check-visible': !item.choosable }"
                    :value="item[fieldNames.value]"
                    :is="activeTab.single ? ARadio : ACheckbox"
                    :disabled="item.disabled"
                    name="check"
                    @change="onCheckChange($event, item)"
                  >
                    <a-avatar
                      shape="square"
                      :src="
                        (item.rowType !== 1 &&
                          '@/assets/images/library/icon-structure.png') ||
                        ''
                      "
                      :size="36"
                    >
                      {{ item[fieldNames.label].slice(0, 1) }}
                    </a-avatar>
                    <div class="cnt">
                      <slot name="label" :row="item" :index="index">
                        <div
                          class="label ellipsis"
                          :title="item[fieldNames.label]"
                        >
                          {{ item[fieldNames.label] }}
                        </div>
                        <!-- <div class="sub ellipsis">三年级-一班-老师</div> -->
                      </slot>
                    </div>
                  </component>
                  <div class="more" v-if="item.isSub" @click="handleMore(item)">
                    下级
                  </div>
                </div>
              </component>
            </a-spin>
          </div>
        </div>
      </div>

      <div class="section section-selected">
        <div class="selected-hd">
          <div class="label">
            已选<span class="count">{{ targetCheckedList?.length || 0 }}</span
            >人
          </div>
          <div class="btn-clear" @click="handleClear">清空</div>
        </div>
        <div class="selected-bd">
          <!-- 已选择数据 -->
          <div class="selected-list" v-if="targetCheckedList?.length">
            <div
              class="selected-item"
              v-for="(item, index) in targetCheckedList"
              :key="item[fieldNames.value]"
            >
              <a-avatar
                shape="square"
                :src="(item.rowType !== 1 && '/image/icon-structure.png') || ''"
                :size="24"
              >
                {{ item[fieldNames.label].slice(0, 1) }}
              </a-avatar>
              <div class="cnt">
                <slot name="label" :row="item" :index="index">
                  <div class="label ellipsis" :title="item[fieldNames.label]">
                    {{ item[fieldNames.label] }}
                  </div>
                </slot>
              </div>
              <div class="icon-del" @click="handleDelete(index)"></div>
            </div>
          </div>
          <div class="empty" v-else>
            <img
              class="empty-img"
              src="@/assets/images/library/empty.png"
              alt
            />
            <p>暂无已选信息</p>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { Checkbox as ACheckbox, Radio as ARadio } from "ant-design-vue";

const props = defineProps({
  // 组件显隐
  visible: Boolean,
  // 查询类型 key：
  // 1.学籍
  // 2.部门
  // 3.角色
  // 业务类型 businessType:
  // * 学籍：  10选班级   11选学生  12选家长
  // * 部门：  20选部门   21选老师
  // * 角色：  30选角色   31.选老师
  // 单选多选 single
  // 应用 code，没有可为null
  // 默认Tab checked
  tabs: {
    type: Array,
    default: () => [
      {
        tab: "宿舍",
        key: 10,
        businessType: 70,
        code: null,
        single: false,
        checked: true,
      },
    ],
  },
  // 回选
  selected: {
    type: Array,
    default: () => [],
  },
  // 键值别名
  fieldNames: {
    type: Object,
    default: () => {
      return { label: "name", value: "id" };
    },
  },
  // 组件异步数据源
  operation: {
    type: Object,
    default: () => {
      return {
        /**
         * @desc 自定义数据源
         * @return Promise<[{id, name, choosable, isSub, rowType}]>
         *      id 唯一值
         *      name 名称
         *      choosable 可选地
         *      siSub 是否有子集
         *      rowType 人员 1
         *      disabled 禁选
         */
        // async dataInit () {},
        // 自定义搜索 同上
        // async search () {}
      };
    },
  },
});

const emit = defineEmits(["update:visible", "confirm"]);

const state = reactive({
  spinning: false,
  // 业务类型
  type: props.tabs.find((item) => item.checked)?.key || props.tabs[0]?.key,
  // 面包屑
  breadcrumbs: [],
  // 源数据
  dataSource: [],
});

// 当前选人业务
const activeTabIndex = ref(props.tabs.findIndex((item) => item.checked) || 0);
const activeTab = computed(() => {
  return props.tabs[activeTabIndex.value];
});

// 左侧选中
const originCheckedList = computed(() => {
  return activeTab.value.single
    ? targetCheckedList.value[0]?.id
    : targetCheckedList.value.map((item) => item.id);
});

// 右侧选中
const targetCheckedList = ref(props.selected || []);

// 搜索数据
const getSearchList = async (name, tab) => {
  if (props.operation.search) {
    return props.operation.search(name, tab);
  }
  return axios
    .post("/app/v3/tree/selectTree/search", {
      searchKey: name,
      treeType: tab.key || 1,
      businessType: tab.businessType,
      code: tab.code,
      isRule: "true",
      pageNo: 1,
      pageSize: 100,
    })
    .then((res) => {
      return res.data?.list?.map(handleConvertItem);
    });
};

// 搜索
const handerSearch = () => {
  if (!state.name) {
    state.dataSource = _dataSource;
    state.breadcrumbs =
      (_breadcrumbs.length && _breadcrumbs) || state.breadcrumbs;
    return;
  }
  state.spinning = true;
  getSearchList(state.name, activeTab.value)
    .then((list) => {
      if (!_breadcrumbs.length) {
        _breadcrumbs = toRaw(state.breadcrumbs);
      }
      state.breadcrumbs = state.breadcrumbs.slice(0, 1);
      state.dataSource = list || [];
    })
    .finally(() => {
      state.spinning = false;
    });
};

// 业务类型和数据类型对，用于判断选择的目标是哪一级
const businessTypesMap = {
  10: "classes",
  11: "student",
  12: "eltern",
  20: "dept",
  21: "dept_employee",
  30: "role",
  31: "role_employee",
};

// 人员类型，用于判断是组织架构还是人员
const personTypes = ["student", "eltern", "dept_employee", "role_employee"];

// 过滤或补充部分字段
const handleConvertItem = (item) => {
  if (item.typeValue === businessTypesMap[activeTab.value.businessType]) {
    item.choosable = true;
  }
  if (personTypes.includes(item.typeValue)) {
    item.rowType = 1;
  }
  return item;
};

// 默认数据源
const getSelectTree = (row = {}, index = 0, tab = {}) => {
  return axios.post("/cloud/v3/tree/selectTree", {
    treeType: tab.key || 1,
    pid: row.id || 0,
    businessType: tab.businessType,
    code: tab.code,
    isRule: "true",
  });
};

// 获取数据
const dataInit = async (row = {}, index = 0) => {
  if (props.operation?.dataInit) {
    return props.operation?.dataInit(row, index, activeTab.value);
  }
  return getSelectTree(row, index, activeTab.value).then((res) => {
    return res.data?.map(handleConvertItem);
  });
};

// 列表数据备份
let _dataSource = [];
// 面包屑备份
let _breadcrumbs = [];

// 面包屑点击
const handleBreadcrumb = (row = {}, index = 0) => {
  state.spinning = true;
  return dataInit(row, index)
    .then((data) => {
      state.breadcrumbs = state.breadcrumbs.slice(0, index + 1);
      state.dataSource = data || [];
      _dataSource = data || [];
      return state.dataSource;
    })
    .finally(() => {
      state.spinning = false;
    });
};

// 获取初始化数据
handleBreadcrumb();

// tab切换
const onTabsChange = () => {
  state.breadcrumbs = [
    {
      name: activeTab.value?.tab,
      id: undefined,
    },
  ];
  handleBreadcrumb();
};

// 下级
const handleMore = (row) => {
  handleBreadcrumb(row, state.breadcrumbs.length).then(() => {
    state.breadcrumbs.push(row);
  });
};

// 全选
const checkAll = computed(() => {
  if (activeTab.value.single) {
    return false;
  }
  let choosableList = state.dataSource.filter((item) => item.choosable);
  return (
    !!choosableList.length &&
    choosableList.every((item) => originCheckedList.value.indexOf(item.id) > -1)
  );
});
const onCheckAllChange = (e) => {
  let choosableList = state.dataSource.filter((item) => item.choosable);
  if (e.target.checked) {
    choosableList.forEach((item) => {
      let index = targetCheckedList.value.findIndex((selectedItem) => {
        return (
          item[props.fieldNames.value] == selectedItem[props.fieldNames.value]
        );
      });
      index === -1 && targetCheckedList.value.push(item);
    });
  } else {
    choosableList.forEach((item) => {
      let index = targetCheckedList.value.findIndex(
        (item) => item[props.fieldNames.value] == item.id
      );
      index > -1 && targetCheckedList.value.splice(index, 1);
    });
  }
};

// 单选
const onCheckChange = (e, row) => {
  if (e.target.checked) {
    activeTab.value.single && (targetCheckedList.value = []);
    targetCheckedList.value.push(row);
  } else {
    let index = targetCheckedList.value.findIndex((item) => {
      return item[props.fieldNames.value] == row[props.fieldNames.value];
    });
    index > -1 && targetCheckedList.value.splice(index, 1);
  }
};

// 清空
const handleClear = () => {
  targetCheckedList.value = [];
};

// 删除
const handleDelete = (index) => {
  targetCheckedList.value.splice(index, 1);
};

// 取消
const onCancel = () => {
  emit("update:visible", false);
};

// 确认
const onOk = () => {
  emit("confirm", toRaw(targetCheckedList.value));
  emit("update:visible", false);
};
</script>

<style lang="less">
.mSelect {
  .ant-modal-body {
    padding: 0;
  }
  .ant-modal-footer {
    padding: 15px 24px;
  }
}
</style>

<style lang="less" scoped>
@import url("./index.less");
</style>
