<template>
    <a-drawer
        :width="width"
        :closable="false"
        v-model:open="drawerOpen"
        :maskClosable="false"
        :keyboard="false"
        :mask="false"
        rootClassName="YDrawer"
        :destroyOnClose="true"
        :rootStyle="rootStyle"
        v-bind="$attrs"
        @close="close"
    >
        <template #title>
            <div class="drawer_title">
                <div>
                    <ArrowLeftOutlined @click="close" :style="{ color: store.sysColor, fontSize: '16px' }" />
                    <span class="ml-10">{{ title }}</span>
                </div>
                <slot name="title"></slot>
            </div>
        </template>
        <template #footer>
            <slot name="footer"></slot>
        </template>
        <slot></slot>
    </a-drawer>
</template>
<script setup>
const urlParams = reactive({ ...getUrlParams() })
defineOptions({
    inheritAttrs: false,
})

const props = defineProps({
    open: {
        type: Boolean,
        default: false,
    },
    title: {
        type: String,
        default: '',
    },
    rootStyle: {
        type: Object,
        default: () => {},
    },
    backMessage: {
        type: String,
        default: '',
    },
})

const rootStyle = computed(() => {
    // 是否是ifram嵌套
    const isIfram = !!urlParams.sysCode
    const height = isIfram ? '100vh' : 'calc(100vh - 90px)'
    const top = isIfram ? '0px' : '90px'
    return {
        height,
        top,
        ...props.rootStyle,
    }
})

const store = useTypeStore()
const width = computed({
    get: () => {
        if (urlParams.sysCode) {
            return '100vw'
        } else {
            return `calc(100% - ${store.collapsed ? '87px' : '215px'} )`
        }
    },
})

const emit = defineEmits(['update:open', 'closebtn'])

let drawerOpen = ref(false)
watch(
    () => props.open,
    val => {
        drawerOpen.value = val
    },
    {
        immediate: true,
        deep: true,
    },
)

const close = async () => {
    drawerOpen.value = false
    emit('update:open', drawerOpen.value)
    emit('closebtn') // 触发外部的close事件
}
</script>
<style lang="less">
.YDrawer {
    outline: none;
}
.YDrawer .ant-drawer-content-wrapper {
    box-shadow: none;
    .ant-drawer-header {
        padding-top: 0px;
        padding-bottom: 0px;
    }
    .ant-drawer-header {
        border-bottom: none;
    }
    .drawer_title {
        height: 58px;
        padding-left: 24px;
        margin-top: -4px;
        margin-left: -24px;
        margin-right: -24px;
        font-size: 16px;
        color: #000;
        font-weight: 600;
        border-bottom: 1px solid #d9d9d9;
        display: flex;
        align-items: center;
        flex: 1;
    }
}
.YDrawer {
    outline: none;
}
</style>
