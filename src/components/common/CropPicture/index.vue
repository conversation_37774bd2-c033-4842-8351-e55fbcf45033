<template>
    <div>
        <a-upload
            :disabled="disableds"
            v-model:file-list="state.fileList"
            name="file"
            list-type="picture-card"
            :class="className"
            :show-upload-list="false"
            action="/"
            :accept="accept"
            :before-upload="beforeUpload"
            @change="handleChange"
        >
            <div v-if="value" class="upload-avatar">{{ title }}</div>
            <img v-if="value" :src="value" style="height: auto; width: 100%; display: block" alt="logo" />
            <div
                v-else
                style="
                    width: 100%;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    /* background: #eeeeee69; */
                    font-size: 12px;
                    border-radius: 50%;
                    color: #0000008a;
                "
            >
                <loading-outlined v-if="state.loading"></loading-outlined>
                <plus-outlined v-else></plus-outlined>
                <div class="ant-upload-text">{{ state.loading ? '正在' : '点击' }}{{ props.uploadText }}</div>
            </div>
            <slot name="parents" />
        </a-upload>

        <a-modal title="上传图片" v-model:open="state.isShowDialog" width="900px">
            <div class="cropper-warp">
                <div class="cropper-warp-left" :key="state.cropperImg">
                    <img :src="state.cropperImg" class="cropper-warp-left-img" />
                </div>
                <div class="cropper-warp-right">
                    <div class="cropper-warp-right-box" :class="{ acitve: props.aspectRatio == 1 }">
                        <div class="cropper-warp-right-title">预览</div>
                        <div class="cropper-warp-right-item">
                            <div class="cropper-warp-right-value">
                                <img :src="state.cropperImgBase64" :key="state.cropperImgBase64" class="cropper-warp-right-value-img" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <a-button @click="onCancel">取 消</a-button>
                    <a-button type="primary" @click="onSubmit">确 定</a-button>
                </span>
            </template>
        </a-modal>
    </div>
</template>

<script setup>
import { reactive, nextTick, watch, toRaw } from 'vue'
import { message } from 'ant-design-vue'
import Cropper from 'cropperjs'
import 'cropperjs/dist/cropper.css'
const emit = defineEmits(['change', 'update:value'])

const props = defineProps({
    value: {
        type: String,
        default: '',
    },
    title: {
        type: String,
        default: '重新上传头像',
    },
    // 上传图片按钮文案
    uploadText: {
        type: String,
        default: '上传图片',
    },

    accept: {
        type: String,
        default: 'image/*',
    },
    disableds: {
        type: Boolean,
        default: false,
    },
    aspectRatio: {
        type: Number,
        default: 1,
    },
    cropBoxResizable: {
        type: Boolean,
        default: false,
    },
    minCropBoxWidth: {
        type: Number,
        default: 200,
    },
    minCropBoxHeight: {
        type: Number,
        default: 200,
    },
    className: {
        type: String,
        default: 'logo_uploader',
    },
    isNum: {
        type: Number,
        default: 2,
    },
})
const state = reactive({
    fileList: [],
    loading: false,
    // imageUrl: '',
    isShowDialog: false,
    cropperImgBase64: '',
    cropperImg: '',
})

function getBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => resolve(reader.result)
        reader.onerror = error => reject(error)
    })
}
// 将base64转换为blob
function dataURLtoBlob(dataurl) {
    const arr = dataurl.split(',')
    const mime = arr[0].match(/:(.*?);/)[1]
    const bstr = window.atob(arr[1])
    let n = bstr.length
    const u8arr = new Uint8Array(n)
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
    }
    return new Blob([u8arr], { type: mime })
}
// 将blob转换为file
function blobToFile(theBlob, fileName) {
    theBlob.lastModifiedDate = new Date()
    theBlob.name = fileName
    return theBlob
}
watch(
    () => {
        return props.value
    },
    v => {
        if (v) {
            state.loading = false
        }
        emit('update:value', v)
    },
)

// 打开弹窗
const openDialog = imgs => {
    state.cropperImg = imgs
    state.isShowDialog = true
    nextTick(() => {
        initCropper()
    })
}

const onCancel = () => {
    state.isShowDialog = false
}

const resetCropper = () => {
    state.cropperImg = ''
    state.cropperImgBase64 = ''
}

const handleChange = async info => {
    const isJpgOrPng = ['image/jpeg', 'image/webp', 'image/jpg', 'image/png']
    if (!isJpgOrPng.includes(info.file.type)) {
        state.acceptLload = true
        message.error('只能上传jpg，png格式!')
        return
    }
    const isLt2M = Number(info.file.size / 1024 / 1024)
    if (isLt2M > props.isNum) {
        message.error('图像必须小于' + props.isNum + 'MB！')
        return
    }
    resetCropper()
    try {
        const base64Img = await getBase64(info.file)
        state.cropperImg = base64Img
        openDialog(base64Img)
    } catch (error) {
        console.log(error)
    }
}

const beforeUpload = (file, fileList) => {
    // const isLt2M = file.size / 1024 / 1024 < 2

    // if (!isLt2M) {
    //     message.error('图像必须小于2MB！')
    //     state.isShowDialog = false
    //     state.fileList = []
    // }
    return false
}

function dataURLtoFile(dataurl, filename) {
    const arr = dataurl.split(',')
    const mime = arr[0].match(/:(.*?);/)[1]
    const bstr = atob(arr[1])
    let n = bstr.length
    const u8arr = new Uint8Array(n)
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
    }
    return new File([u8arr], filename, { type: mime })
}

const onSubmit = () => {
    state.isShowDialog = false
    state.loading = true
    const blob = dataURLtoBlob(state.cropperImgBase64)
    const file = dataURLtoFile(toRaw(state.cropperImgBase64), 'image.png')
    const params = {
        file: blob,
    }
    emit('change', { file, base64: toRaw(state.cropperImgBase64) })

    // state.imageUrl = state.cropperImgBase64;
}

const initCropper = () => {
    const letImg = document.querySelector('.cropper-warp-left-img')
    const cropper = new Cropper(letImg, {
        viewMode: 1,
        dragMode: 'move',
        initialAspectRatio: 1,
        preview: '.before',
        autoCropArea: 0,
        zoomOnWheel: true,
        aspectRatio: props.aspectRatio,
        cropBoxResizable: props.cropBoxResizable, // 是否可以改变裁剪框的尺寸
        minCropBoxWidth: props.minCropBoxWidth,
        minCropBoxHeight: props.minCropBoxHeight,

        crop: () => {
            state.cropperImgBase64 = cropper
                .getCroppedCanvas({
                    imageSmoothingQuality: 'low',
                })
                .toDataURL('image/jpeg')
        },
    })
}
</script>

<style scoped lang="less">
.cropper-warp {
    display: flex;

    .cropper-warp-left {
        position: relative;
        display: inline-block;
        height: 350px;
        flex: 1;
        border: 1px solid #ebeef5;
        background: @body-background;
        overflow: hidden;
        background-repeat: no-repeat;
        cursor: move;
        border-radius: 3px;

        .cropper-warp-left-img {
            width: auto;
            height: 100%;
            border: 1px solid #f0f0f0;
        }
    }

    .cropper-warp-right {
        width: 300px;
        height: 350px;
        margin-left: 20px;
        position: relative;

        .cropper-warp-right-box {
            position: absolute;
            top: 50%;
            margin-top: -125px;

            &.acitve {
                left: 50%;
                margin-left: -100px;

                .cropper-warp-right-value-img {
                    width: 100% !important;
                }
            }
        }

        .cropper-warp-right-title {
            text-align: center;
            height: 20px;
            line-height: 20px;
        }

        .cropper-warp-right-item {
            margin: 15px 0;

            .cropper-warp-right-value {
                display: flex;

                .cropper-warp-right-value-img {
                    width: 300px; // 优化版
                    height: 200px;
                    border-radius: 4px;
                    margin: auto;
                    object-fit: scale-down;
                }
            }
        }
    }
}

.upload-avatar {
    display: none;
    height: 104px;
    width: 104px;
    border-radius: 5px;
    background: rgba(0, 0, 0, 0.5);

    line-height: 104px;
    text-align: center;
    color: @body-background;
    position: absolute;
    top: 0;
    left: 0;
}
:deep(.ant-upload) {
    background: @gray-background;
    &:hover .upload-avatar {
        display: block;
    }
}
</style>
