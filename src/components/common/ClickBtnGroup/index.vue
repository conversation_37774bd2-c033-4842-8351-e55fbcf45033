<template>
    <a-button
        v-for="item in btnList"
        :key="item.key"
        @click="item.method"
        :width="item.width || 'auto'"
        v-bind="item.attrs || {}"
        v-auth="item.auth"
    >
        <template #icon v-if="item.icon">
            <component :is="item.icon"></component>
        </template>
        {{ item.label }}
    </a-button>
    <slot></slot>
</template>
<script setup>
const props = defineProps({
    btnList: {
        type: Array,
        default: [],
    },
})
</script>
<style lang="less" scoped></style>
