<template>
    <a-tooltip class="_tooltip" :title="props.title" :overlayStyle="state.overlayStyle" @mouseenter="showToolTip">
        <span class="tooltip_span">{{ props.title }}</span>
    </a-tooltip>
</template>

<script setup>
const props = defineProps({
    title: {
        type: String,
        default: '',
    },
    maxWidth: {
        type: Number,
        default: 0,
    },
})
const state = reactive({
    overlayStyle: {
        color: '#000',
        maxWidth: props.maxWidth ? props.maxWidth + 'px' : 'initial',
    },
})
// 控制Tooltip显隐
const showToolTip = e => {
    if (e.target.clientWidth >= e.target.scrollWidth) {
        e.target.style.pointerEvents = 'none'
        // 阻止鼠标事件
    }
}
</script>

<style scoped lang="less" src="./style.less"></style>
