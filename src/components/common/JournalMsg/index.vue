<template>
    <a-modal centered v-model:open="show" :maskClosable="false" @cancel="show = false" :width="900" title="详情" :footer="null">
        <div p-24 flex>
            <a-row>
                <a-col :span="8" class="yd_col" :title="formData.bookIsbn">ISSN：{{ formData.bookIsbn }}</a-col>
                <a-col :span="8" class="yd_col" :title="formData.bookIsbn">邮发代号：{{ formData.bookIsbn }}</a-col>
                <a-col :span="8" class="yd_col" :title="formData.bookIsbn">CN：{{ formData.bookIsbn }}</a-col>
                <a-col :span="8" class="yd_col" :title="formData.title">报刊题名：{{ formData.title }}</a-col>
                <a-col :span="8" class="yd_col" :title="formData.publicationAddress">主办单位：{{ formData.publicationAddress }}</a-col>
                <a-col :span="8" class="yd_col" :title="formData.publisherName">出版单位：{{ formData.publisherName }}</a-col>
                <a-col :span="8" class="yd_col" :title="formData.categoryCode">分类号：{{ formData.categoryCode }}</a-col>
                <a-col :span="8" class="yd_col" :title="formData.subTitle">发行周期：{{ formData.releaseCycleName }}</a-col>
                <a-col :span="8" class="yd_col" :title="formData.firstAuthor">主编：{{ formData.firstAuthor }}</a-col>
                <a-col :span="8" class="yd_col" :title="formData.publicationTime">征订年：{{ formData.publicationTime }}</a-col>
                <a-col :span="8" class="yd_col" :title="formData.fixedPrice">总定价：{{ formData.fixedPrice }}</a-col>
                <a-col :span="8" class="yd_col" :title="formData.classNumber">种次号：{{ formData.classNumber }}</a-col>
                <a-col :span="8" class="yd_col" :title="formData.subTitle">卷册期数：{{ formData.volume }}</a-col>
                <a-col :span="8" class="yd_col" :title="formData.parallelTitle">索书号：{{ formData.callNumber }}</a-col>
                <a-col :span="24" class="yd_textarea" :title="formData.description">简介：{{ formData.description }}</a-col>
                <!-- isFooter 是否有页脚 -->
                <a-col :span="24" class="yd_more" v-if="isFooter">
                    <div class="yd_more_item">条码号：{{ formData.barCode }}</div>
                    <div class="yd_more_item">入库日期：{{ formData.registerTime }}</div>
                    <div class="yd_more_item">馆藏状态：{{ itemStatus(formData.itemStatus) }}</div>
                    <div class="yd_more_item">馆藏地：{{ formData.locationName }}</div>
                    <div class="yd_more_item">排架名：{{ formData.shelfName }}</div>
                    <div class="yd_more_item">层架名：{{ formData.layerFrameName }}</div>
                </a-col>
            </a-row>
        </div>
    </a-modal>
</template>
<script setup>
const show = ref(false)
const formData = ref({})
const itemStatusList = ref([])
const isFooter = ref(false)

// footer：true：条码号点击进来（有详情），false：ISSN点击进来(无详情)
const open = (item, footer) => {
    dictFn()
    show.value = true
    formData.value = item
    isFooter.value = footer
}

const itemStatus = computed(() => {
    return val => {
        const item = itemStatusList.value.find(i => i.value === val) || {}
        return item.label
    }
})
const dictFn = async () => {
    itemStatusList.value = (await getDict('lib_item_status')) || []
}

defineExpose({ open })
</script>
<style lang="less" scoped>
.img {
    width: 100%;
    height: 100%;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    overflow: hidden;
}
.yd_col {
    margin-bottom: 24px;
    padding-right: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.yd_textarea {
    max-height: 177px;
    overflow-y: auto;
}
.yd_more {
    border-top: 1px dashed #d9d9d9;
    margin-top: 34px;
    padding-top: 22px;
    display: flex;
    flex-wrap: wrap;
    .yd_more_item {
        width: 32%;
        padding: 14px 0px;
    }
}
</style>
