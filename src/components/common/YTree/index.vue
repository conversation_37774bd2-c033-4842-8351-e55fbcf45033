<template>
    <div class="y_tree">
        <slot name="handleHander"></slot>
        <a-tree
            v-if="treeData.length"
            :checkable="checkable"
            :defaultExpandAll="defaultExpandAll"
            v-bind="$attrs"
            :tree-data="treeData"
            showIcon
            checkStrictly
            :selectable="true"
            blockNode
            @select="select"
        >
            <template #title="{ dataRef }">
                <slot name="treeIcon" :treeItem="{ id: dataRef.id, name: dataRef.name }">
                    <i class="iconfont icon-yellow" :class="treeIcon"></i>
                </slot>
                <Tooltip :title="dataRef.name"></Tooltip>
                <slot name="handleItem" :key="dataRef.id" :handleItem="dataRef"></slot>
            </template>
        </a-tree>
        <slot name="handleFooter"></slot>

        <a-empty
            v-if="!treeData.length && isShowEmpty"
            :description="emptyTitle"
            :image-style="{ width: '100%', height: '180px', marginTop: '50%' }"
        ></a-empty>
    </div>
</template>

<script setup>
// *********************
// Hooks
// *********************

const props = defineProps({
    treeIcon: {
        type: String,
        default: 'icon-a-Fill21',
    },
    defaultExpandAll: {
        type: Boolean,
        default: true,
    },
    isDraggable: {
        type: Boolean,
        default: false,
    },
    isShowEmpty: {
        type: Boolean,
        default: true,
    },
    treeData: {
        type: Array,
        default: () => [],
    },
    emptyTitle: {
        type: String,
        default: '暂无数据',
    },
    checkable: {
        // 勾选框
        type: Boolean,
        default: false,
    },
})
const emit = defineEmits(['emitSelect', 'update:selectedKeys'])

// *********************
// Service Function
// *********************

const select = (selectedKeys, item) => {
    if (item.node.id) {
        emit('update:selectedKeys', [item.node.id])
        emit('emitSelect', item.node.id, item.node)
    }
}
</script>

<style scoped lang="less">
:deep(.ant-tree-list-holder-inner) {
    .ant-tree-treenode {
        width: 100%;
        padding: 5px 0;
    }

    .icon-yellow {
        color: @warning-color;
        padding-right: 6px;
        font-size: 14px;
    }
    .ant-tree-treenode-selected {
        background-color: @acitve-background !important;

        .ant-tree-node-selected {
            color: var(--primary-color);

            .icon-yellow {
                color: var(--primary-color);
            }

            .handle_icon {
                display: inline-block;
            }
        }
    }

    .ant-tree-node-content-wrapper {
        background-color: transparent;
        display: flex;
        flex: 1;
    }
}
</style>
