<template>
    <a-modal centered v-model:open="open" :maskClosable="false" @cancel="cancle" :width="1140" title="图书信息" :footer="null">
        <div p-24 flex>
            <div w-160 flex-shrink-0 mr-40>
                <div class="books_template_img">
                    <img v-if="!formData.url" src="@/assets/images/library/books_template.png" />
                    <div class="image_box" v-else>
                        <img :src="formData.url" />
                    </div>
                    <span v-if="!formData.url" class="title">{{ formData?.title }}</span>
                </div>
            </div>
            <div flex-1 style="overflow: hidden">
                <a-row>
                    <a-col :span="12" class="yd_col" :title="formData.bookIsbn">ISBN：{{ formData.bookIsbn }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.categoryCode">分类号：{{ formData.categoryCode }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.fixedPrice">定价：{{ formData.fixedPrice }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.title">正题名：{{ formData.title }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.subTitle">副题名：{{ formData.subTitle }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.parallelTitle">并列题名：{{ formData.parallelTitle }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.volumeNumber">分辑号：{{ formData.volumeNumber }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.volumeName">分辑名：{{ formData.volumeName }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.subjectWords">主题词：{{ formData.subjectWords }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.callNumber">索书号：{{ formData.callNumber }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.firstAuthor">第一责任者：{{ formData.firstAuthor }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.otherAuthor">其他责任者：{{ formData.otherAuthor }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.seriesName">丛编题名：{{ formData.seriesName }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.seriesAuthor">丛编责任者：{{ formData.seriesAuthor }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.publicationAddress">出版地：{{ formData.publicationAddress }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.publisherName">出版社：{{ formData.publisherName }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.publicationTime">出版年月：{{ formData.publicationTime }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.catalogueTime">编目日期：{{ formData.catalogueTime }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.bookFormat">开本：{{ formData.bookFormat }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.version">版本：{{ formData.version }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.worldLanguageCode">语种码：{{ formData.worldLanguageCode }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.totalPages">页码：{{ formData.totalPages }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.callNumber">索书号：{{ formData.callNumber }}</a-col>
                    <a-col :span="12" class="yd_col" :title="formData.classNumber">种次号：{{ formData.classNumber }}</a-col>
                    <a-col :span="24" class="yd_textarea" :title="formData.description">简介：{{ formData.description }}</a-col>
                    <a-col :span="24" class="yd_more" v-if="!isBooks">
                        <div class="yd_more_item">条码号：{{ formData.barCode }}</div>
                        <div class="yd_more_item">入库日期：{{ formData.registerTime }}</div>
                        <div class="yd_more_item">馆藏状态：{{ itemStatus(formData.itemStatus) }}</div>
                        <div class="yd_more_item">馆藏地：{{ formData.locationName }}</div>
                        <div class="yd_more_item">排架名：{{ formData.shelfName }}</div>
                        <div class="yd_more_item">层架名：{{ formData.layerFrameName }}</div>
                    </a-col>
                </a-row>
            </div>
        </div>
        <!-- <footer>
            <slot name="footer" :record="formData"></slot>
        </footer> -->
    </a-modal>
</template>
<script setup>
const emit = defineEmits(['close'])
const labelList = { 4: '注销', 5: '下架', 6: '剔除' }
const itemStatusList = ref([])
const props = defineProps({
    open: {
        type: Boolean,
        default: false,
    },
    formData: {
        type: Object,
        default: () => {},
    },
    more: {
        type: Boolean,
        default: false,
    },
    // isBooks：true：条码号点击进来（有详情），false：ISBN点击进来
    isBooks: {
        type: Boolean,
        default: false,
    },
})
const open = computed({
    get: () => {
        return props.open
    },
    set: () => {},
})
const formData = computed({
    get: () => {
        return props.formData
    },
    set: () => {},
})

const itemStatus = computed(() => {
    return val => {
        const item = itemStatusList.value.find(i => i.value === val) || {}
        return item.label
    }
})
const dictFn = async () => {
    itemStatusList.value = (await getDict('lib_item_status')) || []
}
function cancle() {
    emit('close')
}

dictFn()
</script>
<style lang="less" scoped>
.img {
    width: 100%;
    height: 100%;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    overflow: hidden;
}
.yd_col {
    margin-bottom: 24px;
    padding-right: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.yd_textarea {
    max-height: 177px;
    overflow-y: auto;
}
.yd_more {
    border-top: 1px dashed #d9d9d9;
    margin-top: 34px;
    padding-top: 22px;
    display: flex;
    flex-wrap: wrap;
    .yd_more_item {
        width: 32%;
        padding: 14px 0px;
    }
}
.books_template_img {
    width: 110px;
    max-height: 160px;
    position: relative;
    margin-right: 40px;
    img {
        width: 100%;
        height: 100%;
    }
    .image_box {
        width: 110px;
        max-height: 160px;
        overflow-y: hidden;
        border-radius: 4px;
        border: 1px solid #d9d9d9;
        align-items: center;
        display: flex;
        justify-content: center;
        img {
            width: 100%;
            height: auto;
        }
    }
    .title {
        position: absolute;
        height: 139px;
        width: 70px;
        top: 12px;
        left: 33px;
        font-size: 14px;
        font-weight: 500;
        color: rgba(51, 51, 51, 0.85);
        line-height: 20px;
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 7;
        line-clamp: 7;
        -webkit-box-orient: vertical;
        hyphens: auto;
    }
}
</style>
