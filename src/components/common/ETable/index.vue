<!--
 * @Author: AKclown
 * @Date: 2023-12-09 
 * @example  插槽的写法  （查看components.usage.md文件）
 * bodyCell插槽: #<dataIndex> 。例如#name
 * headerCell/customFilterIcon/customFilterDropdown插槽(存在dataIndex的插槽): #<slotName>-<dataIndex>  例如#headerCell-name
 * 其余插槽: #<slotName>-slot 。例如#summary-slot
 * 在线演示:  https://codesandbox.io/p/devbox/etable-demo-zyf792
-->
<template>
    <div class="a-table-outside-box" :style="{ 'min-height': minH + 'px' }">
        <a-table :rowKey="rowKey" bordered :pagination="false" v-bind="$attrs" :columns="columns" @resizeColumn="handleResizeColumn">
            <template v-for="slotName in groupSlotNames" #[slotName]="record" :key="slotName">
                <template v-if="slotName === 'headerCell' && record.column.dataIndex === 'index' && colSetting">
                    <div class="serial-container">
                        <Setting :list="settingColumns" :hash="tableHash" @update="updateColumns" />
                        <span>{{ record.title }}</span>
                    </div>
                </template>
                <slot v-else :name="innerSlotName(slotName, record)" v-bind="record"></slot>
            </template>
        </a-table>
    </div>
    <Pagination :total="total" @paginationChange="paginationChange" :current="props.current"></Pagination>
</template>
<script setup>
import Throttle from './Throttle'
import Setting from './Setting.vue'

const antSlotNames = [
    'customFilterDropdown-',
    'customFilterIcon-',
    'emptyText-',
    'expandIcon-',
    'footer-',
    'headerCell-',
    'summary-',
    'title-',
]
const throttle = new Throttle()
const slots = useSlots()
const attrs = useAttrs()
const columns = ref([])
const colStore = useColStore()

const props = defineProps({
    minH: {
        type: Number,
    },
    // 分页
    total: {
        type: Number,
    },
    current: {
        type: Number,
    },
    colSetting: {
        type: Boolean,
        default: false,
    },
    rowKey: {
        type: String,
        default: 'id',
    },
})
const emit = defineEmits(['paginationChange'])

const groupSlotNames = computed(() => {
    // template语法也存在插槽如#summary
    const keys = Object.keys(slots).filter(key => key !== 'default')
    // $ 默认存在headerCell插槽，序号列表在此组件固定
    const names = new Set(['headerCell'])
    keys.forEach(key => {
        const slotName = key.split('-')[0]
        const name = antSlotNames.includes(`${slotName}-`) ? slotName : 'bodyCell'
        names.add(name)
    })
    return Array.from(names)
})

const innerSlotName = computed(() => {
    return (slotName, record) => {
        const prefix = slotName === 'bodyCell' ? '' : `${slotName}-`
        // $ 除了headerCell和bodyCell是存在dataIndex, 其余都不存在
        const dataIndex = record?.column?.dataIndex
        if (dataIndex) {
            return `${prefix}${dataIndex}`
        } else {
            // 序列号不赋值插槽名称
            const isSequence = !prefix || (prefix && ['headerCell-'].includes(prefix))
            return isSequence ? '' : `${slotName}-slot`
        }
    }
})

const settingColumns = computed(() => {
    if (attrs.columns) {
        return attrs.columns.map(i => ({ label: i.title, value: i.dataIndex }))
    } else {
        return (
            slots
                .default()
                // 过滤掉template语法上v-if为false
                .filter(i => i.children !== 'v-if')
                .map(i => ({ label: i.props.title, value: i.props['data-index'] }))
        )
    }
})

const tableHash = computed(() => {
    return window.location.hash + isEmptyStr(attrs.hash)
})

// *********************
// Service Function
// *********************

const countWidth = props => {
    const dataIndex = attrs.columns ? props.dataIndex : props['data-index']
    const key = window.location.hash + isEmptyStr(dataIndex) + isEmptyStr(attrs.hash)
    const width = colStore.get(key)
    if (width && key.indexOf('operate') === -1) {
        return width
    } else {
        const w = parseInt(props.width || 150)
        colStore.set(key, w)
        return w
    }
}
const isEmptyStr = i => {
    return i || ''
}

const changeColumns = data => {
    const antColumns = data
        // 过滤掉template上v-if为false的元素或者注释元素
        .filter(i => attrs.columns || (!attrs.columns && i.props))
        .filter(i => {
            // 过滤掉未选中的列数据
            const dataIndex = attrs.columns ? i.dataIndex : i.props['data-index']
            let checkedList = colStore.getSetting(tableHash.value)
            if (checkedList) {
                // !!! Array.isArray兼容之前历史数据为数组字符串的情况，下个版本去掉
                checkedList = Array.isArray(checkedList) ? checkedList : Object.keys(checkedList).filter(key => checkedList[key])
            }
            return !checkedList || checkedList.includes(dataIndex)
        })
        .map(i => {
            if (attrs.columns) {
                // columns数据
                const isOperate = i.dataIndex === 'operate'
                return {
                    ...i,
                    resizable: !isOperate,
                    fixed: isOperate ? 'right' : false,
                    ellipsis: true,
                    width: countWidth(i),
                }
            } else {
                // 操作列固定到右侧且不可拖动
                const dataIndex = i.props['data-index'] || null
                const isOperate = dataIndex === 'operate'
                const item = {
                    ...i.props,
                    dataIndex,
                    resizable: !isOperate,
                    fixed: isOperate ? 'right' : false,
                    ellipsis: !isOperate,
                    width: countWidth(i.props),
                }
                if (i.children?.default) {
                    item.customRender = i.children.default
                }
                return item
            }
        })
    columns.value = [
        {
            title: '序号',
            width: 80,
            dataIndex: 'index',
            ellipsis: true,
            customRender: item => `${item.index + 1}`,
        },
        ...antColumns,
    ]
}

function handleResizeColumn(w, col) {
    const key = window.location.hash + isEmptyStr(col.dataIndex) + isEmptyStr(attrs.hash)
    col.width = w
    colStore.set(key, w)
}

// 分页
const paginationChange = ({ pageNo, pageSize }) => {
    emit('paginationChange', { pageNo, pageSize })
}

const updateColumns = () => {
    const data = attrs.columns || slots.default()
    changeColumns(data)
}

// *********************
// Watch Function
// *********************
watch(
    () => attrs.columns || slots.default(),
    columns => {
        throttle.start(changeColumns, columns, 1000)
    },
    { immediate: true },
)
</script>
<style lang="less" scoped>
// https://github.com/vueComponent/ant-design-vue/issues/6485
.a-table-outside-box {
    width: 100%;
    overflow-x: auto;
}
.serial-container {
    position: relative;
}
</style>
