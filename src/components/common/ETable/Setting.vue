<template>
    <div class="setting">
        <a-popover trigger="click" placement="rightBottom">
            <template #content>
                <div class="feature">
                    <a-checkbox v-model:checked="state.checkAll" :indeterminate="state.indeterminate" @change="onCheckAllChange">
                        全选
                    </a-checkbox>
                    <a-button type="link" class="btn-link-color" @click="reset">重置</a-button>
                </div>
                <a-divider mt-10 mb-10 />
                <a-checkbox-group class="columns-group" v-model:value="state.checkedList" :options="list" @change="changeChecked" />
            </template>
            <img class="setting-icon" src="@/assets/images/admin/icon-liesz.png" />
        </a-popover>
    </div>
</template>

<script setup>
import Throttle from './Throttle'
const throttle = new Throttle()
const colStore = useColStore()

// *********************
// Hooks Function
// *********************

const props = defineProps({
    list: {
        type: Array,
        default: [],
    },
    hash: {
        type: String,
        default: '',
    },
})

const emit = defineEmits(['update'])

const state = reactive({
    checkedList: [],
    indeterminate: false,
    checkAll: false,
})

const allChecked = computed(() => props.list.map(i => i.value))

// *********************
// Life Event Function
// *********************
onMounted(() => {
    let checked = colStore.getSetting(props.hash)
    if (checked && !Array.isArray(checked)) {
        // !!! Array.isArray兼容之前历史数据为数组字符串的情况，下个版本去掉
        // 表格新增字段,默认勾选
        const keys = new Set(Object.keys(checked))
        const selected = allChecked.value.filter(i => !keys.has(i))
        for (let key in checked) {
            if (checked[key]) {
                selected.push(key)
            }
        }
        checked = selected
    } else {
        checked = checked ? checked : allChecked.value
    }
    changeChecked(checked)
})

// *********************
// Service Function
// *********************

const onCheckAllChange = e => {
    const checked = e.target.checked ? allChecked.value : []
    changeChecked(checked)
}

const reset = () => {
    changeChecked(allChecked.value)
}

const changeChecked = list => {
    throttle.start(
        val => {
            state.indeterminate = !!val.length && val.length < props.list.length
            state.checkAll = val.length === props.list.length
            state.checkedList = val
            const cacheValue = {}
            allChecked.value.forEach(i => {
                cacheValue[i] = val.includes(i)
            })
            colStore.setSetting(props.hash, cacheValue)
            emit('update')
        },
        list,
        500,
    )
}
</script>

<style lang="less" scoped>
.setting {
    position: absolute;
    top: -13px;
    left: -16px;
    cursor: pointer;
}
.feature {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.columns-group {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    max-height: 350px;
    overflow: hidden auto;
    :deep(.ant-checkbox-wrapper) {
        margin-bottom: 8px;
    }
    :deep(.ant-checkbox-wrapper-checked) {
        color: var(--primary-color);
    }
}
</style>
