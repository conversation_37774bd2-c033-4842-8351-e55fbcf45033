<template>
    <a-modal
        class="mSelect"
        :open="modelState.openVisible"
        :destroyOnClose="true"
        :maskClosable="false"
        title="选择"
        width="688px"
        @cancel="onCancel"
        @ok="onOk"
    >
        <div class="mSelect-wrap" v-if="modelState.openVisible">
            <div class="section" :class="{ active: !isShowSearch }">
                <!-- 分类 -->
                <div class="tabs">
                    <a-radio-group v-model:value="activeTabIndex" button-style="solid" @change="onTabsChange">
                        <a-radio-button v-for="(item, index) in props.tabs" :key="item.id" :value="index">
                            {{ item.tab }}
                        </a-radio-button>
                    </a-radio-group>
                </div>
                <!-- 搜索 -->
                <a-input-search
                    v-if="isShowSearch"
                    class="reset-input-search"
                    allowClear
                    v-model:value.trim="state.inputGroup.name"
                    placeholder="请输入搜索的内容"
                    @search="handleSearch"
                >
                    <template v-if="tabs[activeTabIndex].isClassify" #addonBefore>
                        <a-select v-model:value.trim="state.inputGroup.accommodation" style="width: 80px">
                            <a-select-option value="">全部</a-select-option>
                            <a-select-option v-for="it in roomTypes" :key="it.value" :value="it.value">
                                {{ it.label }}
                            </a-select-option>
                        </a-select>
                    </template>
                </a-input-search>

                <div p-18 pt-0 v-if="state.isSearchTable" :class="{ 'single-table': activeTab.single }">
                    <y-table
                        :loading="modelState.spinning"
                        :columns="columns"
                        :data-source="modelState.searchTable"
                        :scrollX="200"
                        :scrollY="modelState.searchTable.length ? 380 : null"
                        :total="state.pagination.total"
                        :current="state.pagination.pageNo"
                        @paginationChange="paginationChange"
                        :row-selection="{
                            hideSelectAll: activeTab.single,
                            selectedRowKeys: state.selectedRowKeys,
                            onChange: onSelectChange,
                        }"
                    >
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.dataIndex === 'detClass'">
                                <Tooltip :title="tipTitle(record)"></Tooltip>
                            </template>
                        </template>
                    </y-table>
                </div>
                <div v-else class="select-wrap">
                    <!-- 面包屑 -->
                    <a-breadcrumb>
                        <template #separator>
                            <a-avatar shape="square" :src="arrow" :size="20"></a-avatar>
                        </template>
                        <template v-for="(item, index) in state.breadcrumbs" :key="item[fieldNames.value]">
                            <a-breadcrumb-item href="" @click="handleBreadcrumb(item, index)">
                                {{ item.showName || item[fieldNames.label] }}
                            </a-breadcrumb-item>
                        </template>
                    </a-breadcrumb>
                    <!-- 源数据 -->
                    <div class="structures" ref="scrollRef" @scroll="handleScroll($event)">
                        <div class="spinning" v-if="modelState.spinning"><a-spin :spinning="modelState.spinning" /></div>
                        <template v-if="modelState.dataSource?.length">
                            <div class="row checkbox" mt-16 v-if="isShowAllSelect && !optionLength">
                                <a-checkbox
                                    :disabled="!!modelState.isaAuthority"
                                    class="check"
                                    :checked="checkAll"
                                    @change="onCheckAllChange"
                                >
                                    全选
                                </a-checkbox>
                            </div>

                            <component
                                :class="{ treeGroup: isShowAllSelect }"
                                :value="originCheckedList"
                                style="width: 100%"
                                :is="activeTab.single ? ARadio.Group : ACheckbox.Group"
                            >
                                <div class="row tree" v-for="item in modelState.dataSource" :key="item.id">
                                    <component
                                        class="check"
                                        :class="{
                                            'check-visible': !isCheckVisible(item),
                                        }"
                                        :value="item[fieldNames._value] || item[fieldNames.value]"
                                        :is="activeTab.single ? ARadio : ACheckbox"
                                        :disabled="
                                            !!item.disabled ||
                                            !!modelState.isaAuthority ||
                                            !!(optionLength && optionLength == targetCheckedList.length)
                                        "
                                        name="check"
                                        @change="onCheckChange($event, item)"
                                    >
                                        <a-avatar shape="square" :src="(!isPerson(item) && structure) || ''" :size="36">
                                            <span v-if="isPerson(item)">
                                                {{ item.studentName?.slice(-2) || item.name?.slice(-2) }}
                                            </span>
                                        </a-avatar>
                                        <div class="cnt">
                                            <div class="label ellipsis" :title="item[fieldNames.label]">
                                                {{ item.showName || item[fieldNames.label] }}
                                            </div>
                                        </div>
                                    </component>
                                    <div class="more" v-if="isShowMore(item)">
                                        <span
                                            class="more-next acitve"
                                            v-if="modelState.checkVisible !== 'people' && prohibitNextStep(item)"
                                        >
                                            下级
                                        </span>
                                        <span class="more-next" v-else @click="handleMore($event, item)">下级</span>
                                    </div>
                                </div>
                            </component>
                        </template>

                        <div v-else class="data-empty">
                            <img src="@/assets/images/admin/select-empty-data.png" />
                            <p>暂时没有找到相关结果</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="section section-selected">
                <div class="selected-hd">
                    <div class="label">
                        已选
                        <span class="count">{{ targetCheckedList?.length || 0 }}{{ unit }}</span>
                    </div>
                    <div class="btn-clear" v-if="targetCheckedList.length" @click="handleClear">清空</div>
                </div>
                <div class="selected-bd">
                    <!-- 已选择数据 -->
                    <div class="selected-list" v-if="targetCheckedList?.length">
                        <div class="selected-item" v-for="(item, index) in targetCheckedList" :key="item[fieldNames.value]">
                            <a-avatar shape="square" :src="(!isPerson(item) && structure) || ''" :size="24">
                                <span v-if="isPerson(item)">
                                    {{ item.studentName?.slice(-2) || item.name?.slice(-2) }}
                                </span>
                            </a-avatar>
                            <div class="cnt">
                                <div class="label ellipsis" :title="item[fieldNames.label]">
                                    {{ item.showName || item[fieldNames.label] }}
                                </div>
                            </div>

                            <div class="icon-del" @click="handleDelete(index)"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </a-modal>
</template>

<script setup>
// ------------- 在调用的页面设置 -------------
// const modelState = reactive({
//   openVisible: false, // 显示弹框
//   dataSource: [],// 左侧数据源
//   selectedData: [] // 已选中的数据
//   dept是否选部门、people是否人员、class班级(只能选择最后一个),all部门和人都可以选择
//   checkVisible: 'dept',
//   disableSelect: [], // 禁止选人的id
// })
// provide("modelState", () => modelState)
// provide('callbackFunction', () => ({
//     search, //搜索
//     toggleLevel, // 切换下一级或者面包屑
//     toggleTabs,  // 切换tabs
//     cancel,  // 取消
//     submit,  // 确定
// }))

//  ********** tabs定义 ***********
// [
//     {
//         tab: 'tab名称',   // tab名称
//         checked: true,    // 当前tab为聚焦状态(多个tab时一个选中，其余不选中)
//         id: TAB_ID_MAP.STUDENT,   // tab的唯一标识，可以自由定义
//         $ 用于区分是 人或者班级|宿舍 还是其他 （老接口，后端数据接口没有区分类型的判断）新版本接口使用personField
//         $ 推荐：使用personField
//         legacy_personKey: 'userId',
//         // $ 用于区分是 人或者班级|宿舍 还是其他 （新接口，后端数据接口带有区分类型的判断）
//         personField: { key: 'typeValue', value: ['student'] },
//         // 复选
//         single: true,  // true为单选  false为复选
//         // 搜索框配置
//         searchOption: {
//             show: true, // 是否展示搜索框 (默认为false)
//             displayMode: 'old',  // 展示的模式，'old'代表旧的， 'new'代表新的(table的形式展示 - 默认) DISPLAY_MODE
//         },
//     },
//     .....
// ]
//  ********** tabs定义 ***********

// ------------- 在调用的页面设置 -------------

import structure from '@/assets/images/library/icon-structure.png'
import arrow from '@/assets/images/library/icon-arrow.png'
import { Checkbox as ACheckbox, Radio as ARadio } from 'ant-design-vue'
import { watch, computed } from 'vue'

const dormitoryStore = useDormitoryStore()
const roomTypes = computed(() => {
    return dormitoryStore.dorm_options || []
})
// // 就读方式的字典列表
// const accommodationOpt = computed(() => {
//     const { accommodation = [] } = store.state.selectSource.dictionary
//     return accommodation
// })
const modelState = inject('modelState')()
const callbackFunction = inject('callbackFunction')()
const props = defineProps({
    // 用法看上面
    tabs: {
        type: Array,
        default: () => [],
    },
    // 已选择
    selected: {
        type: Array,
        default: () => [],
    },
    fieldNames: {
        type: Object,
        default: () => {
            // _value是别名，  在value:id中 不满足的情况下， 用_value 中的值
            //  如果没别名id 则一定要用value: 'id'
            // 为什么要设置_value是别名？
            // 因为在选楼层的时候它们在不通宿舍中的楼层id会相同（后端缺陷）， 所以设置别名去选中（后端将宿舍id和楼层id 做了拼接）
            return { label: 'name', value: 'id', _value: '' }
        },
    },
    optionLength: {
        type: Number,
        default: 0,
    },
    // 不同业务此处存在不同
    columns: {
        type: Array,
        default: () => {
            return [
                {
                    title: '姓名',
                    dataIndex: 'name',
                },
                {
                    title: '部门/班级',
                    dataIndex: 'detClass',
                },
            ]
        },
    },
})
// 选人组件类型
const SELECT_TYPE = {
    // 部门
    DEPT: 'dept',
    // 人
    PEOPLE: 'people',
    // 班级 （只选择最后一个）
    CLASS: 'class',
    // 所有
    ALL: 'all',
}

// 搜索组件展现模式
const DISPLAY_MODE = {
    NEW: 'new',
    OLD: 'old',
}

// 触发toggleLevel标识
const TRIGGER_MAP = {
    BREAD_CRUMBS: 'breadCrumbs',
    NEXT_MORE: 'nextMore',
}

const scrollRef = ref(null)

const state = reactive({
    allSelect: true,
    selectedRowKeys: [],
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
    isSearchTable: false,
    // name: '',
    spinning: false,
    // 面包屑
    breadcrumbs: [
        {
            name: props.tabs[0]?.tab,
            id: 0,
            children: [],
        },
    ],
    // 源数据
    dataSource: [],
    inputGroup: {
        name: '',
        accommodation: '',
    },
})

// 选择的表格数据
const onSelectChange = (ron, node) => {
    const { id: _type, single } = activeTab.value
    if (single) {
        // 需要区分_type，不能影响别的tab栏的数据
        const ids = modelState.searchTable.map(item => item.id)
        // 找到当前table下的id
        const id = state.selectedRowKeys.find(id => ids.includes(id))
        const reservedList = targetCheckedList.value.filter(item => item._type !== _type)
        const reservedKeys = reservedList.map(item => item.id)
        const selectNode = node.filter(row => row.id !== id).map(row => ({ ...row, _type }))
        state.selectedRowKeys = selectNode.length ? [...reservedKeys, selectNode[0].id] : reservedKeys
        targetCheckedList.value = [...reservedList, ...selectNode]
    } else {
        // 只针对当前的table数据进行选择,保留不在这个table里的选中数据
        const ids = modelState.searchTable.map(item => item.id)
        const reservedList = targetCheckedList.value.filter(item => !ids.includes(item.id))
        const reservedKeys = reservedList.map(item => item.id)
        state.selectedRowKeys = [...reservedKeys, ...ron]
        const selectNode = node.map(v => ({ ...v, _type }))
        targetCheckedList.value = [...reservedList, ...selectNode]
    }
}

const tipTitle = computed(() => {
    return item => item.deptName || item.deptString || item.className || item.pName || '--'
})

// 查询人员分页
const paginationChange = ({ pageNo, pageSize }) => {
    state.pagination.pageNo = pageNo
    state.pagination.pageSize = pageSize
}
// 当前选人业务
const activeTabIndex = ref(props.tabs.findIndex(item => item.checked) || 0)
const activeTab = computed(() => {
    return props.tabs[activeTabIndex.value]
})

// 判断是人还是其他 （有一个tab符合就显示人。 后续可增加条件拓展）
const legacy_personKeys = props.tabs.map(tab => tab.legacy_personKey).filter(Boolean)
const isPerson = computed(() => {
    return item => {
        if ([SELECT_TYPE.PEOPLE, SELECT_TYPE.ALL].includes(modelState.checkVisible)) {
            // $ tab有一个符合就算是人（切换tab,保持头像必须不变）
            const isNewRule = props.tabs.some(tab => !!tab.personField?.key)
            if (isNewRule) {
                return props.tabs.some(tab => tab.personField.value.includes(item[tab.personField?.key]))
            } else {
                let keys = legacy_personKeys.length ? legacy_personKeys : ['userId']
                return keys.find(key => item.hasOwnProperty(key))
            }
        }
        return false
    }
})

const isCheckVisible = computed(() => {
    return item => {
        if (modelState.checkVisible === SELECT_TYPE.DEPT) {
            // 选部门
            return true
        } else if (modelState.checkVisible === SELECT_TYPE.PEOPLE) {
            // 选人
            // $ 使用新规则还是旧规则
            const isNewRule = !!activeTab.value.personField?.key
            const value = activeTab.value.personField?.value || []
            const isPeople = value.includes(item[activeTab.value.personField?.key])
            const bol = (!isNewRule && item.hasOwnProperty(activeTab.value.legacy_personKey)) || (isNewRule && isPeople)
            return bol
        } else if (modelState.checkVisible === SELECT_TYPE.CLASS) {
            // 班级、宿舍
            const value = activeTab.value.personField?.value || []
            const isClass = value.includes(item[activeTab.value.personField?.key])
            return isClass
        } else if (modelState.checkVisible === SELECT_TYPE.ALL) {
            // 选人和选部门
            return true
        }
    }
})
// 单位
const unit = computed(() => {
    if (modelState.checkVisible === SELECT_TYPE.DEPT) {
        // 选部门
        return '部门'
    } else if (modelState.checkVisible === SELECT_TYPE.PEOPLE) {
        return '人'
    } else if (modelState.checkVisible === SELECT_TYPE.CLASS) {
        // 班级、宿舍
        return '项'
    } else if (modelState.checkVisible === SELECT_TYPE.ALL) {
        // 选人和选部门
        return '项'
    }
})

// 右侧选中
const targetCheckedList = ref(props.selected || [])
watch(
    () => props.selected,
    val => {
        targetCheckedList.value = JSON.parse(JSON.stringify(val))
    },
)
// 再加个监听选人框打开的时 将已选数据赋值
watch(
    () => modelState.openVisible,
    val => {
        if (val) {
            targetCheckedList.value = JSON.parse(JSON.stringify(props.selected))
        }
    },
)
// 搜索
const handleSearch = () => {
    resetPages()
    // $ 默认使用新的搜索展示框，如果指定旧的就用旧的
    const { searchOption = { displayMode: DISPLAY_MODE.NEW } } = activeTab.value
    const isOld = searchOption.displayMode === DISPLAY_MODE.OLD
    state.isSearchTable = !isOld && (!!state.inputGroup.name || !!state.inputGroup.accommodation)
    // 旧的展示方式
    if (searchOption.displayMode === DISPLAY_MODE.OLD) {
        // 删除面包屑
        state.breadcrumbs.splice(1)
    }
    const tabId = activeTab.value.id
    // 需要把选中的数据同步给table的select
    state.selectedRowKeys = targetCheckedList.value.map(item => item.id)
    // $ 目前旧的搜索不支持分页
    if (state.inputGroup.name || searchOption.displayMode === DISPLAY_MODE.OLD) {
        // 新模式下name为空不会派发'search'因为数据存储在不同的字段里
        callbackFunction.search(tabId, state.inputGroup.name, state.inputGroup.accommodation)
    }
}

// 楼层
let isShowSearch = computed(() => {
    const { legacy_personKey, searchOption = {} } = activeTab.value
    // $ 第一个条件是遗留问题
    return ['userId', 'studentCode'].includes(legacy_personKey) || !!searchOption?.show
})

// 面包屑点击
const handleBreadcrumb = (row = {}, index = 0) => {
    resetPages()
    state.breadcrumbs.splice(index + 1)
    state.inputGroup.name = ''
    state.inputGroup.accommodation = ''
    const tabId = activeTab.value.id
    const options = {
        index,
        // 触发标识
        trigger: TRIGGER_MAP.BREAD_CRUMBS,
    }
    callbackFunction.toggleLevel(tabId, toRaw(row), options)
}

// 分页重置
const resetPages = () => {
    if (modelState.pages) {
        // 分页重置
        modelState.isEnableScroll = true
        modelState.pages.pageNo = 1
        modelState.pages.total = 0
    }

    // 重置滚动条
    if (scrollRef.value) {
        scrollRef.value.scrollTop = 0
    }
}

// tab切换
const onTabsChange = () => {
    resetPages()
    callbackFunction.toggleTabs(activeTab.value)
    state.inputGroup.name = ''
    state.inputGroup.accommodation = ''
    // 初始化面包屑
    state.breadcrumbs = [
        {
            name: activeTab.value.tab,
            id: activeTab.value.id,
        },
    ]
}

// 禁止下一步
const prohibitNextStep = computed(() => {
    return item => {
        const { value, _value } = props.fieldNames
        const ID = item[_value] || item[value]
        if (ID && targetCheckedList.value?.length) {
            const idex = targetCheckedList.value.findIndex(v => (v.userId ? v.userId == ID : v[_value] == ID || v[value] == ID))
            return idex !== -1
        }
        return false
    }
})
// 下级
const handleMore = (e, row) => {
    e.preventDefault()
    resetPages()
    state.inputGroup.name = ''
    state.inputGroup.accommodation = ''
    const isChecked = state.breadcrumbs.some(v => v.id === row.id)
    !isChecked && state.breadcrumbs.push(row)
    const tabId = activeTab.value.id
    const options = {
        index: state.breadcrumbs.length - 1,
        // 触发标识
        trigger: TRIGGER_MAP.NEXT_MORE,
    }
    callbackFunction.toggleLevel(tabId, toRaw(row), options)
}

// 左侧选中
const originCheckedList = computed(() => {
    const { value, _value } = props.fieldNames
    return activeTab.value.single ? targetCheckedList.value[0]?.id : targetCheckedList.value.map(item => item[_value] || item[value])
})

// 全选
const checkAll = computed(() => {
    const { value, _value } = props.fieldNames

    if (activeTab.value.single) {
        return false
    }
    let selectableList = []
    if (modelState.checkVisible === SELECT_TYPE.DEPT) {
        selectableList = modelState.dataSource
    } else if (modelState.checkVisible === SELECT_TYPE.PEOPLE) {
        const isNewRule = !!activeTab.value.personField?.key
        const value = activeTab.value.personField?.value || []
        // 获取到人的列表
        selectableList = modelState.dataSource.filter(
            item =>
                (!isNewRule && item.hasOwnProperty(activeTab.value.legacy_personKey)) ||
                (isNewRule && value.includes(item[activeTab.value.personField?.key])),
        )
    } else if (modelState.checkVisible === SELECT_TYPE.CLASS) {
        // 班级|宿舍
        const value = activeTab.value.personField?.value || []
        selectableList = modelState.dataSource.filter(item => value.includes(item[activeTab.value.personField.key]))
    } else if (modelState.checkVisible === SELECT_TYPE.ALL) {
        selectableList = modelState.dataSource
    }
    return (
        !!selectableList.length &&
        selectableList.every(item => originCheckedList.value.indexOf(item[_value] || item[value]) > -1 || !!item.disabled)
    )
})

// 是否展示下一级
const isShowMore = computed(() => {
    return item => {
        if (modelState.checkVisible === SELECT_TYPE.PEOPLE) {
            // 人
            const isNewRule = !!activeTab.value.personField?.key
            const value = activeTab.value.personField?.value || []
            const isPeople = value.includes(item[activeTab.value.personField?.key])
            if ((!isNewRule && !item.hasOwnProperty(activeTab.value.legacy_personKey)) || (isNewRule && !isPeople)) {
                return true
            } else {
                return false
            }
        } else if ([SELECT_TYPE.CLASS, SELECT_TYPE.ALL].includes(modelState.checkVisible)) {
            // 班级|宿舍
            const value = activeTab.value.personField?.value || []
            const isPerson = value.includes(item[activeTab.value.personField?.key]) || item.hasOwnProperty(activeTab.value.legacy_personKey)
            return !isPerson
        }
        return item.children?.length
    }
})

const isShowAllSelect = computed(() => {
    //  activeTab.single 多选： false ， 单选：
    let allSelect = [SELECT_TYPE.DEPT, SELECT_TYPE.ALL, SELECT_TYPE.CLASS].includes(modelState.checkVisible)
    if ([SELECT_TYPE.PEOPLE, SELECT_TYPE.CLASS].includes(modelState.checkVisible) && modelState.dataSource.length) {
        const item = modelState.dataSource[modelState.dataSource.length - 1]
        const isNewRule = !!activeTab.value.personField?.key
        const value = activeTab.value.personField?.value || []
        const isPeople = value.includes(item[activeTab.value.personField?.key])
        allSelect = (!isNewRule && item.hasOwnProperty(activeTab.value.legacy_personKey)) || (isNewRule && isPeople)
    }
    return !activeTab.value.single && isCheckVisible.value && allSelect
})

// 全选事件
const onCheckAllChange = e => {
    let selectableList = []
    if (modelState.checkVisible === SELECT_TYPE.DEPT) {
        selectableList = modelState.dataSource
    } else if (modelState.checkVisible === SELECT_TYPE.ALL) {
        selectableList = modelState.dataSource
    } else if (modelState.checkVisible === SELECT_TYPE.PEOPLE) {
        // $ 使用新规则还是旧规则
        const isNewRule = !!activeTab.value.personField?.key
        const value = activeTab.value.personField?.value || []

        selectableList = modelState.dataSource.filter(item => {
            return (
                (!isNewRule && item.hasOwnProperty(activeTab.value.legacy_personKey)) ||
                (isNewRule && value.includes(item[activeTab.value.personField?.key]))
            )
        })
    } else if (modelState.checkVisible === SELECT_TYPE.CLASS) {
        const value = activeTab.value.personField?.value || []
        selectableList = modelState.dataSource.filter(item => value.includes(item[activeTab.value.personField?.key]))
    }

    //  $ 用于区分tab,记录类型
    const _type = activeTab.value.id
    const { _value, value } = props.fieldNames
    if (e.target.checked) {
        const selected = targetCheckedList.value.map(item => item[_value] || item[value])
        selectableList.forEach(item => {
            if (!selected.includes(item[_value] || item[value]) && !item.disabled) {
                targetCheckedList.value.push({ ...item, _type })
            }
        })
    } else {
        const ids = new Set(selectableList.map(item => item[_value] || item[value]))
        // 过滤在ids存在的
        targetCheckedList.value = targetCheckedList.value.filter(item => !ids.has(item[_value] || item[value]) && !item.disabled)
    }
}

// 滚动加载
const handleScroll = async event => {
    /**
     * !!! pages 和 onScroll 缺一不可
     * !!! 外层的onScroll必须是promise方法
     * 请参考rfid的使用方法
     */
    if (!modelState.pages || !callbackFunction.onScroll) {
        return
    }

    // 滚动区域的高度 - 滚动条到顶部高度
    const isScroll = event.target.scrollHeight - event.target.scrollTop < 600

    // 是否已经加载完全部数据
    const greaterTotal = modelState.pages.pageNo * modelState.pages.pageSize >= modelState.pages.total
    if ([undefined, true].includes(modelState.isEnableScroll) && isScroll && !greaterTotal) {
        try {
            modelState.isEnableScroll = false
            modelState.pages.pageNo++

            await callbackFunction.onScroll(activeTab.value, state.breadcrumbs.at(-1))
        } catch (error) {
            console.error('error:', error)
            modelState.pages.pageNo--
        } finally {
            modelState.isEnableScroll = true
        }
    }
}

// 单选
const onCheckChange = (e, row) => {
    //  $ 用于区分tab,记录类型
    const { _value, value } = props.fieldNames
    const _type = activeTab.value.id
    if (activeTab.value.single) {
        // 单选
        targetCheckedList.value = e.target.checked ? [{ ...row, _type }] : []
    } else {
        // 复选
        if (e.target.checked) {
            targetCheckedList.value.push({ ...row, _type })
        } else {
            const _targetCheckedList = []
            targetCheckedList.value.forEach(item => {
                if (item[_value] !== row[_value] || item[value] !== row[value]) {
                    _targetCheckedList.push(item)
                }
            })
            targetCheckedList.value = _targetCheckedList
        }
    }
}

// 清空
const handleClear = () => {
    targetCheckedList.value = []
    state.selectedRowKeys = []
}

// 删除
const handleDelete = index => {
    targetCheckedList.value.splice(index, 1)
    state.selectedRowKeys.splice(index, 1)
}

// 取消
const onCancel = () => {
    state.inputGroup.name = ''
    state.inputGroup.accommodation = ''
    state.isSearchTable = false
    handleClear()
    modelState.openVisible = false
    // $ 恢复第一层数据。新的接口是每一层都会请求的. 每次重新打开 以新状态展示
    // 恢复面包屑
    state.breadcrumbs = [state.breadcrumbs[0]]
    // 重置恢复选中的tab
    activeTabIndex.value = props.tabs.findIndex(item => item.checked) || 0
    callbackFunction.cancel?.()
}

// 确认
const onOk = () => {
    const newTargetChecked = JSON.parse(JSON.stringify(targetCheckedList.value))
    callbackFunction.submit(newTargetChecked)
    onCancel()
}

watch(
    () => props.tabs,
    val => {
        state.breadcrumbs[0].name = val[0].tab
    },
)
</script>

<style lang="less" scoped>
.mSelect-wrap {
    display: flex;
    height: 540px;
    font-size: 14px;
    line-height: 20px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);

    .section {
        display: flex;
        flex-direction: column;
        width: 50%;
        padding: 12px 0 0;

        &:last-child {
            border-left: 1px solid #f0f0f0;
        }

        &.active {
            padding-top: 0;

            .select-wrap {
                margin-top: 0;
                border-top: none;
            }
        }

        .reset-input-search {
            margin: 10px auto;
            width: 90%;
        }
    }

    .select-wrap {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 0 16px;
        overflow: hidden;
    }

    .ant-breadcrumb {
        :deep(ol) {
            display: inline-block;
        }

        li {
            display: inline;
        }

        :deep(.ant-breadcrumb-link) {
            display: inline;
        }

        span:last-child {
            pointer-events: none;
        }
    }

    .tabs {
        border-bottom: 1px solid #f0f0f0;
        padding: 0 16px 12px;

        :deep(.ant-radio-group) {
            display: flex;
            text-align: center;
        }

        :deep(.ant-radio-button-wrapper) {
            flex: 1;

            &:first-child {
                border-top-left-radius: 40px;
                border-bottom-left-radius: 40px;
            }

            &:last-child {
                border-top-right-radius: 40px;
                border-bottom-right-radius: 40px;
            }
        }
    }

    .structures {
        flex: 1;
        overflow-y: auto;
        margin-right: -10px;
        position: reactive;

        .spinning {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 999;
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        :deep(.ant-spin-nested-loading) {
            height: 100%;
        }

        :deep(.ant-spin-container) {
            height: 100%;
        }

        .row {
            box-sizing: border-box;
            display: flex;
            align-items: center;
            padding: 0 8px;
            border-radius: 4px;
            margin-bottom: 6px;
            width: 100%;

            &:hover,
            &:active {
                background-color: #f6f6f6;
            }
            &.checkbox {
                position: fixed;
                margin: 0;
                background-color: @body-background;
                z-index: 9999;
                width: 310px;
            }
        }
        .treeGroup {
            margin-top: 34px;
        }
        .check {
            display: flex;
            align-items: center;
            padding: 6px 0;
            width: 100%;

            :deep(span:nth-of-type(2)) {
                flex: 1;
            }
        }

        .check-visible {
            pointer-events: none;

            :deep(.ant-radio),
            :deep(.ant-checkbox) {
                visibility: hidden;
            }
        }

        .cnt {
            flex: 1;
            max-width: 170px;
            margin-left: 8px;
        }

        :deep(.ant-radio + span),
        :deep(.ant-checkbox + span) {
            flex: 1;
            display: flex;
            align-items: center;
        }

        .sub {
            color: #999;
        }

        .more {
            font-size: 14px;
            color: var(--primary-color);
            line-height: 16px;
            padding-left: 12px;
            border-left: 1px solid #d9d9d9;
            margin-left: auto;
            min-width: 30px;
            cursor: pointer;
            user-select: none;

            .more-next {
                &.acitve {
                    color: #999999;
                }
            }
        }
    }

    :deep(.ant-avatar) {
        font-size: 14px;
        background: var(--primary-color);
    }

    :deep(.ant-avatar-image) {
        background: transparent;
    }

    .selected-hd {
        display: flex;
        align-items: center;
        padding: 0 12px;

        .count {
            font-size: 12px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.85);
            line-height: 17px;
            margin-left: 8px;
        }

        .btn-clear {
            color: var(--primary-color);
            margin-left: auto;
            cursor: pointer;
            user-select: none;
        }
    }

    .selected-bd {
        flex: 1;
        padding: 15px 12px 0;
        overflow-y: auto;
    }

    .selected-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .selected-item {
            display: flex;
            align-items: center;
            padding: 4px;
            background: #f6f6f6;
            border-radius: 4px;
        }

        .cnt {
            margin-left: 4px;
        }

        .sub {
            color: #999;
        }

        .icon-del {
            width: 14px;
            height: 15px;
            margin-left: 8px;
            cursor: pointer;
            user-select: none;
            font-size: 12px;
            background-image: url('@/assets/images/library/icon-del.png');
        }
    }
}

.ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: keep-all;
}

.data-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 90px;

    img {
        width: 201px;
        height: 117px;
    }

    p {
        font-size: 14px;
        font-weight: 400;
        color: #595959;
        line-height: 26px;
        text-align: center;
        margin-top: 17px;
    }
}

.single-table {
    :deep(.ant-checkbox-inner) {
        border-radius: 100%;
    }

    :deep(.ant-checkbox-checked) {
        &::after {
            border-radius: 100%;
        }
    }

    :deep(.ant-checkbox-wrapper) {
        border-radius: 100%;
    }
}
</style>
