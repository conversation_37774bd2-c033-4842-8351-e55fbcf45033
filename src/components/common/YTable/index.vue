<template>
    <div class="page-data" :style="{ 'min-height': minH + 'px' }">
        <a-table class="tableList" rowKey="id" :pagination="false" :scroll="{ x: scrollX, y: scrollY }" v-bind="$attrs" :loading="loading">
            <template v-for="slotName in Object.keys($slots)" #[slotName]="{ text, record, index, column }" :key="slotName">
                <slot :name="slotName" v-bind="{ text, record, index, column }"></slot>
            </template>
        </a-table>
    </div>

    <Pagination :total="total" @paginationChange="paginationChange" :current="props.current"></Pagination>
</template>

<script setup>
defineOptions({
    inheritAttrs: false,
})

const props = defineProps({
    minH: {
        type: Number,
    },
    // 分页
    total: {
        type: Number,
    },
    current: {
        type: Number,
    },
    scrollX: {
        type: null || Number,
        default: 1550,
    },
    loading: {
        type: Boolean,
        default: false,
    },
    scrollY: {
        type: null || Number,
        default: null,
    },
})

const emit = defineEmits(['paginationChange', 'select'])

// 分页
const paginationChange = ({ pageNo, pageSize }) => {
    emit('paginationChange', { pageNo, pageSize })
}
</script>

<style lang="less" scoped>
@import url('./index.less');
</style>
