<template>
    <div class="reset-from">
        <div class="reset_title">{{ props.setPassword.resetTitle }}</div>
        <div class="reset_tips" :class="{ active: props.setPassword.isAdmin }" v-if="props.setPassword.isTips === 'set'">
            {{ state.loginTips }}
        </div>
        <a-form :model="state.resetFrom" ref="resetFromRef" name="resetFrom" autocomplete="off" :rules="rules">
            <template v-if="!props.setPassword.isAdmin">
                <a-form-item v-if="props.setPassword.isTips === 'set'" label="当前手机号：">
                    <span>{{ props.setPassword.userPhone }}</span>
                </a-form-item>
                <a-form-item name="phone" v-else>
                    <a-input
                        class="code-input"
                        style="height: 42px"
                        :maxlength="11"
                        v-model:value="state.resetFrom.phone"
                        placeholder="请输入手机号"
                    ></a-input>
                </a-form-item>

                <a-form-item class="form-item-code" name="verifyCode">
                    <a-input class="code-input" v-model:value="state.resetFrom.verifyCode" placeholder="请输入验证码">
                        <template #suffix>
                            <a-typography-text class="code-num" v-if="codeNum !== 180">{{ codeNum }} 秒</a-typography-text>
                            <a-button class="code-num" type="link" v-else @click.stop="handleCode">获取验证码</a-button>
                        </template>
                    </a-input>
                </a-form-item>
            </template>
            <a-form-item name="password">
                <a-input-password
                    v-model:value.trim="state.resetFrom.password"
                    placeholder="请输入含字母、数字、特殊字符，长度为8-20个字符的密码"
                />
            </a-form-item>
            <a-form-item name="againPassword">
                <a-input-password
                    :disabled="!state.resetFrom.password"
                    v-model:value.trim="state.resetFrom.againPassword"
                    placeholder="请再次输入密码"
                />
            </a-form-item>
            <a-form-item>
                <a-button class="confirm-btn" type="primary" @click="resetPassword" :loading="state.loading">确 定</a-button>

                <a-button class="confirm-btn back" type="link" @click="handleBack">返 回</a-button>
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup>
import RSA from '../login/rsa.js'
import { checkField, regularSpecialCharacters } from '@/utils/utils.js'
const store = useStore()

// *********************
// Hooks Function
// *********************
// -----
const emit = defineEmits(['submit', 'emitBack'])
const codeNum = shallowRef(180) // 时长3分钟

const resetFromRef = ref(null)

const props = defineProps({
    setPassword: {
        type: Object,
        default: () => {
            return {
                isAdmin: false,
                isTips: 'set',
                resetTitle: '设置新密码',
                userPhone: '',
            }
        },
    },
})
const state = reactive({
    resetFrom: {
        password: '',
        againPassword: '',
        phone: '',
        verifyCode: '',
    },
    loading: false,
    loginTips: '为保护您的账号安全及隐私，请设置新密码',
})

const tipsTitle = {
    3: '为了您的账号安全，初次进入系统请设置新密码',
    4: '系统检测到您许久未登录系统，请设置新密码',
    5: '系统检测到您的账号密码安全系数较弱，请设置新密码',
}

let isCorrectPhone = ref(false)

watch(
    () => props.setPassword,
    val => {
        const { userPhone, logoutStatus } = val
        state.resetFrom.phone = userPhone
        isCorrectPhone.value = true
        state.loginTips = tipsTitle[logoutStatus] || '为保护您的账号安全及隐私，请设置新密码'
    },
    {
        immediate: true,
        deep: true,
    },
)
// *********************
// Default Function
// *********************

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

// 自定义校验
const verifyPassword = (_rule, value) => {
    if (!checkField(value, 8, 20)) {
        return Promise.reject(new Error('请输入含字母、数字、特殊字符，长度为8-20个字符的密码'))
    }
    if (!regularSpecialCharacters.test(value)) {
        return Promise.reject(new Error('请输入含字母、数字、特殊字符，长度为8-20个字符的密码'))
    }
    return Promise.resolve()
}

const verifyAgainPassword = (_rule, value) => {
    if (value !== state.resetFrom.password) {
        return Promise.reject(new Error('两次输入密码不一致!'))
    }
    return Promise.resolve()
}

const verifyCheckCode = async (_rule, value) => {
    if (!value) {
        return Promise.reject('请输入验证码')
    }
    return Promise.resolve()
}
const verifyCheckPhone = (_rule, value) => {
    if (value && /^[1][3-9][0-9]{9}$/.test(value)) {
        isCorrectPhone.value = true
        return Promise.resolve()
    }
    isCorrectPhone.value = false
    return Promise.reject('请输入正确的手机号码')
}

const rules = {
    password: [{ required: true, validator: verifyPassword, trigger: 'blur' }],
    againPassword: [{ required: true, validator: verifyAgainPassword, trigger: 'blur' }],
    verifyCode: [{ required: true, validator: verifyCheckCode, trigger: 'blur' }],
    phone: [{ required: true, validator: verifyCheckPhone, trigger: 'blur' }],
}

let time = null
let isSms = ref(true)

// 获取验证码
const handleCode = async () => {
    resetFromRef.value.validateFields('phone')
    if (isCorrectPhone.value && isSms.value) {
        const params = {
            phone: state.resetFrom.phone,
        }

        isSms.value = false
        await http
            .post('/cloud/sms/external/message', params)
            .then(res => {
                clearInterval(time)
                time = setInterval(() => {
                    if (codeNum.value <= 1) {
                        clearInterval(time)
                        codeNum.value = 180
                    } else {
                        codeNum.value--
                    }
                }, 1000)
            })
            .finally(() => {
                isSms.value = true
            })
    }
}
const handleBack = () => {
    resetFromRef.value.resetFields()
    clearInterval(time)
    emit('emitBack')
}
// 设置新密码
const resetPassword = async () => {
    resetFromRef.value.validate().then(async () => {
        try {
            let url = '/cloud/user/updateNewPassword'
            state.loading = true
            const { phone, verifyCode, password, againPassword } = state.resetFrom

            const params = { paramEncipher: '' }
            // 忘记密码
            if (props.setPassword.isTips === 'forget') {
                params.paramEncipher = RSA.encrypt(
                    JSON.stringify({
                        phone,
                        verifyCode,
                        password: againPassword,
                    }),
                )
                url = '/cloud/user/forgetPassword'
            } else {
                params.paramEncipher = RSA.encrypt(
                    JSON.stringify({
                        phone,
                        verifyCode,
                        newPwd: password,
                        confirmPwd: againPassword,
                    }),
                )
            }
            const result = await http.post(url, params)
            YMessage.success(result.message)
            const account = { ...store.accountFrom, password: state.resetFrom.password }
            store.accountFrom = account
            emit('submit', account)
        } catch (error) {
            YMessage.error(error.message)
        } finally {
            state.loading = false
        }
    })
}
</script>

<style lang="less" scoped>
.reset-from {
    .reset_title {
        font-size: 26px;
        font-weight: 600;
        color: #000000;
        margin: 18px 0 10px;
    }

    .reset_tips {
        font-size: 14px;
        font-weight: 400;
        color: #595959;
        padding-bottom: 10px;

        &.active {
            padding-bottom: 20px;
        }
    }

    :deep(.ant-form-item) {
        margin-bottom: 30px;

        input {
            height: 30px;
        }
    }

    .confirm-btn {
        width: 100%;
        height: 40px;

        &.back {
            color: #999999;
            margin: 8px 0 0;
            font-size: 13px;
        }
    }

    .code-num {
        color: #00b781;
    }
}
</style>
