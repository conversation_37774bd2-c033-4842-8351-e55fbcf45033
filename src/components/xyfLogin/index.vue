<template>
    <div>
        <div class="login_box" v-if="!state.selectPlatform && state.platformList && state.platformList.length > 0">
            <div class="xyf_introduction">
                <img :src="xyf" alt="logo" />
            </div>
            <div class="form_box">
                <p class="form_title" v-if="state.showArea === SHOW_AREA_MAP.LOGIN">欢迎登录校易付</p>
                <!-- 登录 -->
                <LoginContent
                    v-if="state.showArea === SHOW_AREA_MAP.LOGIN"
                    :loading="state.loadingBtn"
                    @submit="submitLogin"
                    :loginText="state.loginText"
                    :isScanCode="true"
                    @emitIsSetModify="handleIsSetModify"
                />
                <!-- 重置密码 -->
                <Password
                    v-else-if="state.showArea === SHOW_AREA_MAP.PASSWORD"
                    :setPassword="state.setPassword"
                    @submit="submitLogin"
                    @emitBack="state.showArea = SHOW_AREA_MAP.LOGIN"
                />
            </div>
        </div>
        <div v-else class="select_platform">
            <div class="item" v-for="item in state.platformList" :key="item.platform" :class="platformClass[item.platform]">
                <div class="text">{{ item.name }}</div>
                <div class="btn" @click="selectFn(item)" :style="{ color: platformColor[item.platform] }">
                    <span>进入系统</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import LoginContent from '../login/loginContent.vue'
import Password from './xyfPassword.vue'
import RSA from '../login/rsa.js'
import xyf from '@/assets/images/xyf/login-icon.png'

const store = useStore()
const typeStore = useTypeStore()

const platformClass = ref({
    partner: 'partner_bg',
    merchant: 'merchant_bg',
})

const platformColor = ref({
    partner: '#21C192',
    merchant: '#6B85FE',
})

// 显示区域类型
const SHOW_AREA_MAP = {
    LOGIN: 'login',
    SCHOOL: 'school',
    PASSWORD: 'password',
}

const state = reactive({
    isLogin: false, // 是否已经登录（登录过后选学校）
    loginText: '登 录', // 按钮文案
    showArea: SHOW_AREA_MAP.LOGIN, // 界面显示的区域
    loadingBtn: false, // 按钮加载状态
    setPassword: {
        isAdmin: false,
        isTips: 'set',
        resetTitle: '设置新密码',
        userPhone: '',
    },
    selectPlatform: false,
    platformList: [
        {
            name: '服务商管理端',
            platform: 'partner',
        },
        {
            name: '商户端',
            platform: 'merchant',
        },
    ],
})

// 登录状态更新
const updateLoginStatus = status => {
    switch (status) {
        case 'success': {
            state.loginText = '登录'
            state.loadingBtn = false
            break
        }
        case 'error': {
            state.loginText = '登录失败'
            state.loadingBtn = false
            store.clearUser()
            setTimeout(() => {
                state.loginText = '登录'
            }, 2000)
            break
        }
        case 'loading': {
            state.loginText = '登录中...'
            state.loadingBtn = true
            break
        }
    }
}

const handleIsSetModify = item => {
    state.showArea = SHOW_AREA_MAP.PASSWORD
    state.setPassword.resetTitle = item === 'forget' ? '忘记密码' : '设置新密码'
    state.setPassword.isTips = item
}

// 登录平台
async function getUserInfo(platform) {
    const res = await http.get('/campuspay/admin-user/login', { platform })
    if (res.code != 0) {
        state.selectPlatform = false
    } else {
        http.get('/campuspay/admin-user/loginLog')
        typeStore.setPlatformId(res.data.partnerId)
        store.userInfo = res.data
        window.location.href = '/'
    }
}

// 修改系统的主题色和系统标识
function selectFn(item) {
    const { platform } = item
    typeStore.sysColor = platform === 'partner' ? '#00B781' : '#1890FF'
    typeStore.platform = platform
    typeStore.title = platform === 'partner' ? '校易付商户管理端' : '校易付商户端'
    store.loginPlatform = platform
    getUserInfo(platform)
}

// 获取用户的平台标识列表
async function getUserPlatformList() {
    const res = await http.get('/campuspay/admin-user/getUserPlatformList')
    if (res.data) {
        if (res.data.length > 1) {
            state.platformList = res.data
            state.selectPlatform = true
        } else if (res.data.length === 1) {
            // 只有一个端的时候直接进入系统
            // 修改系统的主题色和系统标识
            selectFn(res.data[0])
        } else if (res.data.length === 0) {
            YMessage.error('暂无权限登录系统，请联系管理员!')
        }
    }
}

// 检查用户登录状态
async function checkUserLogin(accountFrom) {
    const res = await http.get('/campuspay/admin-user/checkUserLogin')
    state.setPassword.isAdmin = res.data.isAdmin
    state.setPassword.logoutStatus = res.data.status
    // logoutStatus
    // (1, "校验正常"),
    // (2, "已注销"),
    // (3, "初始化密码（第一次登录）"),
    // (4, "用户长时间未登录系统"),
    // (5, "弱密码"),
    if ([3, 4, 5].includes(res.data.status)) {
        state.setPassword.isAdmin = res.data.isAdmin
        state.setPassword.userPhone = accountFrom.username
        state.setPassword.logoutStatus = res.data.status
        handleIsSetModify('set')
        // 恢复控件按钮状态
        updateLoginStatus('success')
    } else {
        await getUserPlatformList()
        updateLoginStatus('success')
    }
}

async function submitLogin(accountFrom) {
    updateLoginStatus('loading')
    console.log(accountFrom)
    // 账号加密
    const passWadData = {
        paramEncipher: RSA.encrypt(JSON.stringify(accountFrom)),
    }
    await http
        .form('/auth/oauth/token', passWadData)
        .then(res => {
            if (res.code != 0) {
                updateLoginStatus('error')
                YMessage.error(res.message)
                return
            }
            updateLoginStatus('success')
            const { accessToken } = res.data
            store.setToken(accessToken)
            checkUserLogin(accountFrom)
        })
        .catch(err => {
            console.log(err)
            updateLoginStatus('error')
        })
}
</script>

<style lang="less" scoped>
.login_box {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    width: 100vw;
    height: 100vh;
    background: url('@/assets/images/xyf/login_bg.png') no-repeat center;
    background-size: 100% 100%;
    padding-left: 110px;
    .xyf_introduction {
        img {
            width: 907px;
            height: 463px;
        }
    }
}
.form_box {
    background: #ffffff;
    width: 380px;
    height: 416px;
    padding: 32px;
    box-shadow: 0px 7px 16px 0px rgba(0, 0, 0, 0.08);
    border-radius: 8px;
    position: absolute;
    top: 50%;
    left: 75%;
    transform: translate3d(-50%, -50%, 0);

    .form_title {
        text-align: left;
        font-size: 22px;
        font-weight: 800;
        margin: 22px 0px 0px 0px;
    }
}
.select_platform {
    background: #f7fcfb;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    .item {
        width: 276px;
        height: 320px;
        position: relative;
        margin: 0px 10px;
        transition: top 0.3s ease;
        top: 0;

        &:hover {
            top: -6px;
            box-shadow: 0px 9px 20px 10px #75757529;
        }
        .text {
            width: 100%;
            text-align: center;
            position: absolute;
            top: 41px;
            font-weight: 600;
            font-size: 24px;
            color: #ffffff;
            line-height: 33px;
        }
        .btn {
            position: absolute;
            bottom: 19px;
            width: 92px;
            height: 28px;
            background: #f3fffb;
            border-radius: 14px;
            left: calc(50% - 46px);
            font-weight: 500;
            font-size: 14px;
            line-height: 28px;
            text-align: center;
            cursor: pointer;
        }
    }
    .partner_bg {
        background: url('@/assets/images/xyf/partnerBg.png') no-repeat;
        background-size: cover;
    }
    .merchant_bg {
        background: url('@/assets/images/xyf/merchantBg.png') no-repeat;
        background-size: cover;
    }
}
</style>
