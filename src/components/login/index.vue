<template>
    <div class="login_box">
        <div class="introduction">
            <img :src="introduction.logo" alt="logo" mr-30 />
            <div>
                <p class="title">{{ introduction.title }}</p>
                <p class="description">{{ introduction.description }}</p>
            </div>
        </div>
        <div class="form_box">
            <!-- 右上角扫码 -->
            <div
                v-if="state.showArea === SHOW_AREA_MAP.LOGIN"
                class="scan_code"
                :class="!state.isScanCode ? 'scancodelogin' : 'passwordlogin'"
                @click="loginTypeChange"
            ></div>
            <p class="form_title" v-if="state.showArea !== SHOW_AREA_MAP.PASSWORD">欢迎使用</p>
            <!-- 登录 -->
            <LoginContent
                v-if="state.showArea === SHOW_AREA_MAP.LOGIN"
                :loading="state.loadingBtn"
                @submit="submitLogin"
                :loginText="state.loginText"
                :isScanCode="state.isScanCode"
                @emitIsSetModify="handleIsSetModify"
            />
            <!-- 重置密码 -->
            <Password
                v-else-if="state.showArea === SHOW_AREA_MAP.PASSWORD"
                :setPassword="state.setPassword"
                @submit="submitLogin"
                @emitBack="state.showArea = SHOW_AREA_MAP.LOGIN"
            />
            <!-- 选择学校 -->
            <SelectSchool
                @submit="selectSubmit"
                :loading="state.loadingBtn"
                @back="backLogin"
                :data="state.schoolList"
                v-else-if="state.showArea === SHOW_AREA_MAP.SCHOOL"
            />
        </div>
        <div class="copyright">版权所有©深圳市一德文化科技有限公司 粤ICP备17028019号</div>
    </div>
</template>

<script setup>
import LoginContent from './loginContent.vue'
import SelectSchool from './selectSchool.vue'
import Password from './Password.vue'
import library from '@/assets/images/admin/library-login.png'
import dormitory from '@/assets/images/admin/dormitory-login.png'
import paySystem from '@/assets/images/paySystem/paySystem-login.png'
import RSA from './rsa.js'

const code = useCode()
const store = useStore()
const typeStore = useTypeStore()

// 显示区域类型
const SHOW_AREA_MAP = {
    LOGIN: 'login',
    SCHOOL: 'school',
    PASSWORD: 'password',
}

// *********************
// Hooks Function
// *********************

const state = reactive({
    setPassword: {
        isAdmin: false,
        isTips: 'set',
        resetTitle: '设置新密码',
        userPhone: '',
    },
    isScanCode: false, // 是否为扫码登录
    showArea: SHOW_AREA_MAP.LOGIN, // 界面显示的区域
    isLogin: false, // 是否已经登录（登录过后选学校）
    loginText: '登 录', // 按钮文案
    loadingBtn: false, // 按钮加载状态
    schoolList: [], //学校列表
    accessToken: null, // token
})

// 介绍信息
const introduction = computed(() => {
    if (code == 'yd-library') {
        return {
            logo: library,
            title: '一加壹智慧图书馆',
            description: '让阅读变得触手可及',
        }
    } else if (code == 'yd-dormitory') {
        return {
            logo: dormitory,
            title: '一加壹智慧宿舍系统',
            description: '助力管理更高效  住宿生活更便捷',
        }
    } else if (code == 'yd-paySystem') {
        return {
            logo: paySystem,
            title: '一加壹缴费系统',
            description: '让缴费更加便捷',
        }
    } else {
        // TODO，其他未规划子系统，展示用library的logo
        return {
            logo: library,
        }
    }
})

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

// 登录状态更新
const updateLoginStatus = status => {
    switch (status) {
        case 'success': {
            state.loginText = '登录'
            state.loadingBtn = false
            break
        }
        case 'error': {
            state.loginText = '登录失败'
            state.loadingBtn = false
            store.clearUser()
            setTimeout(() => {
                state.loginText = '登录'
            }, 2000)
            break
        }
        case 'loading': {
            state.loginText = '登录中...'
            state.loadingBtn = true
            break
        }
    }
}

// 获取用户信息
async function getCurrentUser(accountFrom) {
    const res = await http.get('/cloud/user/getCurrentUser')
    if (res.code !== 0) {
        updateLoginStatus('error')
        YMessage.error(res.message)
        return
    }
    const schoolList = res.data.school || []
    state.schoolList = schoolList
    // 设置用户信息
    if (accountFrom.grant_type === 'qrcode' && schoolList.length === 1) {
        await selectSubmit(schoolList[0])
    } else {
        // 选择学校
        state.showArea = SHOW_AREA_MAP.SCHOOL
    }
}

// 检查用户是否退出
async function checkUserLogout() {
    return new Promise(async resolve => {
        const res = await http.get('/cloud/user/checkUserLogout')
        if (res.code !== 0) {
            updateLoginStatus('error')
            YMessage.error(res.message)
            resolve(false)
            return
        }
        const { isLogout, logoutTime, isInitializePwd } = res.data
        if (isLogout) {
            Modal.confirm({
                title: `提示`,
                icon: createVNode(ExclamationCircleFilled),
                content: `您的账号已于${logoutTime}提交注销申请，需要撤销后才可登录，是否撤销？`,
                okText: '确认',
                cancelText: '取消',
                onCancel() {
                    resolve({
                        isInitializePwd,
                        isLogin: true,
                    })
                },
                onOk() {
                    http.get('/cloud/user/cancelLogout')
                    resolve({
                        isInitializePwd,
                        isLogin: false,
                    })
                },
            })
        } else {
            resolve({
                isInitializePwd,
                isLogin: true,
            })
        }
    })
}
const handleIsSetModify = item => {
    state.showArea = SHOW_AREA_MAP.PASSWORD
    state.setPassword.resetTitle = item === 'forget' ? '忘记密码' : '设置新密码'
    state.setPassword.isTips = item
}
// 登录按钮
async function submitLogin(accountFrom) {
    try {
        state.showArea = SHOW_AREA_MAP.LOGIN
        store.isCompletedLogin = false
        store.loginPlatform = ''
        updateLoginStatus('loading')
        // 账号加密
        const passWadData = {
            paramEncipher: RSA.encrypt(JSON.stringify(accountFrom)),
        }
        const res = await http.form('/auth/oauth/token', passWadData)
        if (res.code !== 0) {
            updateLoginStatus('error')
            YMessage.error(res.message)
            return
        }
        const { accessToken } = res.data
        store.setToken(accessToken)
        state.setPassword.isAdmin = false
        const { isLogin, isInitializePwd, isAdmin, logoutStatus = 0 } = await checkUserLogout()
        if (isLogin) {
            // if (isInitializePwd) {
            //     state.showArea = SHOW_AREA_MAP.PASSWORD
            //     // 恢复控件按钮状态
            //     updateLoginStatus('success')
            //     return
            // }
            // logoutStatus （枚举）：
            // (1, "校验正常"),
            //     (2, "已注销"),
            //     (3, "初始化密码（第一次登录）"),
            //     (4, "用户长时间未登录系统"),
            //     (5, "弱密码"),
            if ([3, 4, 5].includes(logoutStatus)) {
                state.setPassword.isAdmin = isAdmin
                state.setPassword.userPhone = accountFrom.username
                state.setPassword.logoutStatus = logoutStatus
                handleIsSetModify('set')
                // 恢复控件按钮状态
                updateLoginStatus('success')
            } else {
                await getCurrentUser(accountFrom)
                updateLoginStatus('success')
            }
        }
    } catch (error) {
        updateLoginStatus('error')
    }
}

// 切换登录类型（扫码/账号密码）
function loginTypeChange() {
    state.isScanCode = !state.isScanCode
}

// 选择学校中返回重新登录
function backLogin() {
    state.showArea = SHOW_AREA_MAP.LOGIN
    state.isScanCode = false
}

// 选择学校完按钮
async function selectSubmit(school) {
    try {
        updateLoginStatus('loading')
        const { id, isEnabled, children } = school
        const params = {
            schoolId: id,
            platform: typeStore.platformCode,
        }
        const res = await http.get('/cloud/menu/checkUserLogin', params)
        if (res.code !== 0) {
            updateLoginStatus('error')
            YMessage.error(res.message)
            return
        }
        if (isEnabled !== undefined && !isEnabled) {
            updateLoginStatus('error')
            YMessage.error('该学校被禁用')
        } else if (children.length && !children[0].isEnabled) {
            updateLoginStatus('error')
            YMessage.error('该教职工账号已停用')
        } else {
            // 获取到路由信息
            // $ 重新刷新用户数据，schoolId为当前选中的id
            const userRes = await http.get('/cloud/user/getCurrentUser')
            store.userInfo = userRes.data
            // $ 记录当前登录平台
            store.loginPlatform = typeStore.platformCode
            store.isCompletedLogin = true
            window.location.href = '/'
        }
        updateLoginStatus('success')
    } catch (error) {
        backLogin()
        updateLoginStatus('error')
    }
}
</script>

<style lang="less" scoped>
.login_box {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    width: 100vw;
    height: 100vh;
    background: url('@/assets/images/admin/login-bg.png') no-repeat center;
    background-size: 100% 100%;
    padding-left: 110px;

    .introduction {
        display: flex;
        display: flex;
        align-items: center;

        img {
        }

        .title {
            font-size: 48px;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.88);
            line-height: 67px;
        }

        .description {
            font-size: 32px;
            font-weight: 400;
            color: #1f1f1f;
            line-height: 45px;
            margin-top: 7px;
        }
    }

    .form_box {
        background: #ffffff;
        width: 380px;
        height: 416px;
        padding: 32px;
        box-shadow: 0px 7px 16px 0px rgba(0, 0, 0, 0.08);
        border-radius: 8px;
        position: absolute;
        top: 50%;
        left: 75%;
        transform: translate3d(-50%, -50%, 0);

        .form_title {
            text-align: left;
            font-size: 22px;
            font-weight: 800;
            margin: 22px 0px 0px 0px;
        }

        .passwordlogin {
            background: url('@/assets/images/admin/login_pc.png');
        }

        .scancodelogin {
            background: url('@/assets/images/admin/login_code.png');
        }

        .scan_code {
            width: 56px;
            height: 56px;
            position: absolute;
            top: 0;
            right: 0;
            cursor: pointer;
            background-repeat: no-repeat;
            background-size: contain;
        }
    }

    .copyright {
        position: absolute;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 14px;
        font-weight: 400;
        color: rgba(102, 102, 102, 0.88);
    }
}
</style>
