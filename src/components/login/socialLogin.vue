<template>
    <a-spin tip="授权中..." class="spin"></a-spin>
</template>

<script setup>
const route = useRoute()
const router = useRouter()
const code = useCode()
const store = useStore()
const typeStore = useTypeStore()

const { token, auth, redirectUrl } = route.query

let _params = {
    token,
    auth,
    grant_type: 'social', // 授权模式，默认-password,dingtalk-客户端模式 ,社会第三方授权模式-social
    client_id: 'yide-library' || 'yide-cloud',
    client_secret: 'yide1234567', // 客户端密钥
}

// 显示区域类型
const SHOW_AREA_MAP = {
    LOGIN: 'login',
    SCHOOL: 'school',
    PASSWORD: 'password',
}

// *********************
// Hooks Function
// *********************

const state = reactive({
    setPassword: {
        isAdmin: false,
        isTips: 'set',
        resetTitle: '设置新密码',
        userPhone: '',
    },
    isScanCode: false, // 是否为扫码登录
    showArea: SHOW_AREA_MAP.LOGIN, // 界面显示的区域
    isLogin: false, // 是否已经登录（登录过后选学校）
    loginText: '登 录', // 按钮文案
    loadingBtn: false, // 按钮加载状态
    schoolList: [], //学校列表
    accessToken: null, // token
})

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

// 登录状态更新
const updateLoginStatus = status => {
    switch (status) {
        case 'success': {
            state.loginText = '登录'
            state.loadingBtn = false
            break
        }
        case 'error': {
            state.loginText = '登录失败'
            state.loadingBtn = false
            store.clearUser()
            setTimeout(() => {
                state.loginText = '登录'
            }, 2000)
            break
        }
        case 'loading': {
            state.loginText = '登录中...'
            state.loadingBtn = true
            break
        }
    }
}

// 获取用户信息
async function getCurrentUser() {
    const res = await http.get('/cloud/user/getCurrentUser')
    if (res.code !== 0) {
        updateLoginStatus('error')
        YMessage.error(res.message)
        return
    }
    const schoolList = res.data.school || []
    state.schoolList = schoolList
    // 设置用户信息
    if (schoolList?.length >= 1) {
        await selectSubmit(schoolList[0])
    } else {
        // 选择学校
        state.showArea = SHOW_AREA_MAP.SCHOOL
    }
}

// 检查用户是否退出
async function checkUserLogout() {
    return new Promise(async resolve => {
        const res = await http.get('/cloud/user/checkUserLogout')
        if (res.code !== 0) {
            updateLoginStatus('error')
            YMessage.error(res.message)
            resolve(false)
            return
        }
        const { isLogout, logoutTime, isInitializePwd } = res.data
        if (isLogout) {
            Modal.confirm({
                title: `提示`,
                icon: createVNode(ExclamationCircleFilled),
                content: `您的账号已于${logoutTime}提交注销申请，需要撤销后才可登录，是否撤销？`,
                okText: '确认',
                cancelText: '取消',
                onCancel() {
                    resolve({
                        isInitializePwd,
                        isLogin: true,
                    })
                },
                onOk() {
                    http.get('/cloud/user/cancelLogout')
                    resolve({
                        isInitializePwd,
                        isLogin: false,
                    })
                },
            })
        } else {
            resolve({
                isInitializePwd,
                isLogin: true,
            })
        }
    })
}

function redirectUrlFn() {
    YMessage.error('授权失败')
    setTimeout(() => {
        if (redirectUrl && redirectUrl !== '') {
            location.replace(redirectUrl)
        } else {
            router.replace({ path: '/login' })
        }
    }, 2000)
}
// 登录按钮
async function submitLogin() {
    try {
        updateLoginStatus('loading')
        // 账号加密
        // const passWadData = {
        //     paramEncipher: RSA.encrypt(JSON.stringify(params)),
        // }
        const res = await http.form('/auth/oauth/token', _params)

        console.log({ res })
        if (res.code !== 0) {
            updateLoginStatus('error')
            YMessage.error(res.message)
            return
        }
        const { accessToken, refreshToken } = res.data
        store.setToken(accessToken)
        if (accessToken) {
            await getCurrentUser(_params)
            updateLoginStatus('success')
        } else {
            redirectUrlFn()
            updateLoginStatus('success')
        }
        // }
    } catch (error) {
        //  // 判断是否是科大讯飞进入  是跳他们的网址
        if (Object.keys(route.query).includes('ifuseriflyssost')) {
            window.location.href = 'https://szjz.jyyun.com'
        } else {
            updateLoginStatus('error')
            router.replace({ path: '/login' })
        }
    }
}

// 选择学校中返回重新登录
function backLogin() {
    state.showArea = SHOW_AREA_MAP.LOGIN
    state.isScanCode = false
}

// 选择学校完按钮
async function selectSubmit(school) {
    try {
        updateLoginStatus('loading')
        const { id, isEnabled, children } = school
        const params = {
            schoolId: id,
            platform: typeStore.platformCode,
        }
        const res = await http.get('/cloud/menu/checkUserLogin', params)
        if (res.code !== 0) {
            updateLoginStatus('error')
            YMessage.error(res.message)
            return
        }
        if (isEnabled !== undefined && !isEnabled) {
            updateLoginStatus('error')
            YMessage.error('该学校被禁用')
        } else if (children.length && !children[0].isEnabled) {
            updateLoginStatus('error')
            YMessage.error('该教职工账号已停用')
        } else {
            // 获取到路由信息
            // $ 重新刷新用户数据，schoolId为当前选中的id
            const userRes = await http.get('/cloud/user/getCurrentUser')
            store.userInfo = userRes.data
            // $ 记录当前登录平台
            store.loginPlatform = typeStore.platformCode
            store.isCompletedLogin = true
            window.location.href = '/'
        }
        updateLoginStatus('success')
    } catch (error) {
        backLogin()
        updateLoginStatus('error')
    }
}

onMounted(() => {
    // 判断是否是科大讯飞进入  是则存储到本地
    if (Object.keys(route.query).includes('ifuseriflyssost')) {
        _params = {
            grant_type: 'iflyssost_library',
            client_id: 'yide-library',
            client_secret: 'yide1234567',
            iflyssost: route.query.iflyssost,
        }
        localStorage.setItem('source', 'IFlytek')
        submitLogin()
        return
    } else {
        localStorage.removeItem('source')
    }
    if (token && auth) {
        submitLogin()
    } else {
        redirectUrlFn()
    }
})
</script>

<style lang="less" scoped>
.spin {
    position: absolute;
    left: 50%;
    top: 40%;
    transform: translate3d(-50%, -40%, 0);
}
</style>
