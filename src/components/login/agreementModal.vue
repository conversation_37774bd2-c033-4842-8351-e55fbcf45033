<template>
    <div>
        <a-modal v-model:open="show" :centered="true" title="一加壹云平台" @cancel="submit" @ok="submit" :width="700">
            <div class="modal_box">
                <ProtocolShLibrary v-if="code === 'shLibrary'" />
                <ProtocolShRFID v-else-if="code === 'shRFID'" />
                <Protocol v-else />
            </div>
        </a-modal>
    </div>
</template>

<script setup>
import Protocol from './protocol.vue'
import ProtocolShLibrary from './protocolShLibrary.vue'
import ProtocolShRFID from './protocolShRFID.vue'
const emit = defineEmits(['handle'])
const src = ref(import.meta.env.VITE_BASE_API_AGREEMENT + '/html/protocol.html')
const code = useCode()
const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
})

const show = computed(() => {
    return props.show
})

function submit() {
    emit('handle')
}
</script>

<style lang="less" scope>
.modal_box {
    height: 700px;
    overflow: auto;
}
</style>
