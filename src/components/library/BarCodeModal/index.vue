<template>
    <div>
        <a-modal v-model:open="show" :centered="true" :width="790" @cancel="cancle" title="条形码查缺" :footer="false">
            <div p-24>
                <a-form :model="form" autocomplete="off">
                    <a-row :gutter="[20]">
                        <a-col :span="17">
                            <a-form-item label="条码号：" class="input_class">
                                <a-input-group compact style="display: flex">
                                    <a-input v-model:value="form.startBarCode" class="left_input" placeholder="起始条码" />
                                    <div class="input_transit">~</div>
                                    <a-input v-model:value="form.endBarCode" class="right_input" placeholder="终止条码" />
                                </a-input-group>
                            </a-form-item>
                        </a-col>
                        <a-col :span="7">
                            <a-button type="primary" @click="queryList">
                                <SearchOutlined />
                                查 询
                            </a-button>
                            <a-button @click="queryReset">
                                <reload-outlined />
                                重 置
                            </a-button>
                        </a-col>
                    </a-row>
                </a-form>
                <a-spin :spinning="spinning">
                    <div class="look_check">
                        <div v-if="list && list.length > 0" class="list" p-20>
                            <div v-for="(item, index) in list" :key="index" mr-10>
                                {{ item }}
                            </div>
                        </div>
                        <div class="no_data" v-else>暂无数据</div>
                    </div>
                </a-spin>
            </div>
        </a-modal>
    </div>
</template>

<script setup>
const emit = defineEmits(['close', 'query', 'reset'])
const form = reactive({})
const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    list: {
        type: Array,
        default: () => [],
    },
    spinning: {
        type: Boolean,
        default: false,
    },
})
const show = computed({
    get: () => {
        return props.show
    },
    set: () => {},
})
const spinning = computed({
    get: () => {
        return props.spinning
    },
    set: () => {},
})
const list = computed({
    get: () => {
        return props.list
    },
    set: () => {},
})
function cancle() {
    emit('close')
}
function queryList() {
    emit('query', form)
}

function queryReset() {
    form.startBarCode = null
    form.endBarCode = null
    emit('reset')
}
</script>

<style lang="less" scoped>
.look_check {
    margin-top: 16px;
    width: 100%;
    min-height: 322px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #dddddd;
    .list {
        max-height: 322px;
        overflow-y: auto;
        display: flex;
        flex-wrap: wrap;
    }
    .no_data {
        width: 100%;
        min-height: 322px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
.input_class {
    :deep(.ant-input-number-focused) {
        border-width: 1px !important;
    }
    .input_transit {
        width: 30px;
        border-left: 0;
        border-top: 1px solid #d9d9d9;
        border-bottom: 1px solid #d9d9d9;
        border-right: 0;
        text-align: center;
        background-color: #fff;
        border-radius: 0px;
        padding: 0px 8px;
        color: #bfbfbf;
        line-height: 30px;
    }
    .left_input {
        flex: 1;
        border-right-width: 0px !important;
        text-align: center;
        border-radius: 6px 0px 0px 6px !important;
    }
    .left_input:hover {
        border-right-width: 1px !important;
    }
    .right_input:hover {
        border-left-width: 1px !important;
    }
    .right_input {
        flex: 1;
        border-radius: 0px 6px 6px 0px !important;
        text-align: center;
        border-left-width: 0;
    }
}
</style>
