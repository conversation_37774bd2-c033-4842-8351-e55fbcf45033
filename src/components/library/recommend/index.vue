<template>
    <div class="recommend">
        <div class="title">图书推荐</div>
        <div class="fun">
            <p>导入数据</p>
            <div>
                <a-button type="primary" @click="setFile">选择文件</a-button>
                <a-button ghost type="primary" style="background: #ebfaf5" href="/template/机构推荐导入模板.xlsx">
                    下载模板
                    <template #icon>
                        <i class="iconfont icon-gw2-Group"></i>
                    </template>
                </a-button>
            </div>
            <span>选择需要导入的Excel文件</span>
        </div>
        <div class="xlsx_class" ml-22 mt-18 v-if="file">
            <div class="left">
                <i class="iconfont icon-gw2-xlsx" mr-8 style="color: #00b781; font-size: 16px"></i>
                <span>{{ file.name }}</span>
            </div>
        </div>
        <div v-if="isProgress" class="upload-info">
            <div mt-20 mr-24 text-right>
                <a @click="exportError">
                    <i class="iconfont icon-gw2-Group font-size-14 color-#19BE8D"></i>
                    <span class="font-size-14 color-#19BE8D">下载异常数据</span>
                </a>
            </div>
            <div class="upload-info-num">
                <p>
                    正常数据条数：
                    <span class="color-#00B781">{{ msg.successCount }}</span>
                    条
                </p>
                <p>
                    异常数据条数：
                    <span class="color-#FE6565">{{ msg.errorCount }}</span>
                    条
                </p>
            </div>
            <div class="upload-info-tip" v-if="msg.errorMsg.length > 0">
                <p>异常提示：</p>
                <ul>
                    <li v-for="(item, index) in msg.errorMsg" :key="index">
                        {{ item }}
                    </li>
                </ul>
            </div>
        </div>
        <div class="footer">
            <a-button type="primary" :loading="uploadLoading" @click="save" :disabled="!file">确定</a-button>
        </div>
        <a-modal v-model:open="open" title="账号密码登录" :width="400">
            <div p-24>
                <a-form :model="formState" ref="formRef">
                    <a-form-item label="账号" name="username" :rules="[{ required: true, message: '请输入账号!' }]">
                        <a-input v-model:value="formState.username" placeholder="请输入" />
                    </a-form-item>
                    <a-form-item mt-12 label="密码" name="password" :rules="[{ required: true, message: '请输入密码!' }]">
                        <a-input v-model:value="formState.password" placeholder="请输入" />
                    </a-form-item>
                </a-form>
            </div>
            <template #footer>
                <a-button type="primary" w-full @click="handleOk">确定</a-button>
            </template>
        </a-modal>
    </div>
</template>
<script setup>
const open = ref(false)
const formState = reactive({
    username: '',
    password: '',
})
const formRef = ref(null)
const progressId = ref(null)
const file = ref(null)
const isProgress = ref(false)
const uploadLoading = ref(false)
const msg = ref({
    errorMsg: [],
    successCount: 0,
    errorCount: 0,
})
const save = () => {
    open.value = true
}
const setFile = () => {
    isProgress.value = false
    file.value = null
    let input = document.createElement('input')
    input.type = 'file'
    input.addEventListener('change', function (e) {
        const fileItem = e.target.files[0]
        file.value = fileItem
        input = null
    })
    input.click()
}

// 获取EXCEL异常数据
const getProgress = async () => {
    uploadLoading.value = true
    const { data } = await http.post('/lib/acquisition/import/progress', { importId: progressId.value })
    if (data.schedule >= 100) {
        isProgress.value = true
        msg.value = data || {}
        uploadLoading.value = false
    } else {
        setTimeout(() => {
            getProgress()
        }, 1000)
    }
}

function exportError() {
    http.download(
        '/lib/common/export/importErrorLogList',
        {
            importIds: [progressId.value],
        },
        '异常数据下载',
    )
}

const handleOk = () => {
    formRef.value.validate().then(() => {
        const obj = {
            libraryId: getUrlParams().LibId,
            username: formState.username,
            password: formState.password,
        }
        uploadLoading.value = true
        http.form('/lib/acquisition/import/organization', { file: file.value, importType: 9, params: JSON.stringify(obj) })
            .then(res => {
                open.value = false
                progressId.value = res.data
                getProgress()
                formState.username = ''
                formState.password = ''
            })
            .catch(err => {
                formState.username = ''
                formState.password = ''
                file.value = null
                open.value = false
                uploadLoading.value = false
            })
    })
}
</script>
<style lang="less" scoped>
.title {
    height: 56px;
    border-bottom: 1px solid #d9d9d9;
    line-height: 56px;
    padding-left: 24px;
    font-size: 16px;
    font-weight: 500;
    color: #000;
}
.xlsx_class {
    width: 490px;
    height: 56px;
    background: #f5f8fd;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    font-weight: 400;
    color: #262626;
    line-height: 20px;
    padding: 0px 12px;
    .left {
        display: flex;
        align-items: center;
    }
}
.upload-info {
    .upload-info-num {
        margin: 0 24px;
        background: #f8f8fa;
        border-radius: 4px;
        padding: 20px;
        line-height: 30px;
        font-size: 14px;
        font-weight: 400;
        color: #2c2c2c;
        margin-top: 12px;
        margin-bottom: 20px;
    }
    .upload-info-tip {
        margin: 0 24px;
        max-height: 358px;
        background: #f8f8fa;
        border-radius: 4px;
        overflow-y: auto;
        padding: 20px;
        margin-bottom: 20px;
        p {
            font-size: 14px;
            font-weight: 400;
            color: #262626;
            padding-bottom: 12px;
        }
        li {
            font-size: 14px;
            padding-bottom: 12px;
            font-weight: 400;
            color: #9ea5c2;
        }
        li:nth-last-child(1) {
            padding-bottom: 0;
        }
    }
}
.fun {
    padding-left: 24px;
    margin-top: 24px;
    p {
        font-size: 16px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        margin-bottom: 16px;
    }
    span {
        margin-top: 16px;
        display: block;
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.3);
    }
}
.footer {
    width: 100%;
    height: 61px;
    background: #ffffff;
    border-radius: 0px 0px 4px 4px;
    position: fixed;
    bottom: 0;
    text-align: right;
    line-height: 61px;
    padding-right: 24px;
    border-top: 1px solid #d9d9d9;
    button {
        margin-right: 24px;
    }
}
</style>
