<template>
    <div>
        <!-- 选择分类号 -->
        <a-modal width="1140px" :centered="true" v-model:open="show" title="分类选择" @cancel="onClose">
            <div p-24>
                <a-form :model="query" autocomplete="off">
                    <a-row :gutter="25">
                        <YCol style="display: flex" :span="6">
                            <a-form-item>
                                <a-select style="width: 100px" v-model:value="state.queryType" :options="queryOptions"></a-select>
                            </a-form-item>
                            <a-form-item v-if="state.queryType === 'number'">
                                <a-input v-model:value="query.code" placeholder="请输入分类号" />
                            </a-form-item>
                            <a-form-item v-else>
                                <a-input v-model:value="query.name" placeholder="请输入分类名" />
                            </a-form-item>
                        </YCol>
                        <YCol :span="5">
                            <a-button type="primary" @click="queryFn">
                                <SearchOutlined />
                                查 询
                            </a-button>
                            <a-button @click="reset">
                                <reload-outlined />
                                重 置
                            </a-button>
                        </YCol>
                    </a-row>
                </a-form>
                <div mt-20 mb-20>
                    <a-breadcrumb separator=">">
                        <a-breadcrumb-item style="cursor: pointer" @click="selectLevel(true)">全部分类</a-breadcrumb-item>
                        <a-breadcrumb-item
                            @click="selectLevel(false, item.id, index)"
                            v-for="(item, index) in state.selectCategoryList"
                            :key="index"
                        >
                            {{ item.name }}
                        </a-breadcrumb-item>
                    </a-breadcrumb>
                </div>
                <div mt-20 mb-20 style="color: rgba(0, 0, 0, 0.85)">已选分类：{{ selectCategoryName }}</div>
                <div max-h-500 mb-20 style="overflow: auto">
                    <ETable
                        hash="SelectCategoryCode"
                        :dataSource="page.list"
                        :row-class-name="(record, index) => (index === state.rowIndex ? 'table_backgroundcolor' : null)"
                        :total="page.total"
                        @paginationChange="paginationChange"
                        :current="query.pageNo"
                    >
                        <a-table-column title="级别码" data-index="levelCode" />
                        <a-table-column title="分类号" data-index="code" :width="100" />
                        <a-table-column title="分类名" data-index="name" />
                        <a-table-column title="级别" data-index="level" :width="100" />
                        <a-table-column title="操作" :width="160" data-index="operate">
                            <template #default="{ record, index }">
                                <div flex>
                                    <a-button style="background: #fb9a0e; color: #fff; border: #fb9a0e" @click="subordinate(record)">
                                        下级
                                    </a-button>
                                    <a-button style="background: #00b781; color: #fff" @click="selectCategory(record, index)">
                                        选择
                                    </a-button>
                                </div>
                            </template>
                        </a-table-column>
                    </ETable>
                </div>
            </div>
            <template #footer>
                <a-space>
                    <a-button @click="onClose">取消</a-button>
                    <a-button type="primary" @click="submit">确定</a-button>
                </a-space>
            </template>
        </a-modal>
    </div>
</template>

<script setup>
const emit = defineEmits(['handle'])
const show = ref(false)

const open = () => {
    show.value = true
    getList()
}

const state = reactive({
    queryType: 'number',
    category: {},
    selectCategoryList: [], // 面包屑
    rowIndex: null,
})
const queryOptions = [
    {
        value: 'number',
        label: '分类号',
    },
    {
        value: 'name',
        label: '分类名',
    },
]
const { query, page, getList, reset, paginationChange } = useList('/lib/category/list')

const selectCategoryName = computed(() => {
    return state.category && state.category.code && state.category.name ? state.category.code + ' ' + state.category.name : '-'
})

function queryFn() {
    state.selectCategoryList = []
    query.id = null
    getList()
}

function selectLevel(isAll, id, level) {
    if (isAll) {
        state.selectCategoryList = []
        query.id = ''
        getList()
    } else {
        const list = []
        state.selectCategoryList.forEach((i, index) => {
            if (level >= index) {
                list.push(i)
            }
        })
        state.selectCategoryList = list
        query.id = id // 分类点击下级的下级ID
        getList()
    }
}

function onClose() {
    state.category = {}
    state.selectCategoryList = []
    query.id = ''
    getList()
    show.value = false
}

function submit() {
    emit('handle', state.category)
    state.category = {}
    state.selectCategoryList = []
    query.id = ''
    getList()
    show.value = false
}

function subordinate({ id, code, name }) {
    query.id = id // 分类点击下级的下级ID
    state.selectCategoryList.push({ id, code, name })
    getList()
}

function selectCategory(item, index) {
    state.rowIndex = index
    state.category = item
}

defineExpose({ open })
</script>

<style lang="less">
.table_backgroundcolor {
    background-color: #eaf9f5;
    .ant-table-cell-row-hover {
        background: #eaf9f5 !important;
    }
}
</style>
