<template>
    <!-- 选择种次号 -->
    <div>
        <a-modal v-model:open="show" :centered="true" :width="408" @cancel="cancle" title="种次号选择" @ok="confirm">
            <div p-20>
                <a-form layout="vertical" :model="form" ref="collectionRef" autocomplete="off">
                    <a-form-item label mb-20>
                        <a-radio-group v-model:value="form.type">
                            <a-radio :value="1">单本种次号：</a-radio>
                            <a-radio :value="2">多卷种次号：</a-radio>
                        </a-radio-group>
                    </a-form-item>
                    <a-form-item v-if="form.type === 1" label="单本种次号：" name="one">
                        <a-input v-model:value="form.oneClassNumber" />
                    </a-form-item>
                    <a-form-item v-else label="多卷种次号：" name="classNumber">
                        <a-input v-model:value="form.doubleClassNumber" />
                    </a-form-item>
                </a-form>
            </div>
        </a-modal>
    </div>
</template>

<script setup>
const emit = defineEmits(['confirm'])
const form = ref({ type: 1 })

const show = ref(null)
const categoryCode = ref(null)

// 获取种次号接口
function getClassNumber() {
    http.post('/lib/catalog/maxClassNumber', { categoryCode: categoryCode.value }).then(res => {
        form.value = { ...res.data, type: 1 }
    })
}

const open = code => {
    show.value = true
    categoryCode.value = code
    getClassNumber()
}

function cancle() {
    show.value = false
}
function confirm() {
    const { oneClassNumber, type, doubleClassNumber } = form.value
    const classNumber = type == 1 ? oneClassNumber : doubleClassNumber
    emit('confirm', classNumber, categoryCode.value)
    show.value = false
}

defineExpose({ open })
</script>

<style lang="scss" scoped></style>
