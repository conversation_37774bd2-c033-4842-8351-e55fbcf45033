<template>
    <div>
        <!-- 种次号查缺 -->
        <a-modal v-model:open="show" :centered="true" :width="790" @cancel="cancle" title="种次号查缺" :footer="false">
            <div p-24>
                <a-form :model="form" autocomplete="off" ref="formRef">
                    <a-row :gutter="[24]">
                        <a-col :span="8">
                            <a-form-item
                                label="分类号："
                                name="categoryCode"
                                autoLink
                                :rules="[{ required: true, message: '请输入分类号' }]"
                            >
                                <a-input v-model:value="form.categoryCode" placeholder="请输入">
                                    <template #suffix>
                                        <SearchOutlined @click="selectCategoryCode" class="color-#d9d9d9" />
                                    </template>
                                </a-input>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-button type="primary" @click="queryList">
                                <SearchOutlined />
                                查 询
                            </a-button>
                            <a-button @click="queryReset">
                                <reload-outlined />
                                重 置
                            </a-button>
                        </a-col>
                    </a-row>
                </a-form>
                <div class="look_check">
                    <div v-if="state.detectionList && state.detectionList.length > 0">
                        <div v-for="(item, index) in state.detectionList" :key="index">
                            {{ item.code }}
                        </div>
                    </div>
                    <div class="no_data" v-else>暂无数据</div>
                </div>
            </div>
        </a-modal>
        <SelectCategoryCode ref="selectCodeRef" @handle="submitCategoryCode" />
    </div>
</template>

<script setup>
const formRef = ref()
const selectCodeRef = ref(null)
const form = reactive({ categoryCode: '' })
const state = reactive({
    detectionList: [],
})

const show = ref(false)
const type = ref(false)

// isJournal 是否为期刊的 默认为false
const open = isJournal => {
    type.value = isJournal || false
    show.value = true
}

function cancle() {
    form.categoryCode = ''
    state.detectionList = []
    show.value = false
}

function queryList() {
    formRef.value.validate().then(() => {
        // type 是否为期刊的 默认为false
        queryDetectionList()
    })
}
function queryReset() {
    form.categoryCode = ''
    state.detectionList = []
}
function selectCategoryCode() {
    selectCodeRef.value.open()
}

function submitCategoryCode({ code }) {
    form.categoryCode = code
}

// 种次号查询事件
function queryDetectionList() {
    // 期刊种次号
    const url = type.value ? '/lib/journal/classNumber/checkLeak' : '/lib/classNumber/checkLeak'
    http.post(url, form).then(res => {
        const continuous = res.data?.continuous?.join('、')
        const nonsequence = res.data?.discontinuous?.join('、')
        const list = []
        list.push({ code: continuous })
        list.push({ code: nonsequence })
        state.detectionList = list
    })
}

defineExpose({ open })
</script>

<style lang="less" scoped>
.look_check {
    margin-top: 16px;
    padding: 20px;
    width: 94%;
    min-height: 322px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #dddddd;
    .no_data {
        width: 100%;
        min-height: 322px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
