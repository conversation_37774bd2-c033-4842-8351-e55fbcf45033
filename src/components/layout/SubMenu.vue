<template>
    <a-sub-menu :key="menuList.name">
        <template #icon v-if="menuList.meta.icon">
            <i class="sider" :style="iconSize" :class="menuList.meta.icon"></i>
        </template>
        <template #title>
            <span>{{ menuList.meta.title }}</span>
        </template>
        <template v-for="item in menuList.children" :key="item.path">
            <template v-if="!item.children">
                <a-menu-item :key="item.name">
                    <template #icon v-if="item.meta.icon">
                        <i class="sider" :class="item.meta.icon" :style="iconSize"></i>
                    </template>
                    {{ item.meta.title }}
                </a-menu-item>
            </template>
            <template v-else>
                <sub-menu :menu-list="item" :key="item.name" />
            </template>
        </template>
    </a-sub-menu>
</template>
<script setup name="sub-menu">
defineProps({
    menuList: {
        type: Object,
        default: () => ({}),
    },
})
const code = useCode()
const iconSize = computed(() => {
    const iconList = ['yd-library']
    console.log(iconList.includes(code))
    return {
        fontSize: iconList.includes(code) ? '16px' : '18px',
    }
})
</script>
<style lang="less" scoped></style>
