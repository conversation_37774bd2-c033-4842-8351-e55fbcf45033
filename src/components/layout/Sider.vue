<template>
    <a-layout-sider :collapsed="collapsed" theme="light" width="208" :trigger="null" collapsible class="scrollbar sider_container">
        <div
            :class="[
                'logo',
                {
                    'flex-justify-center': collapsed,
                    'important-w-80': collapsed,
                },
            ]"
        >
            <i class="iconfont" :class="typeStore?.logo" mr-8></i>
            <span v-if="!collapsed">{{ typeStore?.title }}</span>
        </div>
        <a-menu v-model:selectedKeys="selectedKeys" v-model:openKeys="openKeys" mode="inline" @click="handleClick" class="yd_root_menu">
            <template v-for="item in menuList" :key="item.name">
                <template v-if="!item.children">
                    <a-menu-item :key="item.name">
                        <template #icon v-if="item.meta.icon">
                            <i class="sider" :class="item.meta.icon" :style="iconSize"></i>
                        </template>
                        {{ item.meta.title }}
                    </a-menu-item>
                </template>
                <template v-else>
                    <SubMenu :menu-list="item" />
                </template>
            </template>
        </a-menu>
    </a-layout-sider>
</template>

<script setup>
import SubMenu from './SubMenu.vue'
import routeList from '@/router/getRoute'
import { useRoute, useRouter } from 'vue-router'
defineProps({
    collapsed: {
        type: Boolean,
        default: false,
    },
})
const router = useRouter()
const code = useCode()
const route = useRoute()
const menuList = ref([])
const typeStore = useTypeStore()
const init = async () => {
    let [arr, _] = await routeList()
    arr = arr.sort((i, b) => i.meta.order - b.meta.order)
    menuList.value = arr
}
init()
const openKeys = ref([])
const selectedKeys = ref(route.matched.map(i => i.name))
watch(
    () => route.path,
    () => {
        selectedKeys.value = route.matched.map(i => i.name)
        openKeys.value = route.matched.map(i => i.name)
    },
    { immediate: true },
)

const iconSize = computed(() => {
    const iconList = ['yd-library']
    return {
        fontSize: iconList.includes(code) ? '16px' : '18px',
    }
})

const handleClick = e => {
    router.push({ name: e.key })
}
</script>

<style lang="less" scoped>
.scrollbar {
    &::-webkit-scrollbar {
        width: 0px;
    }
}
.sider_container {
    box-shadow: 1px 0px 4px 0px #eceef1;
    position: relative;
    :deep(.ant-menu-inline),
    :deep(.ant-menu-vertical) {
        border-inline-end: none;
    }
    .logo {
        position: absolute;
        top: 0;
        z-index: 9;
        background-color: #fff;
        width: 208px;
        height: 49px;
        display: flex;
        align-items: center;
        padding-left: 16px;
        color: #2c2c2c;
        font-weight: 600;
        i {
            color: var(--primary-color);
            font-size: 24px;
        }
    }
    .yd_root_menu {
        padding-top: 49px;
        // :deep(.ant-menu-item) {
        //     &:hover {
        //         background-color: #e8f8f3;
        //         color: #19be8d;
        //     }
        // }
        // :deep(.ant-menu-submenu-title) {
        //     &:hover {
        //         background-color: #e8f8f3;
        //         color: #19be8d;
        //     }
        // }
        :deep(.ant-menu-item-selected) {
            // background-color: #e8f8f3;
            // color: #19be8d;
            // @content: 'before';
            &:before {
                content: '';
                width: 4px;
                height: 26px;
                // background-color: #19be8d;
                position: absolute;
                top: 5px;
                left: 0;
                border-radius: 0px 2px 2px 0px;
            }
            & > .ant-menu-submenu-title {
                // color: #19be8d;
            }
            &:active {
                // background-color: #e8f8f3;
            }
            &:hover {
                // background-color: #e8f8f3;
            }
        }
        :deep(.ant-menu-submenu-selected) {
            & > .ant-menu-submenu-title {
                // color: #19be8d;
            }
        }
        :deep(.ant-menu-item-active) {
            // background-color: #e8f8f3;
            // color: #19be8d;
        }
    }
}
</style>
