<template>
    <a-layout-header class="layout_header">
        <div class="header_left">
            <slot name="headerLeft"></slot>
            <a-button type="primary" class="mr-38" shape="round" size="default" @click="router.push('/')">返回{{ backName }}</a-button>
            <div class="btn_item" v-for="item in btnList" :key="item.path" @click="to(item)">
                {{ item.name }}
            </div>
        </div>
        <div class="header_right">
            <div class="header_right_l" v-if="code == 'yd-library' || code == 'shLibrary'">
                <slot name="headerRight">
                    <!-- <span>帮助</span> -->
                    <a-dropdown arrow>
                        <span>技术服务</span>
                        <template #overlay>
                            <a-card :bordered="false">
                                <template #cover>
                                    <div p-6>
                                        <div flex flex-items-center class="color-#595959">
                                            <i mr-4 class="iconfont icon-zhidianlianxi" :style="{ fontSize: '18px' }"></i>
                                            致电联系：
                                        </div>
                                        <div class="color-#000000d9">400-0755-861（售后）</div>
                                    </div>
                                </template>
                            </a-card>
                        </template>
                    </a-dropdown>

                    <!-- <span flex flex-items-center>
                        <a-badge count="5" :offset="[-2, 0]">
                            <svg-icon name="icon-a-icon-news" size="20"></svg-icon>
                        </a-badge>
                    </span> -->
                    <span flex flex-items-center>
                        <a-dropdown arrow>
                            <i class="iconfont icon-fenguan" :style="{ fontSize: '20px', color: '#5B5B5B' }"></i>
                            <template #overlay>
                                <a-menu v-model:selectedKeys="library">
                                    <a-menu-item v-for="item in options" :key="item.id" @click="onChange(item)">
                                        <a href="javascript:;">{{ item.name }}</a>
                                    </a-menu-item>
                                </a-menu>
                            </template>
                        </a-dropdown>
                    </span>

                    <span flex flex-items-center @click="branchLibrary">
                        <SettingOutlined :style="{ fontSize: '20px', color: '#5B5B5B' }" />
                    </span>
                </slot>
            </div>
            <div class="header_right_l" v-if="code == 'yd-xyf'">
                <span flex flex-items-center>
                    <a-dropdown arrow>
                        <SwapOutlined :style="{ fontSize: '20px', color: '#5B5B5B' }" />
                        <template #overlay>
                            <a-menu v-model:selectedKeys="xyfPlatform">
                                <a-menu-item v-for="item in xyfOptions" :key="item.id" @click="changePlatform(item)">
                                    <a href="javascript:;">{{ item.partnerName }}</a>
                                </a-menu-item>
                            </a-menu>
                        </template>
                    </a-dropdown>
                </span>
            </div>
            <a-dropdown arrow>
                <span class="header_right_item">
                    <a-avatar :size="26" v-if="mainStore.userInfo?.avatar" :src="mainStore.userInfo.avatar"></a-avatar>
                    <a-avatar :size="26" v-else src="/avatar.png"></a-avatar>
                    <span pl-4>{{ mainStore.userInfo?.name }}</span>
                </span>
                <template #overlay>
                    <a-menu>
                        <a-menu-item @click="state.changePasswordVisible = true">
                            <template #icon>
                                <i class="iconfont icon-a-icon-tuichu-nor"></i>
                            </template>
                            修改密码
                        </a-menu-item>
                        <a-menu-item @click="handleLoginOut('退出登录')" v-if="state.source !== 'IFlytek'">
                            <template #icon>
                                <i class="iconfont icon-a-icon-tuichu-nor"></i>
                            </template>
                            退出登录
                        </a-menu-item>
                    </a-menu>
                </template>
            </a-dropdown>
            <!-- v-if="code != 'yd-xyf' && code != 'shRFID' && code != 'shLibrary' && state.source !== 'IFlytek'" -->
            <a-button
                v-if="!['yd-xyf', 'shRFID', 'shLibrary'].includes(code) && state.source !== 'IFlytek'"
                class="cloud_platform_btn"
                @click="jumpCouldPlatform"
            >
                前往云平台
            </a-button>
        </div>
        <!-- 修改密码对话框 -->
        <a-modal
            class="updatePassword"
            v-model:open="state.changePasswordVisible"
            title="修改密码"
            :bodyStyle="{ padding: '24px' }"
            :maskClosable="false"
            @cancel="handlerCancel"
            @ok="changePasswordOk"
        >
            <a-form :model="state.changePasswordForm" layout="vertical" ref="changePasswordRef">
                <a-form-item label="旧密码：" name="oldPassword" :rules="[{ required: true, validator: pwdCheck, trigger: 'blur' }]">
                    <a-input-password v-model:value="state.changePasswordForm.oldPassword" placeholder="请输入旧密码" allow-clear />
                </a-form-item>
                <a-form-item
                    label="新密码："
                    name="newPassword"
                    :rules="[{ required: true, validator: verifyNewPassword, trigger: 'blur' }]"
                >
                    <a-input-password v-model:value="state.changePasswordForm.newPassword" placeholder="请输入新密码" allow-clear />
                </a-form-item>
                <a-form-item
                    label="确认密码："
                    name="confirmPassword"
                    :rules="[{ required: true, validator: pwdAgainCheck, trigger: 'blur' }]"
                >
                    <a-input-password v-model:value="state.changePasswordForm.confirmPassword" placeholder="请再次输入新密码" allow-clear />
                    <exclamation-circle-filled />
                    &nbsp;
                    <span>需含字母、数字、特殊字符，长度为8-20个字符的密码</span>
                </a-form-item>
            </a-form>
            <template #footer>
                <a-button key="back" @click="handlerCancel">取消</a-button>
                <a-button key="submit" type="primary" @click="changePasswordOk" :loading="submitLoading">确定</a-button>
            </template>
        </a-modal>
    </a-layout-header>
</template>

<script setup>
import { regularSpecialCharacters } from '@/utils/utils.js'
const state = reactive({
    changePasswordVisible: false,
    submitLoading: false,
    changePasswordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: '',
    },
    source: '', //  来源是否是讯飞
})
const router = useRouter()
const code = useCode()
const yStore = useTypeStore()

const mainStore = useStore()

const backName = computed(() => {
    const item = yStore.tags[0] || {}
    return item.title || '首页'
})

const btnList = computed(() => {
    if (code == 'yd-library') {
        const token = encodeURI(mainStore.token)
        return [
            { name: '图书编目', key: 'lib_catalog' },
            { name: '图书借阅', key: 'borrow_manage' },
            { name: '图书归还', key: 'book_return' },
            { name: '读者办证', key: 'readers_manage' },
            { name: 'OPAC检索', key: 'opac_manage' },
            {
                name: '大数据',
                path: `${import.meta.env.VITE_BASE_SCREEN}/#/bigDataDisplay?libId=${yStore.LibId}`,
                type: 'link',
            },
        ]
    } else {
        return []
    }
})

const xyfOptions = ref([])
const xyfPlatform = ref([])
const options = ref([])
const library = ref([])
const getName = (arr, name) => {
    let item
    arr.forEach(i => {
        if (i.name == name) {
            item = i
        }
        if (i.children?.length > 0) {
            !item && (item = getName(i.children, name))
        }
    })
    return item
}
const to = item => {
    if (item.type == 'link') {
        window.open(item.path)
    } else {
        const routerList = router.getRoutes()
        const route = getName(routerList, item.key == 'book_return' ? 'borrow_manage' : item.key)
        if (route) {
            router.push({
                path: route.path,
                query: item.key == 'book_return' ? { isReturn: true } : {},
            })
        } else {
            YMessage.warning('暂无权限!')
        }
    }
}

const handleLoginOut = val => {
    const loginPlatform = mainStore.loginPlatform
    mainStore.clearUser()
    if (code == 'yd-xyf') {
        yStore.clearUser()
    }
    window.localStorage.clear()
    if (loginPlatform === 'cloud') {
        // 跳转到云平台登录页
        window.location.replace(import.meta.env.VITE_BASE_API_CLOUD + '/#/login')
    } else {
        console.log(code)
        if (code === 'yd-xyf') {
            // 跳转到校易付系统登录页
            window.location.replace('/#/xyfLogin')
        } else if (code == 'shRFID') {
            window.location.replace('/#/shRfidLogin')
        } else if (code == 'shLibrary') {
            window.location.replace('/#/shLibraryLogin')
        } else {
            // 跳转到当前系统登录页
            window.location.replace('/#/login')
        }
    }
}

// 登录平台
async function changePlatform(value) {
    const { platform, id } = value
    const res = await http.get('/campuspay/admin-user/login', { platform, partnerId: id })
    if (res.code != 0) {
        YMessage.error(res.message)
    } else {
        xyfPlatform.value = []
        yStore.setPlatformId(id)
        xyfPlatform.value.push(id)
        http.get('/campuspay/admin-user/loginLog')
        mainStore.userInfo = res.data
        window.location.href = '/'
    }
}

function onChange(value) {
    library.value = []
    yStore.setLibId(value.id, value.name)
    library.value.push(value.id)
    setTimeout(() => {
        window.location.reload()
    }, 100)
}
const branchLibrary = () => {
    router.push('/branchLibrary')
}

function getXyfOptions() {
    xyfPlatform.value = []
    xyfOptions.value = yStore.partnerList
    xyfPlatform.value = [yStore.PartnerId]
}

function getOption() {
    library.value = []
    options.value = yStore.libList
    library.value = [yStore.LibId]
}

// 获取到宿舍大楼
const getDormitoryBuilding = async () => {
    const res = await yStore.getDorms()
    yStore.activeDorm = res.data[0] ?? {}
}

const jumpCouldPlatform = () => {
    toCouldPlatform()
}
// 修改密码对话框
import RSA from '@/utils/rsa.js'
// 修改密码验证
const pwdCheck = (rule, value, callback) => {
    if (value) {
        if (/[\u4E00-\u9FA5]/g.test(value)) {
            return Promise.reject('只可输入字母和数字、不能输入汉字!')
        }
        if (value.length < 6) {
            return Promise.reject('密码不能少于6位！')
        }
        if (value.length > 20) {
            return Promise.reject('密码最长不能超过20位！')
        }
        return Promise.resolve()
    }
    return Promise.reject(new Error('请输入旧密码！'))
}
const verifyNewPassword = (rule, value, callback) => {
    if (!value) {
        return Promise.reject(new Error('请输入新密码'))
    }
    if (!checkField(value, 8, 20) || !regularSpecialCharacters.test(value)) {
        return Promise.reject(new Error('请输入含字母、数字、特殊字符，长度为8-20个字符的密码'))
    }
    return Promise.resolve()
}
// 重复密码验证
const pwdAgainCheck = (rule, value, callback) => {
    if (value) {
        if (state.changePasswordForm.newPassword !== value) {
            return Promise.reject('两次输入密码不一致！')
        }
        return Promise.resolve()
    }
    return Promise.reject(new Error('请输入确认密码'))
}

function clearLocalStorage() {
    for (let i = 0; i < localStorage.length; i++) {
        let key = localStorage.key(i) || ''
        localStorage.removeItem(key)
        if (key.includes('Comp')) {
            continue // 跳过包含"Comp"字符串的key
        }
    }
}
// 清空缓存并退出
const clearExit = () => {
    clearLocalStorage()
    YMessage.success('安全退出')
    // router.push({ path: '/shLibraryLogin' })
    handleLoginOut()
}
const changePasswordRef = ref()

const updatePassword = () => {
    state.submitLoading = true
    const params = {
        oldPwd: state.changePasswordForm.oldPassword,
        newPwd: state.changePasswordForm.newPassword,
        confirmPwd: state.changePasswordForm.confirmPassword,
    }
    const passWadData = {
        paramEncipher: RSA.encrypt(JSON.stringify(params)),
    }
    http.post('/cloud/user/updatePassword', passWadData)
        .then(res => {
            state.changePasswordVisible = false
            changePasswordRef.value.resetFields()
            clearExit()
        })
        .finally(() => {
            state.submitLoading = false
        })
}

const handlerCancel = () => {
    changePasswordRef.value.resetFields()
    state.changePasswordVisible = false
}
const changePasswordOk = () => {
    changePasswordRef.value.validateFields().then(() => {
        updatePassword()
    })
}
onMounted(() => {
    state.source = localStorage.getItem('source') || ''
    const schoolId = mainStore.userInfo?.schoolId
    if (schoolId) {
        mainStore.getCommentKeys(schoolId)
    }

    if (code == 'yd-dormitory') {
        getDormitoryBuilding()
        yStore.getDataDictionary()
    } else if (code == 'yd-library' || code == 'shLibrary') {
        getOption()
    } else if (code == 'yd-xyf') {
        getXyfOptions()
    }
})
</script>

<style lang="less" scoped>
.layout_header {
    background: #fff;
    padding: 0;
    height: 48px;
    line-height: 48px;
    display: flex;
    justify-content: space-between;

    .header_left {
        display: flex;
        align-items: center;

        .btn_item {
            display: inline-block;
            margin-left: 48px;
            cursor: pointer;

            &:hover {
                // color: #00b781;
            }

            &:first-of-type {
                margin-left: 0px;
            }
        }
    }

    .header_right {
        margin-right: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .header_right_l {
            display: flex;
            align-items: center;

            & > span {
                color: #2c2c2c;
                padding-right: 16px;
                margin-right: 16px;
                font-size: 14px;
                cursor: pointer;
                line-height: 18px;
                border-right: 1px solid #999999;

                &:hover {
                    // color: #00b781;
                }
            }
        }

        .header_right_item {
            margin: 0 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
        }

        .header_right_item:hover {
            // color: #19be8d;
        }

        .iconfont {
            font-size: 18px;
        }

        .dormitory-box {
            display: flex;
            align-items: center;
            height: 30px;
            box-sizing: border-box;
            background: #ebfaf5;
            // border: 1px solid #00b781;
            border-radius: 30px;
            padding: 0 14px;

            span {
                font-size: 14px;
                font-weight: 400;
                // color: #00b781;
            }
        }
    }

    .cloud_platform_btn {
        border-radius: 32px;
        // color: rgb(0, 183, 129);
    }
}

.updatePassword {
    .ant-form-item {
        margin-bottom: 12px !important;
    }
}
</style>
