<template>
    <a-layout-content :class="['layout_container', { isFrame }]">
        <a-spin :spinning="mainStore.spinning">
            <router-view v-slot="{ Component, route }">
                <transition enter-active-class="animate__animated animate__fadeIn">
                    <div :class="['scrollbar', { router_view: route.name !== 'home' }]">
                        <KeepAlive :include="include" :max="10">
                            <component :is="Component" :key="route.fullPath" />
                        </KeepAlive>
                    </div>
                </transition>
            </router-view>
        </a-spin>
    </a-layout-content>
</template>
<script setup>
const store = useTypeStore()
const mainStore = useStore()
const props = defineProps({
    isFrame: {
        type: Boolean,
    },
})
// const include = computed(() => {
//     const tags = store.tags.filter(i => i.key != store.exTag)
//     return tags.map(i => i.key)
// })
const include = []

const height = props.isFrame ? '100vh' : 'calc(100vh - 98px)'
</script>
<style lang="less" scoped>
.isFrame {
    margin-left: 0 !important;
    margin-right: 0 !important;
}
.layout_container {
    margin: 0 10px 10px 10px;
    .router_view {
        background-color: #fff;
        height: v-bind('height');
        border-radius: 4px;
    }
}
</style>
