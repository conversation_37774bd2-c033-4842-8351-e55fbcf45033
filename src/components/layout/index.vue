<template>
    <a-layout class="layou_box">
        <sider :collapsed="store.collapsed" v-if="!isFrame"></sider>
        <a-layout class="scrollbar right_container-1" style="width: 100%">
            <Header v-if="!isFrame">
                <template #headerLeft>
                    <div v-if="yStore.platform == 'merchant'">
                        <svg-icon
                            name="icon-zhankaicaidan"
                            v-if="store.collapsed"
                            @click="() => (store.collapsed = !store.collapsed)"
                        ></svg-icon>
                        <svg-icon name="icon-shouqicaidan" v-else @click="() => (store.collapsed = !store.collapsed)"></svg-icon>
                    </div>
                    <div v-else>
                        <svg-icon
                            name="icon-icon_open"
                            v-if="store.collapsed"
                            @click="() => (store.collapsed = !store.collapsed)"
                        ></svg-icon>
                        <svg-icon name="icon-icon_up" v-else @click="() => (store.collapsed = !store.collapsed)"></svg-icon>
                    </div>
                </template>
            </Header>
            <Tags v-if="!isFrame"></Tags>
            <Content :isFrame="isFrame"></Content>
        </a-layout>
    </a-layout>
</template>
<script setup>
const code = useCode()
const yStore = useTypeStore()
const store = useTypeStore()
const mainStore = useStore()
const isFrame = computed(() => window.self !== window.top)
if (!isFrame.value && code !== 'yd-xyf') {
    let url = '/cloud/user/getCurrentUser'
    if (['shLibrary', 'shRFID'].includes(code)) {
        url = '/cloud/user/getLibCurrentUser'
    }
    http.get(url).then(res => {
        mainStore.userInfo = res?.data
    })
}
</script>

<style lang="less" scoped>
.layou_box {
    height: 100vh;

    svg {
        margin-left: 24px;
        margin-right: 16px;
        cursor: pointer;
    }

    .right_container-1 {
        background-color: #f7f8fa;
        width: 100% !important;
    }
}
</style>
