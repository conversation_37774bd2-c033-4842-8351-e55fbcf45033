import { createApp, createVNode } from 'vue'
import { ExclamationCircleFilled } from '@ant-design/icons-vue'
import { Modal } from 'ant-design-vue'

import App from './App.vue'
import 'uno.css'
import '@/assets/css/common.less'
import '@/assets/css/antd.less'
import '@/assets/js/dom-to-image.min.js'
import pinia from '@/store/pinia'
import './yConfig'
import { includeLinkStyle } from 'ydutils'
includeLinkStyle()
const app = createApp(App)
directive(app)
app.use(pinia)

app.config.globalProperties.captchaId = window.btoa('193884511')
app.config.globalProperties.ticket = window.btoa('jPH4ltECNN6xlmAWGvqf34X4X')

const init = async () => {
    import('@/router/index.js').then(async res => {
        const router = await res.default()
        app.use(router).mount('#app')
    })
}
init()

// 监听页面版本更新后 获取使用旧的js文件报错
// window.errorFn = e => {
//     if (window.$t) return false
//     if (e.target.tagName === 'LINK' || e.target.tagName === 'SCRIPT') {
//         window.$t = true
//         Modal.confirm({
//             title: '更新提示',
//             icon: createVNode(ExclamationCircleFilled),
//             content: '版本已更新，请确认更新页面',
//             onOk() {
//                 window.location.reload()
//             },
//             onCancel() {
//                 window.location.reload()
//             },
//         })
//     }
// }
