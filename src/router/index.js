import { createRouter, createWebHashHistory, useRouter } from 'vue-router'
import getRoute from './getRoute'
import config from './config'
const code = useCode()

const getAsRoute = async () => {
    const [routeArr, jump] = await getRoute()
    const router = [
        // {
        //     path: '',
        //     redirect: '/home',
        // },
        {
            component: () => import('@/components/layout/index.vue'),
            name: 'main',
            children: [
                ...routeArr,
                ...jump,
                {
                    path: '/404',
                    name: '404',
                    component: () => import('@/components/common/404.vue'),
                },
                {
                    path: '/no-auth',
                    name: 'no-auth',
                    component: () => import('@/components/common/NoAuth.vue'),
                },
                {
                    path: '/redirect',
                    name: 'redirect',
                    component: () => import('@/components/common/redirect.vue'),
                },
            ],
        },
    ]
    return router
}
const init = async () => {
    let routes = [
        //  社会图书馆frid登录
        {
            path: '/shRfidLogin',
            name: 'shRfidLogin',
            component: () => import('@/components/societyLogin/index.vue'),
        },
        {
            path: '/shLibraryLogin',
            name: 'shLibraryLogin',
            component: () => import('@/components/societyLogin/index.vue'),
        },
        {
            path: '/login',
            name: 'login',
            component: () => import('@/components/login/index.vue'),
        },
        {
            path: '/socialLogin',
            name: 'socialLogin',
            component: () => import('@/components/login/socialLogin.vue'),
        },
        // 校易付登录
        {
            path: '/xyfLogin',
            name: 'xyfLogin',
            component: () => import('@/components/xyfLogin/index.vue'),
        },
        {
            path: '/:pathMatch(.*)',
            redirect: '/404',
        },
    ]
    if (code == 'yd-library' || code == 'shLibrary') {
        routes.push({
            path: '/recommend',
            meta: { title: '图书推荐' },
            component: () => import('@/components/library/recommend/index.vue'),
        })
    }

    if (window.location.hash.indexOf('login') == -1) {
        // debugger
        let arr = await getAsRoute()
        arr = [getFirstPath(arr), ...arr]
        routes = [...routes, ...arr]
    }
    return Promise.resolve(routes)
}

// 获取动态路由第一个路径
function getFirstPath(arr) {
    if (arr.length) {
        if (arr[0]?.children) {
            return getFirstPath(arr[0].children)
        } else {
            return {
                path: '',
                redirect: arr[0].path,
            }
        }
    }
}
const asRouter = async () => {
    return new Promise((resolve, reject) => {
        init().then(res => {
            // debugger
            const router = createRouter({
                history: createWebHashHistory(),
                scrollBehavior() {
                    return { top: 0, left: 0 }
                },
                routes: res,
            })

            config(router)
            return resolve(router)
        })
    })
}

export default asRouter
