const store = useStore()
const modules = import.meta.glob('../pages/**/**/index.vue', { import: 'default' })
const typeStore = useTypeStore()
const params = getUrlParams()
const code = useCode()
if (params.token) {
    window.localStorage.removeItem('layerFrameList')
    window.localStorage.removeItem('saveCollectionForm')
    window.localStorage.removeItem('shelfList')
    store.token = decodeURI(params.token)
    // 云平台跳转过来的
    // 平台标识优先外部传入,不传默认云平台 cloud
    // store.loginPlatform = 'cloud'
    store.loginPlatform = params.sysCode || 'cloud'
    store.isCompletedLogin = true
} else {
    // 在登录过程中，未选择学校也会存token，因此需要清除掉
    if (!store.isCompletedLogin) {
        store.isCompletedLogin = true
        store.clearUser()
    }
}

if (params.sysColor) {
    typeStore.sysColor = params.sysColor
}

let Perms = []
const setRouter = arr => {
    let list = []
    const jump = []
    arr?.forEach(i => {
        let item = {
            path: i.path,
            name: i.component,
            meta: {
                title: i.name,
                icon: i.icon,
            },
            isVisible: i.isVisible,
        }
        if (i.children.length > 0) {
            item.children = setRouter(i.children)[0]
        }
        if (i.filePath) {
            item.component = modules[i.filePath]
        }
        if (i.type == 'C') {
            list.push(item)
        } else if (i.type == 'J') {
            jump.push(item)
        }
    })
    return [list, jump]
}
const setPerms = arr => {
    arr?.forEach(i => {
        if (i.perms) {
            Perms.push(i.perms)
        }
        setPerms(i.btnList)
        setPerms(i.children)
        setPerms(i.tabList)
    })
}
let routerLists = null
const whiteList = ['recommend', 'login', 'xyfLogin', 'shRfidLogin', 'shLibraryLogin']
const isWhite = () => {
    const url = window.location.href
    let flag = false
    whiteList.forEach(i => {
        if (url.indexOf(i) != -1) {
            flag = true
        }
    })
    return flag
}

// 因为yd-permission本身不处理自身的v-auth
// 所以当code命中permission 的时候 需要重新请求项目的Perms 存起来使用 才能生效
const getPermissionPerms = async () => {
    const res = await http.get('/system/menu/getRouters')
    const data = res.code !== 0 ? [] : res?.data
    Perms = []
    setPerms(data)
    store.Perms = Perms
}

export default async () => {
    if (!store.token || isWhite()) {
        return new Promise(r => {
            // window.location.href = '/#/login'
            r([[], []])
        })
    } else {
        return new Promise(r => {
            if (routerLists) {
                r(routerLists)
            } else {
                try {
                    http.get('/system/menu/getRouters').then(async res => {
                        const data = res.code !== 0 ? [] : res?.data
                        const routerList = setRouter(data)
                        setPerms(data)
                        store.Perms = Perms
                        routerLists = routerList
                        if ((code == 'yd-library' || code == 'shLibrary') && res.code === 0) {
                            await http.get('/lib/library/my-libraries').then(async res => {
                                const ids = res.data?.map(i => i.id)
                                const item = res.data[0]
                                if (typeStore.LibId && ids.includes(typeStore.LibId)) {
                                    // typeStore.setLibId(item.id, item.name)
                                } else {
                                    typeStore.setLibId(item.id, item.name)
                                }
                                typeStore.libList = res.data
                            })
                        }
                        if (code == 'yd-xyf' && res.code === 0) {
                            await http.get('/campuspay/admin-user/getUserPartnerList').then(async res => {
                                if (res.data && res.data.length > 0) {
                                    const ids = res.data?.map(i => i.id)
                                    const item = res.data[0]
                                    if (typeStore.PartnerId && ids.includes(typeStore.PartnerId)) {
                                        // typeStore.setLibId(item.id, item.name)
                                    } else {
                                        typeStore.setPlatformId(item.id)
                                    }
                                    typeStore.partnerList = res.data || []
                                } else {
                                    window.location.replace('/#/xyfLogin')
                                }
                            })
                        }
                        // 如果已经进入到permission ,其实它本身不控制按钮的权限,如果获取自己的数据只会是空数组
                        // 那么只获取外部传入的params.sysCode中的数据
                        // 所以可以在这里重新获取一次 只需要覆盖store.Perms, 覆盖了store.Perms 会重新生效
                        if (typeStore.platform === 'permission') {
                            typeStore.platform = params.sysCode
                            await getPermissionPerms()
                        }
                        if (params.sysCode) {
                            // 只有url参数上传参之后修改platform 是为了修改axios请求头参数
                            typeStore.platform = params.sysCode
                        }
                        r(routerList)
                    })
                } catch (error) {
                    r([[], []])
                }
            }
        })
    }
}
