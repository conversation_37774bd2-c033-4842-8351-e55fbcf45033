const store = useStore()
const code = useCode()

export default router => {
    router.beforeEach((to, from, next) => {
        store.spinning = true

        const loginPath = ['/login', '/socialLogin', '/xyfLogin', '/', '/shRfidLogin', '/shLibraryLogin']
        if (loginPath.includes(to.path)) {
            next()
            return
        }
        if (!store.token) {
            let src = '/login'

            if (code == 'yd-xyf') {
                src = '/xyfLogin'
            }
            if (code == 'shRFID') {
                src = '/shRfidLogin'
            }
            if (code == 'shLibrary') {
                src = '/shLibraryLogin'
            }
            next(src)
            return
        }
        next()
    })
    router.afterEach((to, from) => {
        setTimeout(() => {
            store.spinning = false
        }, 300)
    })
}
